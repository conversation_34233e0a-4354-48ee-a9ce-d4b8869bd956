package com.drex.core.api;

import com.drex.core.api.request.ClaimMaizeRequest;
import com.drex.core.api.request.OperateMaizeKernelRequest;
import com.drex.core.api.request.RexyBasketsRequest;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.core.api.response.MaizeKernelDTO;
import com.drex.core.api.response.WalletOperationDTO;
import com.kikitrade.framework.common.model.Response;

/**
 * 恐龙篮子服务接口
 */
public interface RemoteRexyBasketService {

    /**
     * 查询恐龙篮子接口（received 代表篮子中的玉米粒数量）
     */
    Response<CustomerRexyBasketsDTO> getCustomerRexyBaskets(RexyBasketsRequest request);

    /**
     * 收集(增加)玉米粒
     * @return
     */
    Response<MaizeKernelDTO> collectMaizeKernel(OperateMaizeKernelRequest request);

    /**
     * 领取(减少)玉米粒接口
     * @return
     */
    Response<MaizeKernelDTO> claimMaizeKernel(ClaimMaizeRequest request);

    /**
     * 构建领取请求
     * @param customerId
     * @param basketCode
     * @return
     */
    Response<WalletOperationDTO> buildClaimRequests(String customerId, String address, String basketCode);
}
