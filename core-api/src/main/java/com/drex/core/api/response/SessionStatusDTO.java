package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 会话状态DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionStatusDTO implements Serializable {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 会话状态
     */
    private String sessionStatus;

    /**
     * 会话开始时间
     */
    private Long startTime;

    /**
     * 会话结束时间
     */
    private Long endTime;

    /**
     * 会话持续时间（秒）
     */
    private Integer sessionDurationSeconds;

    /**
     * 累计观看时长（秒）
     */
    private Integer totalWatchSeconds;

    /**
     * 有效观看时长（秒）
     */
    private Integer effectiveWatchSeconds;

    /**
     * 当前信任分数
     */
    private Double currentTrustScore;

    /**
     * 最终信任分数
     */
    private Double finalTrustScore;

    /**
     * 是否已授予奖励
     */
    private Boolean rewardGranted;

    /**
     * 拒绝原因
     */
    private String denialReason;

    /**
     * 事件统计
     */
    private EventStatistics eventStatistics;

    /**
     * 欺诈指标摘要
     */
    private List<FraudIndicatorSummary> fraudIndicators;

    /**
     * 会话配置
     */
    private SessionConfiguration sessionConfig;

    /**
     * 事件统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventStatistics implements Serializable {
        /**
         * 总事件数
         */
        private Integer totalEvents;

        /**
         * 播放事件数
         */
        private Integer playEvents;

        /**
         * 暂停事件数
         */
        private Integer pauseEvents;

        /**
         * 跳转事件数
         */
        private Integer seekEvents;

        /**
         * 焦点变化事件数
         */
        private Integer focusChangeEvents;

        /**
         * 用户交互事件数
         */
        private Integer interactionEvents;

        /**
         * 异常事件数
         */
        private Integer anomalousEvents;

        /**
         * 最后事件时间
         */
        private Long lastEventTime;
    }

    /**
     * 欺诈指标摘要
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FraudIndicatorSummary implements Serializable {
        /**
         * 指标类型
         */
        private String indicatorType;

        /**
         * 指标值
         */
        private Double indicatorValue;

        /**
         * 影响分数
         */
        private Double impactScore;

        /**
         * 检测次数
         */
        private Integer detectionCount;

        /**
         * 最后检测时间
         */
        private Long lastDetectionTime;

        /**
         * 风险等级
         */
        private String riskLevel;
    }

    /**
     * 会话配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionConfiguration implements Serializable {
        /**
         * 会话超时时间（分钟）
         */
        private Integer sessionTimeoutMinutes;

        /**
         * 最大事件数
         */
        private Integer maxEventsPerSession;

        /**
         * 信任分数阈值
         */
        private Double trustScoreThreshold;

        /**
         * 最小观看百分比
         */
        private Double minWatchPercentage;

        /**
         * 是否启用欺诈检测
         */
        private Boolean fraudDetectionEnabled;

        /**
         * 是否启用实时分析
         */
        private Boolean realTimeAnalysisEnabled;
    }
}
