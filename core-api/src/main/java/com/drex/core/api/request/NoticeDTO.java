package com.drex.core.api.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class NoticeDTO implements Serializable {

    /** 消息id */
    private String id;

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subTitle;

    /** 通知内容 */
    private String content;

    /** 跳转链接 */
    private String link;

    /** 通知时间 */
    private Long notifyTime;

    /** 通知状态 */
    private String status;

    /** 发送目标 1 所有用户 2 内测用户 */
    private Integer sendTo;
}