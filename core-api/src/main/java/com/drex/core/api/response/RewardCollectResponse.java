package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 奖励收集响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewardCollectResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 交易哈希
     */
    private String transactionHash;

    /**
     * 奖励金额
     */
    private String rewardAmount;

    /**
     * 奖励类型
     */
    private String rewardType;

    /**
     * 奖励阶段
     */
    private String rewardStage;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 交易状态
     */
    private String transactionStatus;

    /**
     * 预估确认时间
     */
    private Long estimatedConfirmationTime;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 奖励详情
     */
    private RewardDetails rewardDetails;

    /**
     * 交易详情
     */
    private TransactionDetails transactionDetails;

    /**
     * 奖励详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardDetails implements Serializable {
        /**
         * 基础奖励
         */
        private String baseReward;

        /**
         * 奖励倍数
         */
        private Double rewardMultiplier;

        /**
         * 信任分数奖励
         */
        private String trustScoreBonus;

        /**
         * 完成度奖励
         */
        private String completionBonus;

        /**
         * 总奖励
         */
        private String totalReward;

        /**
         * 奖励计算详情
         */
        private String calculationDetails;

        /**
         * 奖励发放时间
         */
        private Long rewardGrantTime;
    }

    /**
     * 交易详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransactionDetails implements Serializable {
        /**
         * 区块链网络
         */
        private String network;

        /**
         * 链ID
         */
        private Integer chainId;

        /**
         * Gas费用
         */
        private String gasFee;

        /**
         * Gas价格
         */
        private String gasPrice;

        /**
         * Gas限制
         */
        private String gasLimit;

        /**
         * 交易费用
         */
        private String transactionFee;

        /**
         * 区块号
         */
        private Long blockNumber;

        /**
         * 确认数
         */
        private Integer confirmations;

        /**
         * 交易创建时间
         */
        private Long transactionTime;

        /**
         * 交易确认时间
         */
        private Long confirmationTime;
    }
}
