package com.drex.core.api.common;

public enum CoreResponseCode {
    SUCCESS("0000", "success", true),
    INVALID_PARAMETER("4001", "invalid.parameter"),
    INVALID_BUSINESS_ID("4002", "invalid.business.id"),

    DATA_NOT_FOUND("4010", "data.not.found"),
    DATA_OPERATE_FAIL("4011", "data.operate.fail"),

    REXY_BASKET_FULL("40021", "rexy.basket.full"),
    REXY_BASKET_EMPTY("4022", "rexy.basket.empty"),

    TOO_MANY_REQUEST("4091", "too many request"),


    REQUESTS_FREQUENT("40996", "requests frequent"),
    IP_LIMITED("40997", "ip limited"),
    ADDRESS_LIMITED("40998", "address limited"),
    SYSTEM_ERROR("40999", "system error"),

    // YouTube防刷系统错误码
    YOUTUBE_SESSION_NOT_FOUND("50001", "youtube.session.not.found"),
    YOUTUBE_SESSION_EXPIRED("50002", "youtube.session.expired"),
    YOUTUBE_SESSION_INVALID("50003", "youtube.session.invalid"),
    YOUTUBE_VIDEO_NOT_REWARD("50004", "youtube.video.not.reward"),
    YOUTUBE_REWARD_ALREADY_GRANTED("50005", "youtube.reward.already.granted"),
    YOUTUBE_USER_BLACKLISTED("50006", "youtube.user.blacklisted"),
    YOUTUBE_IP_BLACKLISTED("50007", "youtube.ip.blacklisted"),
    YOUTUBE_FRAUD_DETECTED("50008", "youtube.fraud.detected"),
    YOUTUBE_TRUST_SCORE_LOW("50009", "youtube.trust.score.low"),
    YOUTUBE_INVALID_SIGNATURE("50010", "youtube.invalid.signature"),
    YOUTUBE_EVENT_DATA_INVALID("50011", "youtube.event.data.invalid"),
    YOUTUBE_REWARD_CODE_INVALID("50012", "youtube.reward.code.invalid"),
    YOUTUBE_REWARD_CODE_EXPIRED("50013", "youtube.reward.code.expired"),

    ;

    private final String key;
    private final String code;
    private final boolean success;

    CoreResponseCode(String code, String key, boolean success) {
        this.key = key;
        this.code = code;
        this.success = success;
    }

    CoreResponseCode(String code, String key) {
        this.key = key;
        this.code = code;
        this.success = false;
    }

    public String getKey() {
        return key;
    }

    public String getCode() {
        return code;
    }

    public boolean isSuccess() {
        return success;
    }
}

