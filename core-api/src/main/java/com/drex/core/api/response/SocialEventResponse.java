package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 社交事件响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialEventResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 奖励代码
     */
    private String rewardCode;

    /**
     * 奖励阶段
     */
    private String rewardStage;

    /**
     * 奖励金额
     */
    private String rewardAmount;

    /**
     * 奖励类型
     */
    private String rewardType;

    /**
     * 奖励过期时间
     */
    private Long rewardExpireTime;

    /**
     * 最终风控分数（风控分数越高表示风险越高）
     */
    private Double finalRiskScore;

    /**
     * 验证结果
     */
    private ValidationResult validationResult;

    /**
     * 欺诈检测结果
     */
    private FraudDetectionResult fraudDetectionResult;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 验证结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationResult implements Serializable {
        /**
         * 是否通过业务规则验证
         */
        private Boolean businessRulePassed;

        /**
         * 是否通过欺诈检测
         */
        private Boolean fraudCheckPassed;

        /**
         * 计算的有效观看时长
         */
        private Integer calculatedWatchSeconds;

        /**
         * 观看完成百分比
         */
        private Double watchCompletionPercentage;

        /**
         * 聚焦时间百分比
         */
        private Double focusTimePercentage;

        /**
         * 用户活跃度分数
         */
        private Double userActivityScore;

        /**
         * 播放行为分数
         */
        private Double playbackBehaviorScore;

        /**
         * 验证详情
         */
        private String validationDetails;
    }

    /**
     * 欺诈检测结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FraudDetectionResult implements Serializable {
        /**
         * 总体风险等级
         */
        private String overallRiskLevel;

        /**
         * 欺诈指标列表
         */
        private List<FraudIndicator> fraudIndicators;

        /**
         * 环境风险分析
         */
        private EnvironmentRiskAnalysis environmentRisk;

        /**
         * 行为风险分析
         */
        private BehaviorRiskAnalysis behaviorRisk;

        /**
         * 检测时间
         */
        private Long detectionTime;
    }

    /**
     * 欺诈指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FraudIndicator implements Serializable {
        /**
         * 指标类型
         */
        private String indicatorType;

        /**
         * 指标值
         */
        private Double indicatorValue;

        /**
         * 权重
         */
        private Double weight;

        /**
         * 影响分数
         */
        private Double impactScore;

        /**
         * 描述
         */
        private String description;

        /**
         * 详细信息
         */
        private String details;
    }

    /**
     * 环境风险分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EnvironmentRiskAnalysis implements Serializable {
        /**
         * IP风险等级
         */
        private String ipRiskLevel;

        /**
         * 指纹风险等级
         */
        private String fingerprintRiskLevel;

        /**
         * 地理位置风险等级
         */
        private String geoLocationRiskLevel;

        /**
         * 设备风险等级
         */
        private String deviceRiskLevel;

        /**
         * 环境一致性分数
         */
        private Double environmentConsistencyScore;
    }

    /**
     * 行为风险分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BehaviorRiskAnalysis implements Serializable {
        /**
         * 播放行为风险等级
         */
        private String playbackRiskLevel;

        /**
         * 交互行为风险等级
         */
        private String interactionRiskLevel;

        /**
         * 时间模式风险等级
         */
        private String timePatternRiskLevel;

        /**
         * 行为一致性分数
         */
        private Double behaviorConsistencyScore;

        /**
         * 异常行为计数
         */
        private Integer anomalousBehaviorCount;
    }
}
