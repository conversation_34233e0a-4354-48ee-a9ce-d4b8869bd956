package com.drex.core.api.request;

import com.drex.core.api.common.RexyConstant;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
public class OperateMaizeKernelRequest implements Serializable {

    private String customerId;

    private RexyConstant.RexyBasketsTypeEnum basketType;

    private String businessId;

    private Integer businessCode;

    private BigDecimal amount;

    private RexyConstant.OperateTypeEnum operateType;

    private String transactionHash;
}
