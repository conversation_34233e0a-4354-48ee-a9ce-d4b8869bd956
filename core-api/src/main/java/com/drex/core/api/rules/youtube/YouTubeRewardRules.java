package com.drex.core.api.rules.youtube;

import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.rules.RewardRules;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/25 14:53
 * @description:
 */
@Data
public class YouTubeRewardRules implements RewardRules {

    private Long totalDuration;
    

    @Override
    public String getType() {

        return SocialConstant.PlatformEnum.YouTube.name();
    }
}
