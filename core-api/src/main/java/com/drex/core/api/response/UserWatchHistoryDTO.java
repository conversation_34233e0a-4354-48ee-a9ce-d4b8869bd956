package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用户观看历史DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserWatchHistoryDTO implements Serializable {

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 总观看会话数
     */
    private Integer totalSessions;

    /**
     * 成功完成的会话数
     */
    private Integer completedSessions;

    /**
     * 获得奖励的会话数
     */
    private Integer rewardedSessions;

    /**
     * 被拒绝的会话数
     */
    private Integer rejectedSessions;

    /**
     * 总观看时长（秒）
     */
    private Long totalWatchSeconds;

    /**
     * 平均信任分数
     */
    private Double averageTrustScore;

    /**
     * 最高信任分数
     */
    private Double highestTrustScore;

    /**
     * 最低信任分数
     */
    private Double lowestTrustScore;

    /**
     * 观看会话列表
     */
    private List<WatchSession> watchSessions;

    /**
     * 用户行为统计
     */
    private UserBehaviorStatistics behaviorStatistics;

    /**
     * 观看会话
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WatchSession implements Serializable {
        /**
         * 会话ID
         */
        private String sessionId;

        /**
         * 视频ID
         */
        private String videoId;

        /**
         * 视频标题
         */
        private String videoTitle;

        /**
         * 视频时长
         */
        private Integer videoDurationSeconds;

        /**
         * 观看时长
         */
        private Integer watchSeconds;

        /**
         * 观看百分比
         */
        private Double watchPercentage;

        /**
         * 信任分数
         */
        private Double trustScore;

        /**
         * 会话状态
         */
        private String sessionStatus;

        /**
         * 是否获得奖励
         */
        private Boolean rewardGranted;

        /**
         * 奖励金额
         */
        private String rewardAmount;

        /**
         * 开始时间
         */
        private Long startTime;

        /**
         * 结束时间
         */
        private Long endTime;

        /**
         * 拒绝原因
         */
        private String denialReason;

        /**
         * 风险等级
         */
        private String riskLevel;
    }

    /**
     * 用户行为统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserBehaviorStatistics implements Serializable {
        /**
         * 平均观看完成率
         */
        private Double averageCompletionRate;

        /**
         * 平均暂停次数
         */
        private Double averagePauseCount;

        /**
         * 平均跳转次数
         */
        private Double averageSeekCount;

        /**
         * 平均聚焦百分比
         */
        private Double averageFocusPercentage;

        /**
         * 平均播放速率
         */
        private Double averagePlaybackRate;

        /**
         * 常用设备类型
         */
        private String commonDeviceType;

        /**
         * 常用浏览器
         */
        private String commonBrowser;

        /**
         * 常用IP地址
         */
        private String commonIpAddress;

        /**
         * 观看时间偏好（小时）
         */
        private List<Integer> preferredWatchHours;

        /**
         * 异常行为次数
         */
        private Integer anomalousBehaviorCount;

        /**
         * 欺诈检测触发次数
         */
        private Integer fraudDetectionTriggers;

        /**
         * 账户创建时间
         */
        private Long accountCreationTime;

        /**
         * 最后活动时间
         */
        private Long lastActivityTime;
    }
}
