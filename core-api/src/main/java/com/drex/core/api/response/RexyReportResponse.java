package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 事件上报响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RexyReportResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 处理的事件数量
     */
    private Integer processedEventCount;

    /**
     * 会话状态
     */
    private String sessionStatus;

    /**
     * 当前信任分数
     */
    private Double currentTrustScore;

    /**
     * 累计观看时长
     */
    private Integer totalWatchSeconds;

    /**
     * 有效观看时长
     */
    private Integer effectiveWatchSeconds;

    /**
     * 下次上报间隔（秒）
     */
    private Integer nextReportInterval;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 检测到的异常
     */
    private List<DetectedAnomaly> detectedAnomalies;

    /**
     * 风险警告
     */
    private List<RiskWarning> riskWarnings;

    /**
     * 检测到的异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectedAnomaly implements Serializable {
        /**
         * 异常类型
         */
        private String anomalyType;

        /**
         * 异常描述
         */
        private String description;

        /**
         * 严重程度 (LOW, MEDIUM, HIGH, CRITICAL)
         */
        private String severity;

        /**
         * 影响分数
         */
        private Double impactScore;

        /**
         * 检测时间
         */
        private Long detectionTime;

        /**
         * 相关事件ID
         */
        private List<String> relatedEventIds;
    }

    /**
     * 风险警告
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskWarning implements Serializable {
        /**
         * 警告类型
         */
        private String warningType;

        /**
         * 警告消息
         */
        private String message;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 建议操作
         */
        private String recommendedAction;

        /**
         * 警告时间
         */
        private Long warningTime;
    }
}
