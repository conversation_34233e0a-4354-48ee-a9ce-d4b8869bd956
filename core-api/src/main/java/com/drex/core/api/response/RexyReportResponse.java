package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 事件上报响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RexyReportResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 处理的事件数量
     */
    private Integer processedEventCount;

    /**
     * 会话状态
     */
    private String sessionStatus;

    /**
     * 当前信任分数
     */
    private Double currentTrustScore;

    /**
     * 累计观看时长
     */
    private Integer totalWatchSeconds;

    /**
     * 有效观看时长
     */
    private Integer effectiveWatchSeconds;

    /**
     * 下次上报间隔（秒）
     */
    private Integer nextReportInterval;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 检测到的异常
     */
    private List<DetectedAnomaly> detectedAnomalies;

    /**
     * 风险警告
     */
    private List<RiskWarning> riskWarnings;

    /**
     * 进度信息
     */
    private ProgressInfo progressInfo;

    /**
     * 奖励信息
     */
    private RewardInfo rewardInfo;

    /**
     * 检测到的异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectedAnomaly implements Serializable {
        /**
         * 异常类型
         */
        private String anomalyType;

        /**
         * 异常描述
         */
        private String description;

        /**
         * 严重程度 (LOW, MEDIUM, HIGH, CRITICAL)
         */
        private String severity;

        /**
         * 影响分数
         */
        private Double impactScore;

        /**
         * 检测时间
         */
        private Long detectionTime;

        /**
         * 相关事件ID
         */
        private List<String> relatedEventIds;
    }

    /**
     * 风险警告
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskWarning implements Serializable {
        /**
         * 警告类型
         */
        private String warningType;

        /**
         * 警告消息
         */
        private String message;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 建议操作
         */
        private String recommendedAction;

        /**
         * 警告时间
         */
        private Long warningTime;
    }

    /**
     * 进度信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProgressInfo implements Serializable {
        /**
         * 当前进度等级（1、2、3）
         */
        private Integer progress;

        /**
         * 观看百分比
         */
        private Double watchPercentage;

        /**
         * 是否可以获得奖励
         */
        private Boolean rewardEligible;

        /**
         * 下一个奖励阶段的要求
         */
        private String nextRewardRequirement;

        /**
         * 进度描述
         */
        private String progressDescription;
    }

    /**
     * 奖励信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardInfo implements Serializable {
        /**
         * 奖励代码
         */
        private String rewardCode;

        /**
         * 奖励积分
         */
        private Long rewardScore;

        /**
         * 奖励等级
         */
        private String rewardLevel;

        /**
         * 进度等级
         */
        private Integer progress;

        /**
         * 奖励过期时间
         */
        private Long expirationTime;

        /**
         * 奖励描述
         */
        private String description;

        /**
         * 是否为新奖励
         */
        private Boolean isNewReward;
    }
}
