package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 系统统计信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SystemStatisticsDTO implements Serializable {

    /**
     * 统计时间范围
     */
    private TimeRange timeRange;

    /**
     * 会话统计
     */
    private SessionStatistics sessionStatistics;

    /**
     * 用户统计
     */
    private UserStatistics userStatistics;

    /**
     * 奖励统计
     */
    private RewardStatistics rewardStatistics;

    /**
     * 欺诈检测统计
     */
    private FraudDetectionStatistics fraudDetectionStatistics;

    /**
     * 性能统计
     */
    private PerformanceStatistics performanceStatistics;

    /**
     * 时间范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange implements Serializable {
        /**
         * 开始时间
         */
        private Long startTime;

        /**
         * 结束时间
         */
        private Long endTime;

        /**
         * 时间范围描述
         */
        private String description;
    }

    /**
     * 会话统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionStatistics implements Serializable {
        /**
         * 总会话数
         */
        private Long totalSessions;

        /**
         * 活跃会话数
         */
        private Long activeSessions;

        /**
         * 完成的会话数
         */
        private Long completedSessions;

        /**
         * 失败的会话数
         */
        private Long failedSessions;

        /**
         * 平均会话时长（秒）
         */
        private Double averageSessionDuration;

        /**
         * 平均观看时长（秒）
         */
        private Double averageWatchDuration;

        /**
         * 会话成功率
         */
        private Double sessionSuccessRate;

        /**
         * 按小时分布的会话数
         */
        private Map<Integer, Long> sessionsByHour;

        /**
         * 按状态分布的会话数
         */
        private Map<String, Long> sessionsByStatus;
    }

    /**
     * 用户统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserStatistics implements Serializable {
        /**
         * 总用户数
         */
        private Long totalUsers;

        /**
         * 活跃用户数
         */
        private Long activeUsers;

        /**
         * 新用户数
         */
        private Long newUsers;

        /**
         * 黑名单用户数
         */
        private Long blacklistedUsers;

        /**
         * 平均用户信任分数
         */
        private Double averageUserTrustScore;

        /**
         * 高风险用户数
         */
        private Long highRiskUsers;

        /**
         * 按设备类型分布的用户数
         */
        private Map<String, Long> usersByDeviceType;

        /**
         * 按地理位置分布的用户数
         */
        private Map<String, Long> usersByLocation;
    }

    /**
     * 奖励统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardStatistics implements Serializable {
        /**
         * 总奖励发放次数
         */
        private Long totalRewardsGranted;

        /**
         * 总奖励金额
         */
        private String totalRewardAmount;

        /**
         * 平均奖励金额
         */
        private String averageRewardAmount;

        /**
         * 奖励成功率
         */
        private Double rewardSuccessRate;

        /**
         * 按阶段分布的奖励数
         */
        private Map<String, Long> rewardsByStage;

        /**
         * 按类型分布的奖励数
         */
        private Map<String, Long> rewardsByType;

        /**
         * 拒绝的奖励请求数
         */
        private Long rejectedRewards;

        /**
         * 主要拒绝原因
         */
        private Map<String, Long> rejectionReasons;
    }

    /**
     * 欺诈检测统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FraudDetectionStatistics implements Serializable {
        /**
         * 总检测次数
         */
        private Long totalDetections;

        /**
         * 欺诈检测触发次数
         */
        private Long fraudDetectionTriggers;

        /**
         * 欺诈检测准确率
         */
        private Double fraudDetectionAccuracy;

        /**
         * 误报率
         */
        private Double falsePositiveRate;

        /**
         * 漏报率
         */
        private Double falseNegativeRate;

        /**
         * 按指标类型分布的检测数
         */
        private Map<String, Long> detectionsByIndicatorType;

        /**
         * 按风险等级分布的检测数
         */
        private Map<String, Long> detectionsByRiskLevel;

        /**
         * 平均信任分数
         */
        private Double averageTrustScore;

        /**
         * 信任分数分布
         */
        private Map<String, Long> trustScoreDistribution;
    }

    /**
     * 性能统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformanceStatistics implements Serializable {
        /**
         * 平均响应时间（毫秒）
         */
        private Double averageResponseTime;

        /**
         * 最大响应时间（毫秒）
         */
        private Long maxResponseTime;

        /**
         * 最小响应时间（毫秒）
         */
        private Long minResponseTime;

        /**
         * 请求成功率
         */
        private Double requestSuccessRate;

        /**
         * 错误率
         */
        private Double errorRate;

        /**
         * 吞吐量（请求/秒）
         */
        private Double throughput;

        /**
         * 并发用户数
         */
        private Long concurrentUsers;

        /**
         * 系统负载
         */
        private Double systemLoad;

        /**
         * 内存使用率
         */
        private Double memoryUsage;

        /**
         * CPU使用率
         */
        private Double cpuUsage;

        /**
         * 数据库连接数
         */
        private Long databaseConnections;

        /**
         * Redis连接数
         */
        private Long redisConnections;
    }
}
