package com.drex.core.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 视频会话初始化请求
 * 对应接口1: video/session/init
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoSessionInitRequest implements Serializable {

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 视频ID (YouTube视频ID)
     */
    private String videoId;

    /**
     * 视频URL
     */
    private String videoUrl;

    /**
     * 视频总时长（秒）
     */
    private Integer videoDurationSeconds;

    /**
     * 客户端时间戳
     */
    private Long clientTimestamp;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 浏览器指纹信息
     */
    private BrowserFingerprint browserFingerprint;

    /**
     * 设备信息
     */
    private DeviceInfo deviceInfo;

    /**
     * 浏览器指纹信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BrowserFingerprint implements Serializable {
        /**
         * 指纹哈希值
         */
        private String fingerprintHash;

        /**
         * 屏幕分辨率
         */
        private String screenResolution;

        /**
         * 时区
         */
        private String timezone;

        /**
         * 语言设置
         */
        private String language;

        /**
         * 插件列表
         */
        private String plugins;

        /**
         * Canvas指纹
         */
        private String canvasFingerprint;

        /**
         * WebGL指纹
         */
        private String webglFingerprint;

        /**
         * 字体列表
         */
        private String fonts;

        /**
         * 音频指纹
         */
        private String audioFingerprint;
    }

    /**
     * 设备信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeviceInfo implements Serializable {
        /**
         * 设备类型 (desktop, mobile, tablet)
         */
        private String deviceType;

        /**
         * 操作系统
         */
        private String operatingSystem;

        /**
         * 浏览器名称
         */
        private String browserName;

        /**
         * 浏览器版本
         */
        private String browserVersion;

        /**
         * 是否移动设备
         */
        private Boolean isMobile;

        /**
         * 是否触摸设备
         */
        private Boolean isTouchDevice;
    }
}
