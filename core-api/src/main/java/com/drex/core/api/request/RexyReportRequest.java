package com.drex.core.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 事件上报请求
 * 对应接口2: rexy/report
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RexyReportRequest implements Serializable {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 加密的事件数据包
     */
    private String encryptedData;

    /**
     * 数据签名
     */
    private String signature;

    /**
     * 客户端时间戳
     */
    private Long clientTimestamp;

    /**
     * 事件列表（解密后的数据结构）
     */
    private List<EventData> events;



    /**
     * 事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventData implements Serializable {
        /**
         * 事件ID
         */
        private String eventId;

        /**
         * 事件类型
         */
        private String eventType;

        /**
         * 事件时间戳
         */
        private Long timestamp;

        /**
         * 事件序号
         */
        private Integer sequence;

        /**
         * 事件数据
         */
        private Map<String, Object> data;

        /**
         * 播放相关数据
         */
        private PlaybackData playbackData;

        /**
         * 焦点相关数据
         */
        private FocusData focusData;

    }



    /**
     * 焦点数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FocusData implements Serializable {
        /**
         * 标签页是否活跃
         */
        private Boolean tabActive;

        /**
         * 窗口是否聚焦
         */
        private Boolean windowFocused;

        /**
         * 页面是否可见
         */
        private Boolean pageVisible;

        /**
         * 用户活动状态
         */
        private String userActivityState;

        /**
         * 空闲时间（秒）
         */
        private Integer idleSeconds;

        /**
         * 最后活动时间
         */
        private Long lastActivityTime;
    }


}
