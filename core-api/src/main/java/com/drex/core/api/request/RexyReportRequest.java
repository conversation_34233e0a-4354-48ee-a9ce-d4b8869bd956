package com.drex.core.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 事件上报请求
 * 对应接口2: rexy/report
 * 支持视频播放事件的定时上报功能
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RexyReportRequest implements Serializable {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 加密的事件数据包
     */
    private String encryptedData;

    /**
     * 数据签名
     */
    private String signature;

    /**
     * 客户端时间戳
     */
    private Long clientTimestamp;

    /**
     * 设备指纹
     */
    private String deviceFinger;

    /**
     * 事件列表（解密后的数据结构）
     */
    private List<EventData> events;



    /**
     * 事件数据
     * 支持6种不同类型的事件：PLAYING、PAUSED、SEEK、FOCUS_LOST、FOCUS_GAINED、USER_STATE
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventData implements Serializable {
        /**
         * 事件ID
         */
        private String eventId;

        /**
         * 事件类型
         * 支持的类型：PLAYING、PAUSED、SEEK、FOCUS_LOST、FOCUS_GAINED、USER_STATE
         */
        private String eventType;

        /**
         * 事件时间戳
         */
        private Long timestamp;

        /**
         * 事件序号
         */
        private Integer sequence;

        /**
         * 事件详细数据
         * 根据eventType的不同，包含不同的数据结构
         */
        private EventDetails details;
    }



    /**
     * 事件详细数据
     * 根据eventType的不同，包含不同的数据结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventDetails implements Serializable {
        /**
         * PLAYING状态事件数据
         */
        private PlayingEventData playingData;

        /**
         * PAUSED状态事件数据
         */
        private PausedEventData pausedData;

        /**
         * SEEK跳转事件数据
         */
        private SeekEventData seekData;

        /**
         * FOCUS_LOST失焦事件数据
         */
        private FocusLostEventData focusLostData;

        /**
         * FOCUS_GAINED聚焦事件数据
         */
        private FocusGainedEventData focusGainedData;

        /**
         * USER_STATE用户状态事件数据
         */
        private UserStateEventData userStateData;
    }

    /**
     * PLAYING状态事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlayingEventData implements Serializable {
        /**
         * 新状态
         */
        private String newState;

        /**
         * 当前播放时间（秒）
         */
        private Double currentTime;

        /**
         * 播放速率
         */
        private Double playbackRate;
    }

    /**
     * PAUSED状态事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PausedEventData implements Serializable {
        /**
         * 新状态
         */
        private String newState;

        /**
         * 当前播放时间（秒）
         */
        private Double currentTime;
    }

    /**
     * SEEK跳转事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SeekEventData implements Serializable {
        /**
         * 当前播放时间（秒）
         */
        private Double currentTime;

        /**
         * 之前播放时间（秒）
         */
        private Double previousTime;
    }

    /**
     * FOCUS_LOST失焦事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FocusLostEventData implements Serializable {
        /**
         * 标签页是否活跃
         */
        private Boolean tabActive;

        /**
         * 窗口是否聚焦
         */
        private Boolean windowFocused;
    }

    /**
     * FOCUS_GAINED聚焦事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FocusGainedEventData implements Serializable {
        /**
         * 标签页是否活跃
         */
        private Boolean tabActive;

        /**
         * 窗口是否聚焦
         */
        private Boolean windowFocused;
    }

    /**
     * USER_STATE用户状态事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserStateEventData implements Serializable {
        /**
         * 用户状态：ACTIVE/IDLE/LOCKED
         */
        private String state;
    }
}
