package com.drex.core.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 奖励收集请求
 * 对应接口4: reward/collect
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewardCollectRequest implements Serializable {

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 奖励代码
     */
    private String rewardCode;

    /**
     * 奖励阶段
     */
    private String rewardStage;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 客户端时间戳
     */
    private Long clientTimestamp;

    /**
     * 请求签名
     */
    private String signature;

    /**
     * 钱包地址（用于发放奖励）
     */
    private String walletAddress;

    /**
     * 验证信息
     */
    private VerificationInfo verificationInfo;

    /**
     * 验证信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationInfo implements Serializable {
        /**
         * 最终观看时长
         */
        private Integer finalWatchSeconds;

        /**
         * 最终观看进度
         */
        private Double finalWatchProgress;

        /**
         * 信任分数
         */
        private Double trustScore;

        /**
         * 是否通过验证
         */
        private Boolean verified;

        /**
         * 验证失败原因
         */
        private String failureReason;

        /**
         * 欺诈风险等级
         */
        private String riskLevel;

        /**
         * 验证时间戳
         */
        private Long verificationTimestamp;
    }
}
