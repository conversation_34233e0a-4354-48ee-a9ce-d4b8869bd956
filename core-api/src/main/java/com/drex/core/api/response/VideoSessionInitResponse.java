package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 视频会话初始化响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoSessionInitResponse implements Serializable {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 上报密钥
     */
    private String reportKey;

    /**
     * 会话过期时间
     */
    private Long sessionExpireTime;

    /**
     * 视频分阶段奖励范围
     */
    private List<RewardStageInfo> rewardStages;

    /**
     * 上报配置
     */
    private ReportConfig reportConfig;

    /**
     * 最后一次奖励信息
     */
    private LastRewardInfo lastReward;

    /**
     * 奖励阶段信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardStageInfo implements Serializable {
        /**
         * 阶段名称
         */
        private String stageName;

        /**
         * 阶段代码
         */
        private String stageCode;

        /**
         * 需要观看的百分比
         */
        private Double requiredWatchPercentage;

        /**
         * 需要观看的秒数
         */
        private Integer requiredWatchSeconds;

        /**
         * 奖励金额
         */
        private String rewardAmount;

        /**
         * 奖励类型
         */
        private String rewardType;

        /**
         * 是否已领取
         */
        private Boolean claimed;

        /**
         * 领取时间
         */
        private Long claimTime;
    }

    /**
     * 上报配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReportConfig implements Serializable {
        /**
         * 基础上报间隔（秒）
         */
        private Integer baseReportInterval;

        /**
         * 最大上报间隔（秒）
         */
        private Integer maxReportInterval;

        /**
         * 上报间隔计算公式参数
         */
        private Integer intervalCalculationParam;

        /**
         * 最大事件数量
         */
        private Integer maxEventsPerReport;

        /**
         * 是否启用加密
         */
        private Boolean encryptionEnabled;

        /**
         * 是否启用签名验证
         */
        private Boolean signatureEnabled;

        /**
         * 必须上报的事件类型
         */
        private List<String> requiredEventTypes;

        /**
         * 可选上报的事件类型
         */
        private List<String> optionalEventTypes;
    }

    /**
     * 最后一次奖励信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LastRewardInfo implements Serializable {
        /**
         * 视频ID
         */
        private String videoId;

        /**
         * 最后领取的阶段
         */
        private String lastClaimedStage;

        /**
         * 领取时间
         */
        private Long claimTime;

        /**
         * 奖励金额
         */
        private String rewardAmount;

        /**
         * 信任分数
         */
        private Double trustScore;

        /**
         * 观看时长
         */
        private Integer watchSeconds;
    }
}
