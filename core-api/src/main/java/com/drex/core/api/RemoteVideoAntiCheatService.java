package com.drex.core.api;

import com.drex.core.api.request.RewardCollectRequest;
import com.drex.core.api.request.RexyReportRequest;
import com.drex.core.api.request.SocialEventRequest;
import com.drex.core.api.request.VideoSessionInitRequest;
import com.drex.core.api.response.RewardCollectResponse;
import com.drex.core.api.response.RexyReportResponse;
import com.drex.core.api.response.SocialEventResponse;
import com.drex.core.api.response.VideoSessionInitResponse;
import com.kikitrade.framework.common.model.Response;

/**
 * 视频防刷系统远程服务接口
 * 提供四个核心接口的Dubbo服务定义
 */
public interface RemoteVideoAntiCheatService {

    /**
     * 接口1: 视频会话初始化
     * 路径: video/session/init
     * 
     * 功能：
     * - 创建新的观看会话
     * - 校验用户黑名单和欺诈标记
     * - 检查视频是否为奖励视频
     * - 检查是否已发放过奖励
     * - 返回会话信息和上报配置
     *
     * @param request 会话初始化请求
     * @return 会话初始化响应
     */
    Response<VideoSessionInitResponse> initVideoSession(VideoSessionInitRequest request);

    /**
     * 接口2: 事件数据上报
     * 路径: rexy/report
     * 
     * 功能：
     * - 接收加密的事件数据包
     * - 验证数据签名
     * - 解密并存储事件数据
     * - 实时分析异常行为
     * - 返回处理结果和下次上报间隔
     *
     * @param request 事件上报请求
     * @return 事件上报响应
     */
    Response<RexyReportResponse> reportEvents(RexyReportRequest request);

    /**
     * 接口3: 社交事件处理（获取奖励代码）
     * 路径: task/socialEvent
     * 
     * 功能：
     * - 验证观看进度是否达到奖励阶段
     * - 分析会话事件数据
     * - 执行欺诈检测和信任分数计算
     * - 生成奖励代码（如果通过验证）
     * - 缓存奖励代码供后续领取
     *
     * @param request 社交事件请求
     * @return 社交事件响应
     */
    Response<SocialEventResponse> processSocialEvent(SocialEventRequest request);

    /**
     * 接口4: 奖励收集
     * 路径: reward/collect
     * 
     * 功能：
     * - 验证奖励代码有效性
     * - 检查奖励代码是否过期
     * - 执行最终的安全检查
     * - 发放奖励到用户钱包
     * - 记录奖励发放记录
     *
     * @param request 奖励收集请求
     * @return 奖励收集响应
     */
    Response<RewardCollectResponse> collectReward(RewardCollectRequest request);

    /**
     * 辅助接口: 获取会话状态
     * 
     * @param sessionId 会话ID
     * @return 会话状态信息
     */
    Response<VideoSessionInitResponse.ReportConfig> getSessionStatus(String sessionId);

    /**
     * 辅助接口: 获取用户观看历史
     * 
     * @param customerId 用户ID
     * @param videoId 视频ID（可选）
     * @return 观看历史列表
     */
    Response<Object> getUserWatchHistory(String customerId, String videoId);

    /**
     * 辅助接口: 获取用户信任分数历史
     * 
     * @param customerId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 信任分数历史
     */
    Response<Object> getUserTrustScoreHistory(String customerId, Long startTime, Long endTime);

    /**
     * 管理接口: 更新系统配置
     * 
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否成功
     */
    Response<Boolean> updateSystemConfig(String configKey, String configValue);

    /**
     * 管理接口: 添加用户到黑名单
     * 
     * @param customerId 用户ID
     * @param reason 原因
     * @return 是否成功
     */
    Response<Boolean> addUserToBlacklist(String customerId, String reason);

    /**
     * 管理接口: 从黑名单移除用户
     * 
     * @param customerId 用户ID
     * @return 是否成功
     */
    Response<Boolean> removeUserFromBlacklist(String customerId);

    /**
     * 管理接口: 添加IP到黑名单
     * 
     * @param ipAddress IP地址
     * @param reason 原因
     * @return 是否成功
     */
    Response<Boolean> addIpToBlacklist(String ipAddress, String reason);

    /**
     * 管理接口: 从黑名单移除IP
     * 
     * @param ipAddress IP地址
     * @return 是否成功
     */
    Response<Boolean> removeIpFromBlacklist(String ipAddress);

    /**
     * 监控接口: 获取系统统计信息
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Response<Object> getSystemStatistics(Long startTime, Long endTime);

    /**
     * 监控接口: 获取欺诈检测报告
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 欺诈检测报告
     */
    Response<Object> getFraudDetectionReport(Long startTime, Long endTime);
}
