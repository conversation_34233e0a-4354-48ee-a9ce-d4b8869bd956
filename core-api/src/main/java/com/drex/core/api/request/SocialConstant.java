package com.drex.core.api.request;

import lombok.Data;
import lombok.Getter;

@Data
public class SocialConstant {

    public enum PlatformEnum {
        X,
        Discord,
        YouTube,

        ;
    }

    public enum EventEnum {
        replay,
        watch,

        ;
    }

    public static final String GLOBAL_APPID = "00000";
    public static final String PREFIX_GLOBAL_TASK_CODE = "global_";

    @Getter
    public enum MaizeLevelEnum {
        NORMAL(5),
        GOLD(10),
        ;

        private long score;

        MaizeLevelEnum(long score) {
            this.score = score;
        }
    }

}
