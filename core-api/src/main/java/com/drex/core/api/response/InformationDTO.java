package com.drex.core.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InformationDTO implements Serializable {

    private String id;

    private String type;
    
    private String name;

    private String logo;
    
    private String title;

    private String subTitle;

    private String summary;

    private String content;

    private String image;

    private String link;

    private List<String> category;

    private Boolean isRecommend;

    private Integer sort;

    private String tag;

    private Long date;

    private Long created;

    private Long modified;

    private String organizer;

    private String location;

    private Long activityStartTime;

    private Long activityEndTime;

    private String position;

    private Boolean isReward;

    private String rewardAmount;

    private Long videoDuration;

    private String xMention;

    private String xLength;
}
