package com.drex.core.api.websocket;

import com.drex.core.api.request.RexyReportRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * YouTube WebSocket消息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YouTubeWebSocketMessage implements Serializable {

    /**
     * 消息类型
     */
    private MessageType messageType;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 事件上报数据（客户端发送）
     */
    private RexyReportRequest eventData;

    /**
     * 响应数据（服务端发送）
     */
    private ResponseData responseData;

    /**
     * 错误信息
     */
    private ErrorInfo errorInfo;

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        // 客户端发送的消息类型
        EVENT_REPORT("event_report", "事件上报"),
        HEARTBEAT("heartbeat", "心跳"),
        
        // 服务端发送的消息类型
        EVENT_ACK("event_ack", "事件确认"),
        PROGRESS_UPDATE("progress_update", "进度更新"),
        REWARD_NOTIFICATION("reward_notification", "奖励通知"),
        ERROR("error", "错误"),
        HEARTBEAT_ACK("heartbeat_ack", "心跳确认");

        private final String code;
        private final String description;

        MessageType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }

    /**
     * 响应数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResponseData implements Serializable {
        /**
         * 处理状态
         */
        private String status;

        /**
         * 处理的事件数量
         */
        private Integer processedEventCount;

        /**
         * 当前进度信息
         */
        private ProgressInfo progressInfo;

        /**
         * 奖励信息
         */
        private RewardInfo rewardInfo;

        /**
         * 下次上报间隔（秒）
         */
        private Integer nextReportInterval;

        /**
         * 额外数据
         */
        private Map<String, Object> additionalData;
    }

    /**
     * 进度信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProgressInfo implements Serializable {
        /**
         * 当前进度等级（1、2、3）
         */
        private Integer progress;

        /**
         * 有效观看时长（秒）
         */
        private Integer effectiveWatchSeconds;

        /**
         * 视频总时长（秒）
         */
        private Integer videoDurationSeconds;

        /**
         * 观看百分比
         */
        private Double watchPercentage;

        /**
         * 信任分数
         */
        private Double trustScore;

        /**
         * 是否可以获得奖励
         */
        private Boolean rewardEligible;

        /**
         * 下一个奖励阶段的要求
         */
        private String nextRewardRequirement;
    }

    /**
     * 奖励信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardInfo implements Serializable {
        /**
         * 奖励代码
         */
        private String rewardCode;

        /**
         * 奖励积分
         */
        private Long rewardScore;

        /**
         * 奖励等级
         */
        private String rewardLevel;

        /**
         * 进度等级
         */
        private Integer progress;

        /**
         * 奖励过期时间
         */
        private Long expirationTime;

        /**
         * 奖励描述
         */
        private String description;
    }

    /**
     * 错误信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorInfo implements Serializable {
        /**
         * 错误代码
         */
        private String errorCode;

        /**
         * 错误消息
         */
        private String errorMessage;

        /**
         * 错误详情
         */
        private String errorDetails;

        /**
         * 是否可重试
         */
        private Boolean retryable;
    }
}
