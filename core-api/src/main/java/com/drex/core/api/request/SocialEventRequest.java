package com.drex.core.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 社交事件请求
 * 对应接口3: task/socialEvent
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialEventRequest implements Serializable {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 奖励阶段 (STAGE_1, STAGE_2, STAGE_3)
     */
    private String rewardStage;

    /**
     * 当前观看进度（百分比）
     */
    private Double watchProgress;

    /**
     * 累计观看时长（秒）
     */
    private Integer totalWatchSeconds;

    /**
     * 有效观看时长（秒）
     */
    private Integer effectiveWatchSeconds;

    /**
     * 客户端时间戳
     */
    private Long clientTimestamp;

    /**
     * 请求签名
     */
    private String signature;

    /**
     * 验证数据
     */
    private ValidationData validationData;

    /**
     * 验证数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationData implements Serializable {
        /**
         * 暂停次数
         */
        private Integer pauseCount;

        /**
         * 跳转次数
         */
        private Integer seekCount;

        /**
         * 聚焦时长（秒）
         */
        private Integer focusSeconds;

        /**
         * 失焦时长（秒）
         */
        private Integer blurSeconds;

        /**
         * 空闲时长（秒）
         */
        private Integer idleSeconds;

        /**
         * 播放速率变化次数
         */
        private Integer playbackRateChangeCount;

        /**
         * 平均播放速率
         */
        private Double averagePlaybackRate;

        /**
         * 最大播放速率
         */
        private Double maxPlaybackRate;

        /**
         * 最小播放速率
         */
        private Double minPlaybackRate;

        /**
         * 用户交互次数
         */
        private Integer interactionCount;

        /**
         * 鼠标移动距离
         */
        private Double mouseMoveDistance;

        /**
         * 键盘按键次数
         */
        private Integer keyPressCount;

        /**
         * 滚动次数
         */
        private Integer scrollCount;

        /**
         * 窗口大小变化次数
         */
        private Integer resizeCount;

        /**
         * 全屏切换次数
         */
        private Integer fullscreenToggleCount;

        /**
         * 音量变化次数
         */
        private Integer volumeChangeCount;

        /**
         * 网络状态变化次数
         */
        private Integer networkStateChangeCount;

        /**
         * 缓冲事件次数
         */
        private Integer bufferEventCount;

        /**
         * 错误事件次数
         */
        private Integer errorEventCount;
    }
}
