package com.drex.core.api;

import com.drex.core.api.response.InformationDTO;
import com.kikitrade.framework.common.model.Response;

import java.util.List;

/**
 * 信息服务接口
 */
public interface RemoteInformationService {

    /**
     * 根据ID获取信息
     *
     * @param id 信息ID
     * @return 信息DTO
     */
    Response<InformationDTO> getById(String id);

    /**
     * 保存信息
     *
     * @param informationDTO 信息DTO
     * @return 是否保存成功
     */
    Response<Boolean> saveInformation(InformationDTO informationDTO);

    /**
     * 更新信息
     *
     * @param informationDTO 信息DTO
     * @return 是否更新成功
     */
    Response<Boolean> updateInformation(InformationDTO informationDTO);

    /**
     * 删除信息
     *
     * @param id 信息ID
     * @return 是否删除成功
     */
    Response<Boolean> deleteInformation(String id);

    /**
     * 根据类型获取信息列表
     *
     * @param type 信息类型
     * @return 信息DTO列表
     */
    Response<List<InformationDTO>> getByType(String type);

    /**
     * 获取推荐信息列表
     *
     * @param isRecommend 是否推荐
     * @return 信息DTO列表
     */
    Response<List<InformationDTO>> getByRecommend(Boolean isRecommend, String position);

    /**
     * 根据类型和推荐状态获取信息列表
     *
     * @param type 信息类型
     * @param isRecommend 是否推荐
     * @return 信息DTO列表
     */
    Response<List<InformationDTO>> getByTypeAndRecommend(String type, Boolean isRecommend);

    /**
     * 获取所有信息列表
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 所有信息DTO列表
     */
    Response<List<InformationDTO>> listAll(Integer offset, Integer limit, String position);
}
