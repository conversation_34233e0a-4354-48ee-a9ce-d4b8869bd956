# 中心模块设计文档

## [烟花] 1. 📂 核心内容 [烟花]

_请描述对应的子模块或子系统_

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/68W8g159mU77qEyG/d7a2b33d223d4a7288e44fa6df95a40d0714.png)

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/68W8g159mU77qEyG/d25aec18eb9d4398afd2ca70474781690714.png)

## [烟花] 2. 🔱 技术架构 [烟花]

**架构图**

_此处插入对应的技术架构图，可点击下面的脑图/流程图直接输入_

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/68W8g159mU77qEyG/9f32befd8eb445b2925becddf2c0de270714.png)

## [烟花] 3. 💠 核心流程 [烟花]

### 1、初始化恐龙

rexy\_config(恐龙配置表)

|  字段  |  类型  |  含义  |
| --- | --- | --- |
|  id  |  long  |  恐龙id  |
|  name  |  string  |  恐龙英文code  |
|  level  |  int  |  等级  |
|  rate  |  int  |  生产速率  |
|  limit  |  int  |  篮子上限  |
|  effective\_time  |  long  |  可购买生效时间  |
|   expiration\_time  |  long  |  可购买过期时间  |

customer\_rexy(用户拥有的恐龙)

|  字段  |  类型  |  含义  |
| --- | --- | --- |
|  customer\_id  |  string  |  主键  |
|  rexy\_id  |  string  |  主键  |
|  rexy\_name  |  string  |   |
|  rexy\_level  |  int  |   |
|  rexy\_rate  |  int  |   |
|  rexy\_basket\_limit  |  int  |   |
|  created  |  long  |   |

2、恐龙变更

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/68W8g159mU77qEyG/a9be5ec4555d43a899393f423fbea82a0714.png)

3、产玉米

功能描述：

玉米{在x、youtube页产生的}  ——收集——>  玉米粒「篮子」 ——claim——>  苞米花「积分」

普通玉米棒上有5粒玉米，高品质玉米上有10粒玉米

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/68W8g159mU77qEyG/c366b3be825f4f189a732bcc2c6211b70714.png)

4、产玉米粒 --> 更新篮子

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/68W8g159mU77qEyG/534b73123bee4f50b7bc037e5f2417aa0714.png)

5、claim 篮子

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/68W8g159mU77qEyG/55cf129e3c824a5bafab8b84b39a15400714.png)

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/68W8g159mU77qEyG/1ab2a95b04914c7ba460fdfdb37743c20714.png)

customer\_rexy\_baskets(恐龙的篮子)

|  字段  |  类型  |  含义  |
| --- | --- | --- |
|  customer\_id  |  string  |   |
|  basket\_code  |  string  |  普通篮子：normal，邀请篮子： invite  |
|  received  |  string  |  已收集数量  |
|  basket\_limit  |  int  |  篮子上限  |
|  last\_claim\_time  |  long  |  上一次结算时间  |

rexy\_basket\_record(篮子更新记录)

|  字段  |  类型  |  含义  |
| --- | --- | --- |
|  id  |  string  |  主键  |
|  customer\_id  |  string  |   |
|  basket\_code  |  string  |   |
|  business\_id  |  string  |   |
|  business\_code  |  string  |  task\_code、claim、invite  |
|  number  |  int  |  变化数量  |
|  status  |  int  |  padding、success、fail  |
|  created  |  long  |   |

当前篮子玉米数量： received +  ((now - last\_claim\_time) \*  rexy\_rate/60).intValue()
