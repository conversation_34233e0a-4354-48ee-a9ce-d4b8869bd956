package com.drex.core.web.controller;

import com.drex.core.model.youtube.FraudIndicatorThresholds;
import com.drex.core.service.business.youtube.*;
import com.drex.core.service.cache.model.SessionEvent;
import com.kikitrade.framework.common.model.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 系统测试控制器
 * 用于验证各个服务的完整性和正确性
 */
@Slf4j
@RestController
@RequestMapping("/api/system-test")
@Api(tags = "系统测试")
public class SystemTestController {

    @Autowired
    private DataValidationService dataValidationService;

    @Autowired
    private PlaybackAnalysisService playbackAnalysisService;

    @Autowired
    private FocusAndActivityService focusAndActivityService;

    @Autowired
    private EnvironmentAnalysisService environmentAnalysisService;

    @Autowired
    private TrustScoreCalculationService trustScoreCalculationService;

    @Autowired
    private RewardDecisionService rewardDecisionService;

    @Autowired
    private SystemConfigurationService systemConfigurationService;

    /**
     * 测试欺诈指标阈值计算
     */
    @PostMapping("/test-fraud-indicators")
    @ApiOperation("测试欺诈指标阈值计算")
    public Response<Map<String, Object>> testFraudIndicators() {
        try {
            Map<String, Object> results = new HashMap<>();

            // 测试重复事件计算
            double repeatedEventsScore1 = FraudIndicatorThresholds.calculateRepeatedEventsScore(8.0);
            double repeatedEventsScore2 = FraudIndicatorThresholds.calculateRepeatedEventsScore(3.0);
            results.put("repeatedEvents_8perMin", repeatedEventsScore1); // 应该是0.6
            results.put("repeatedEvents_3perMin", repeatedEventsScore2); // 应该是0.0

            // 测试完成百分比异常计算
            double completionScore1 = FraudIndicatorThresholds.calculateCompletionPercentageAnomalyScore(0.9, 100, 270);
            double completionScore2 = FraudIndicatorThresholds.calculateCompletionPercentageAnomalyScore(0.9, 50, 270);
            results.put("completion_normal", completionScore1); // 应该是0.0
            results.put("completion_suspicious", completionScore2); // 应该是1.0

            // 测试低聚焦分数计算
            double focusScore1 = FraudIndicatorThresholds.calculateLowFocusScore(0.5);
            double focusScore2 = FraudIndicatorThresholds.calculateLowFocusScore(0.8);
            results.put("lowFocus_50percent", focusScore1); // 应该是0.5
            results.put("lowFocus_80percent", focusScore2); // 应该是0.0

            // 测试长时间空闲分数计算
            double idleScore1 = FraudIndicatorThresholds.calculateLongIdleScore(0.4);
            double idleScore2 = FraudIndicatorThresholds.calculateLongIdleScore(0.1);
            results.put("longIdle_40percent", idleScore1); // 应该是0.67
            results.put("longIdle_10percent", idleScore2); // 应该是0.0

            // 测试环境不一致分数计算
            double envScore1 = FraudIndicatorThresholds.calculateEnvironmentInconsistencyScore(4, 3, false);
            double envScore2 = FraudIndicatorThresholds.calculateEnvironmentInconsistencyScore(1, 1, true);
            results.put("environment_4accounts_3changes", envScore1); // 应该是0.67
            results.put("environment_maliciousIP", envScore2); // 应该是1.0

            // 测试时间戳异常分数计算
            double timestampScore1 = FraudIndicatorThresholds.calculateTimestampAnomalyScore(8.0);
            double timestampScore2 = FraudIndicatorThresholds.calculateTimestampAnomalyScore(2.0);
            results.put("timestamp_8seconds", timestampScore1); // 应该是1.0
            results.put("timestamp_2seconds", timestampScore2); // 应该是0.4

            // 测试播放速率分数计算
            double playbackScore1 = FraudIndicatorThresholds.calculateExcessivePlaybackSpeedScore(1.4);
            double playbackScore2 = FraudIndicatorThresholds.calculateExcessivePlaybackSpeedScore(1.1);
            results.put("playbackRate_1.4", playbackScore1); // 应该是0.67
            results.put("playbackRate_1.1", playbackScore2); // 应该是0.0

            // 测试异常跳转分数计算
            double seekScore1 = FraudIndicatorThresholds.calculateAbnormalSeekScore(8.0);
            double seekScore2 = FraudIndicatorThresholds.calculateAbnormalSeekScore(3.0);
            results.put("abnormalSeek_8perMin", seekScore1); // 应该是0.6
            results.put("abnormalSeek_3perMin", seekScore2); // 应该是0.0

            // 测试指纹重复分数计算
            double fingerprintScore1 = FraudIndicatorThresholds.calculateFingerprintDuplicationScore(4);
            double fingerprintScore2 = FraudIndicatorThresholds.calculateFingerprintDuplicationScore(1);
            results.put("fingerprint_4accounts", fingerprintScore1); // 应该是0.67
            results.put("fingerprint_1account", fingerprintScore2); // 应该是0.0

            // 验证阈值配置
            boolean thresholdsValid = FraudIndicatorThresholds.validateThresholds();
            results.put("thresholdsValid", thresholdsValid);

            // 获取阈值描述
            String description = FraudIndicatorThresholds.getThresholdDescription();
            results.put("thresholdDescription", description);

            return Response.success(results);

        } catch (Exception e) {
            log.error("Failed to test fraud indicators", e);
            return Response.fail("TEST_ERROR", "Failed to test fraud indicators", null);
        }
    }

    /**
     * 测试信任分数计算
     */
    @PostMapping("/test-trust-score")
    @ApiOperation("测试信任分数计算")
    public Response<Map<String, Object>> testTrustScore() {
        try {
            Map<String, Object> results = new HashMap<>();

            // 创建测试欺诈指标
            Map<String, Double> fraudIndicators = new HashMap<>();
            fraudIndicators.put("TIMESTAMP_ANOMALY", 0.2);
            fraudIndicators.put("EVENT_ORDER_ANOMALY", 0.0);
            fraudIndicators.put("DUPLICATE_EVENT", 0.1);
            fraudIndicators.put("EXCESSIVE_PLAYBACK_SPEED", 0.3);
            fraudIndicators.put("ABNORMAL_SEEK", 0.0);
            fraudIndicators.put("ABNORMAL_COMPLETION_PERCENTAGE", 0.0);
            fraudIndicators.put("LOW_FOCUS_DURATION", 0.2);
            fraudIndicators.put("LONG_IDLE_DURATION", 0.1);
            fraudIndicators.put("ENVIRONMENT_INCONSISTENCY", 0.4);
            fraudIndicators.put("FINGERPRINT_DUPLICATION", 0.0);
            fraudIndicators.put("MALICIOUS_IP", 0.0);

            // 获取默认权重
            Map<String, Double> weights = trustScoreCalculationService.getDefaultWeights();
            results.put("defaultWeights", weights);

            // 计算信任分数
            TrustScoreCalculationService.TrustScoreResult trustScoreResult = 
                    trustScoreCalculationService.calculateTrustScore(fraudIndicators, weights);
            
            results.put("finalTrustScore", trustScoreResult.getFinalTrustScore());
            results.put("rawScore", trustScoreResult.getRawScore());
            results.put("riskLevel", trustScoreResult.getRiskLevel().getCode());
            results.put("confidenceScore", trustScoreResult.getConfidenceScore());
            results.put("contributionByIndicator", trustScoreResult.getContributionByIndicator());

            // 测试权重验证
            TrustScoreCalculationService.WeightValidationResult validationResult = 
                    trustScoreCalculationService.validateWeights(weights);
            results.put("weightValidation", Map.of(
                    "isValid", validationResult.isValid(),
                    "totalWeight", validationResult.getTotalWeight(),
                    "errors", validationResult.getValidationErrors()
            ));

            // 获取阈值配置
            TrustScoreCalculationService.TrustScoreThresholds thresholds = 
                    trustScoreCalculationService.getThresholds();
            results.put("trustScoreThresholds", Map.of(
                    "lowRiskThreshold", thresholds.getLowRiskThreshold(),
                    "mediumRiskThreshold", thresholds.getMediumRiskThreshold(),
                    "highRiskThreshold", thresholds.getHighRiskThreshold(),
                    "rewardEligibilityThreshold", thresholds.getRewardEligibilityThreshold(),
                    "autoRejectThreshold", thresholds.getAutoRejectThreshold()
            ));

            return Response.success(results);

        } catch (Exception e) {
            log.error("Failed to test trust score calculation", e);
            return Response.fail("TEST_ERROR", "Failed to test trust score calculation", null);
        }
    }

    /**
     * 测试系统配置服务
     */
    @GetMapping("/test-system-config")
    @ApiOperation("测试系统配置服务")
    public Response<Map<String, Object>> testSystemConfig() {
        try {
            Map<String, Object> results = new HashMap<>();

            // 测试获取所有配置
            Map<String, Object> allConfigs = systemConfigurationService.getAllConfigurations();
            results.put("allConfigurations", allConfigs);

            // 测试权重配置
            Map<String, Double> weights = systemConfigurationService.getFraudDetectionWeights();
            results.put("fraudDetectionWeights", weights);

            // 测试阈值配置
            SystemConfigurationService.TrustScoreThresholds thresholds = 
                    systemConfigurationService.getTrustScoreThresholds();
            results.put("trustScoreThresholds", thresholds);

            // 测试奖励配置
            List<SystemConfigurationService.RewardStageConfiguration> rewardConfigs = 
                    systemConfigurationService.getRewardConfigurations();
            results.put("rewardConfigurations", rewardConfigs);

            // 测试系统限制配置
            SystemConfigurationService.SystemLimitsConfiguration limits = 
                    systemConfigurationService.getSystemLimits();
            results.put("systemLimits", limits);

            // 测试配置验证
            SystemConfigurationService.ConfigValidationResult validationResult = 
                    systemConfigurationService.validateConfiguration("test.key", "test.value");
            results.put("configValidation", Map.of(
                    "isValid", validationResult.isValid(),
                    "errorMessage", validationResult.getErrorMessage()
            ));

            return Response.success(results);

        } catch (Exception e) {
            log.error("Failed to test system configuration", e);
            return Response.fail("TEST_ERROR", "Failed to test system configuration", null);
        }
    }

    /**
     * 测试奖励决策服务
     */
    @PostMapping("/test-reward-decision")
    @ApiOperation("测试奖励决策服务")
    public Response<Map<String, Object>> testRewardDecision() {
        try {
            Map<String, Object> results = new HashMap<>();

            // 测试奖励配置获取
            RewardDecisionService.RewardConfiguration config = 
                    rewardDecisionService.getRewardConfiguration("STAGE_1");
            results.put("rewardConfiguration", Map.of(
                    "rewardStage", config.getRewardStage(),
                    "baseAmount", config.getBaseAmount(),
                    "requiredWatchPercentage", config.getRequiredWatchPercentage(),
                    "minTrustScore", config.getMinTrustScore()
            ));

            // 测试奖励金额计算
            RewardDecisionService.RewardAmountCalculationResult amountResult = 
                    rewardDecisionService.calculateRewardAmount("STAGE_1", 0.8, 0.9);
            results.put("rewardAmountCalculation", Map.of(
                    "baseAmount", amountResult.getBaseAmount(),
                    "bonusAmount", amountResult.getBonusAmount(),
                    "totalAmount", amountResult.getTotalAmount(),
                    "multiplier", amountResult.getMultiplier(),
                    "calculationFormula", amountResult.getCalculationFormula()
            ));

            // 测试奖励代码生成
            RewardDecisionService.RewardCodeGenerationResult codeResult = 
                    rewardDecisionService.generateRewardCode("test_user", "test_session", "STAGE_1");
            results.put("rewardCodeGeneration", Map.of(
                    "success", codeResult.isSuccess(),
                    "rewardCode", codeResult.getRewardCode(),
                    "expirationTime", codeResult.getExpirationTime()
            ));

            // 测试奖励代码验证
            if (codeResult.isSuccess()) {
                RewardDecisionService.RewardCodeValidationResult validationResult = 
                        rewardDecisionService.validateRewardCode(
                                codeResult.getRewardCode(), "test_user", "test_session");
                results.put("rewardCodeValidation", Map.of(
                        "valid", validationResult.isValid(),
                        "expired", validationResult.isExpired(),
                        "alreadyUsed", validationResult.isAlreadyUsed()
                ));
            }

            return Response.success(results);

        } catch (Exception e) {
            log.error("Failed to test reward decision", e);
            return Response.fail("TEST_ERROR", "Failed to test reward decision", null);
        }
    }

    /**
     * 系统健康检查
     */
    @GetMapping("/health-check")
    @ApiOperation("系统健康检查")
    public Response<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 检查各个服务是否正常
            health.put("dataValidationService", dataValidationService != null ? "OK" : "FAILED");
            health.put("playbackAnalysisService", playbackAnalysisService != null ? "OK" : "FAILED");
            health.put("focusAndActivityService", focusAndActivityService != null ? "OK" : "FAILED");
            health.put("environmentAnalysisService", environmentAnalysisService != null ? "OK" : "FAILED");
            health.put("trustScoreCalculationService", trustScoreCalculationService != null ? "OK" : "FAILED");
            health.put("rewardDecisionService", rewardDecisionService != null ? "OK" : "FAILED");
            health.put("systemConfigurationService", systemConfigurationService != null ? "OK" : "FAILED");
            
            // 检查欺诈指标阈值配置
            health.put("fraudIndicatorThresholds", FraudIndicatorThresholds.validateThresholds() ? "OK" : "FAILED");
            
            health.put("timestamp", System.currentTimeMillis());
            health.put("status", "YouTube Anti-Cheat System is running");

            return Response.success(health);

        } catch (Exception e) {
            log.error("Health check failed", e);
            return Response.fail("HEALTH_CHECK_ERROR", "Health check failed", null);
        }
    }
}
