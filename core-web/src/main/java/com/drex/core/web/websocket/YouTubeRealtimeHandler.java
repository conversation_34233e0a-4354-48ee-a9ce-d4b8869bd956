package com.drex.core.web.websocket;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.request.RexyReportRequest;
import com.drex.core.api.websocket.YouTubeWebSocketMessage;
import com.drex.core.service.business.youtube.DataValidationService;
import com.drex.core.service.business.youtube.RealtimeRewardService;
import com.drex.core.service.business.youtube.SessionProcessingService;
import com.drex.core.service.business.youtube.util.EventDataProcessor;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * YouTube实时WebSocket处理器
 * 处理视频播放事件的实时上报和奖励发放
 */
@Slf4j
@Component
public class YouTubeRealtimeHandler implements WebSocketHandler {

    @Autowired
    private DataValidationService dataValidationService;

    @Autowired
    private SessionProcessingService sessionProcessingService;

    @Autowired
    private RealtimeRewardService realtimeRewardService;

    @Autowired
    private EventDataProcessor eventDataProcessor;

    // 存储WebSocket会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    // 存储会话信息
    private final Map<String, SessionInfo> sessionInfoMap = new ConcurrentHashMap<>();

    // 心跳检测定时器
    private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(2);

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        
        log.info("WebSocket connection established: {}", sessionId);
        
        // 发送连接确认消息
        YouTubeWebSocketMessage welcomeMessage = YouTubeWebSocketMessage.builder()
                .messageType(YouTubeWebSocketMessage.MessageType.EVENT_ACK)
                .messageId(generateMessageId())
                .timestamp(System.currentTimeMillis())
                .responseData(YouTubeWebSocketMessage.ResponseData.builder()
                        .status("CONNECTED")
                        .nextReportInterval(10) // 默认10秒上报间隔
                        .build())
                .build();
        
        sendMessage(session, welcomeMessage);
        
        // 启动心跳检测
        startHeartbeatCheck(sessionId);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        try {
            String payload = message.getPayload().toString();
            YouTubeWebSocketMessage wsMessage = JSON.parseObject(payload, YouTubeWebSocketMessage.class);
            
            log.debug("Received WebSocket message: type={}, sessionId={}", 
                    wsMessage.getMessageType(), session.getId());

            switch (wsMessage.getMessageType()) {
                case EVENT_REPORT:
                    handleEventReport(session, wsMessage);
                    break;
                case HEARTBEAT:
                    handleHeartbeat(session, wsMessage);
                    break;
                default:
                    log.warn("Unknown message type: {}", wsMessage.getMessageType());
                    break;
            }

        } catch (Exception e) {
            log.error("Failed to handle WebSocket message from session: {}", session.getId(), e);
            sendErrorMessage(session, "MESSAGE_PROCESSING_ERROR", "Failed to process message: " + e.getMessage());
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket transport error for session: {}", session.getId(), exception);
        cleanupSession(session.getId());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.info("WebSocket connection closed: {}, status: {}", session.getId(), closeStatus);
        cleanupSession(session.getId());
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理事件上报
     */
    private void handleEventReport(WebSocketSession session, YouTubeWebSocketMessage wsMessage) {
        try {
            RexyReportRequest eventData = wsMessage.getEventData();
            if (eventData == null) {
                sendErrorMessage(session, "MISSING_EVENT_DATA", "Event data is required");
                return;
            }

            // 1. 数据验证
            DataValidationService.ValidationResult validationResult = 
                    dataValidationService.validateReportRequest(eventData);
            
            if (!validationResult.isValid()) {
                sendErrorMessage(session, "VALIDATION_FAILED", validationResult.getMessage());
                return;
            }

            // 2. 处理事件数据
            List<SessionEvent> sessionEvents = eventDataProcessor.processEventsBatch(eventData.getEvents());
            
            // 3. 更新会话信息
            SessionInfo sessionInfo = updateSessionInfo(session.getId(), eventData, sessionEvents);
            
            // 4. 实时计算奖励
            RealtimeRewardService.RealtimeProcessingResult result = 
                    realtimeRewardService.processRealtimeEvents(
                            eventData.getSessionId(),
                            eventData.getCustomerId(),
                            sessionInfo.getVideoId(),
                            sessionEvents,
                            sessionInfo.getVideoDurationSeconds()
                    );

            // 5. 构建响应消息
            YouTubeWebSocketMessage.ResponseData responseData = YouTubeWebSocketMessage.ResponseData.builder()
                    .status(result.isSuccess() ? "SUCCESS" : "FAILED")
                    .processedEventCount(sessionEvents.size())
                    .progressInfo(result.getProgressInfo())
                    .nextReportInterval(10) // 固定10秒间隔
                    .build();

            YouTubeWebSocketMessage responseMessage = YouTubeWebSocketMessage.builder()
                    .messageType(YouTubeWebSocketMessage.MessageType.PROGRESS_UPDATE)
                    .messageId(generateMessageId())
                    .timestamp(System.currentTimeMillis())
                    .responseData(responseData)
                    .build();

            sendMessage(session, responseMessage);

            // 6. 如果有新奖励，发送奖励通知
            if (result.isHasNewReward() && result.getRewardInfo() != null) {
                YouTubeWebSocketMessage rewardMessage = YouTubeWebSocketMessage.builder()
                        .messageType(YouTubeWebSocketMessage.MessageType.REWARD_NOTIFICATION)
                        .messageId(generateMessageId())
                        .timestamp(System.currentTimeMillis())
                        .responseData(YouTubeWebSocketMessage.ResponseData.builder()
                                .status("REWARD_AVAILABLE")
                                .rewardInfo(result.getRewardInfo())
                                .build())
                        .build();

                sendMessage(session, rewardMessage);
                
                log.info("Sent reward notification to session: {}, reward: {}", 
                        session.getId(), result.getRewardInfo().getRewardCode());
            }

        } catch (Exception e) {
            log.error("Failed to handle event report from session: {}", session.getId(), e);
            sendErrorMessage(session, "EVENT_PROCESSING_ERROR", "Failed to process events: " + e.getMessage());
        }
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(WebSocketSession session, YouTubeWebSocketMessage wsMessage) {
        try {
            // 更新最后心跳时间
            SessionInfo sessionInfo = sessionInfoMap.get(session.getId());
            if (sessionInfo != null) {
                sessionInfo.setLastHeartbeatTime(System.currentTimeMillis());
            }

            // 发送心跳确认
            YouTubeWebSocketMessage heartbeatAck = YouTubeWebSocketMessage.builder()
                    .messageType(YouTubeWebSocketMessage.MessageType.HEARTBEAT_ACK)
                    .messageId(generateMessageId())
                    .timestamp(System.currentTimeMillis())
                    .responseData(YouTubeWebSocketMessage.ResponseData.builder()
                            .status("HEARTBEAT_OK")
                            .build())
                    .build();

            sendMessage(session, heartbeatAck);

        } catch (Exception e) {
            log.error("Failed to handle heartbeat from session: {}", session.getId(), e);
        }
    }

    /**
     * 更新会话信息
     */
    private SessionInfo updateSessionInfo(String wsSessionId, RexyReportRequest eventData, 
                                        List<SessionEvent> sessionEvents) {
        SessionInfo sessionInfo = sessionInfoMap.computeIfAbsent(wsSessionId, k -> new SessionInfo());
        
        sessionInfo.setYoutubeSessionId(eventData.getSessionId());
        sessionInfo.setCustomerId(eventData.getCustomerId());
        sessionInfo.setLastEventTime(System.currentTimeMillis());
        sessionInfo.setLastHeartbeatTime(System.currentTimeMillis());
        
        // 从事件中提取视频信息（这里需要根据实际事件结构调整）
        if (!sessionEvents.isEmpty()) {
            SessionEvent firstEvent = sessionEvents.get(0);
            if (firstEvent.getEventData() != null) {
                // 假设视频ID和时长在事件数据中
                Object videoId = firstEvent.getEventData().get("videoId");
                Object duration = firstEvent.getEventData().get("duration");
                
                if (videoId != null) {
                    sessionInfo.setVideoId(videoId.toString());
                }
                if (duration != null && duration instanceof Number) {
                    sessionInfo.setVideoDurationSeconds(((Number) duration).intValue());
                }
            }
        }
        
        return sessionInfo;
    }

    /**
     * 发送消息
     */
    private void sendMessage(WebSocketSession session, YouTubeWebSocketMessage message) {
        try {
            if (session.isOpen()) {
                String json = JSON.toJSONString(message);
                session.sendMessage(new TextMessage(json));
            }
        } catch (IOException e) {
            log.error("Failed to send WebSocket message to session: {}", session.getId(), e);
        }
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String errorCode, String errorMessage) {
        YouTubeWebSocketMessage errorMsg = YouTubeWebSocketMessage.builder()
                .messageType(YouTubeWebSocketMessage.MessageType.ERROR)
                .messageId(generateMessageId())
                .timestamp(System.currentTimeMillis())
                .errorInfo(YouTubeWebSocketMessage.ErrorInfo.builder()
                        .errorCode(errorCode)
                        .errorMessage(errorMessage)
                        .retryable(false)
                        .build())
                .build();

        sendMessage(session, errorMsg);
    }

    /**
     * 启动心跳检测
     */
    private void startHeartbeatCheck(String sessionId) {
        heartbeatExecutor.scheduleWithFixedDelay(() -> {
            try {
                SessionInfo sessionInfo = sessionInfoMap.get(sessionId);
                WebSocketSession session = sessions.get(sessionId);
                
                if (sessionInfo == null || session == null || !session.isOpen()) {
                    return;
                }
                
                long now = System.currentTimeMillis();
                long lastHeartbeat = sessionInfo.getLastHeartbeatTime();
                
                // 如果超过60秒没有心跳，关闭连接
                if (now - lastHeartbeat > 60000) {
                    log.warn("Session {} heartbeat timeout, closing connection", sessionId);
                    try {
                        session.close(CloseStatus.GOING_AWAY);
                    } catch (IOException e) {
                        log.error("Failed to close session: {}", sessionId, e);
                    }
                    cleanupSession(sessionId);
                }
                
            } catch (Exception e) {
                log.error("Error in heartbeat check for session: {}", sessionId, e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }

    /**
     * 清理会话
     */
    private void cleanupSession(String sessionId) {
        sessions.remove(sessionId);
        sessionInfoMap.remove(sessionId);
        log.debug("Cleaned up session: {}", sessionId);
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 会话信息
     */
    private static class SessionInfo {
        private String youtubeSessionId;
        private String customerId;
        private String videoId;
        private int videoDurationSeconds;
        private long lastEventTime;
        private long lastHeartbeatTime;

        // Getters and setters
        public String getYoutubeSessionId() { return youtubeSessionId; }
        public void setYoutubeSessionId(String youtubeSessionId) { this.youtubeSessionId = youtubeSessionId; }
        public String getCustomerId() { return customerId; }
        public void setCustomerId(String customerId) { this.customerId = customerId; }
        public String getVideoId() { return videoId; }
        public void setVideoId(String videoId) { this.videoId = videoId; }
        public int getVideoDurationSeconds() { return videoDurationSeconds; }
        public void setVideoDurationSeconds(int videoDurationSeconds) { this.videoDurationSeconds = videoDurationSeconds; }
        public long getLastEventTime() { return lastEventTime; }
        public void setLastEventTime(long lastEventTime) { this.lastEventTime = lastEventTime; }
        public long getLastHeartbeatTime() { return lastHeartbeatTime; }
        public void setLastHeartbeatTime(long lastHeartbeatTime) { this.lastHeartbeatTime = lastHeartbeatTime; }
    }
}
