package com.drex.core.web;

import com.drex.core.api.request.RexyConfigDTO;
import com.drex.core.service.business.rexy.CustomerRexyService;
import com.drex.core.service.business.rexy.RexyConfigService;
import com.drex.customer.api.RemoteCustomerService;
import com.drex.customer.api.response.CustomerDTO;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@Slf4j
@RequestMapping("/core")
public class CoreController {

    @Resource
    private RexyConfigService rexyConfigService;
    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @Resource
    private CustomerRexyService customerRexyService;

    @RequestMapping("/hello")
    public String hello() {
        return "Hello, World!";
    }

    /**
     * curl -X POST -H "Content-Type: application/json" -d '{"name":"L0 Default","level":"L0","rate":12,"limit":500, "isDefault":true,"avatar":""}' http://localhost:8080/core/add/rexy
     * @param record
     * @return
     */
    @PostMapping("/add/rexy")
    @ResponseBody
    public String addRexy(@RequestBody RexyConfigDTO record) {
        // Logic to add a record
        return rexyConfigService.saveRexyConfig(record).toString();
    }

    /**
     * curl -X POST "http://localhost:8080/core/updateCustomerLevel?customerId=1919989883007598592&kycLevel=L1"
     * @param customerId
     * @param kycLevel
     * @return
     */
    @PostMapping("/updateCustomerLevel")
    @ResponseBody
    public String updateCustomerLevel(@RequestParam String customerId, @RequestParam String kycLevel) {
        Response<CustomerDTO> response = remoteCustomerService.getById(customerId);
        Response<Boolean> levelRes = remoteCustomerService.updateLevel(customerId, kycLevel);
        if (levelRes.isSuccess()) {
            Boolean rexyRes = customerRexyService.updateCustomerRexyByLevel(response.getData(), kycLevel);
            return "customerLevel change: success, rexy change: " + rexyRes;
        } else {
            return "customerLevel change failed: " + levelRes.getMessage();
        }
    }

}
