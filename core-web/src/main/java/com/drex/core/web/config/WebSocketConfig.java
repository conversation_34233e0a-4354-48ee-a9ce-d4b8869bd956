package com.drex.core.web.config;

import com.drex.core.web.websocket.YouTubeRealtimeHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置类
 * 配置YouTube防刷系统的实时WebSocket通信
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private YouTubeRealtimeHandler youTubeRealtimeHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册YouTube实时上报WebSocket处理器
        registry.addHandler(youTubeRealtimeHandler, "/ws/youtube/realtime")
                .setAllowedOrigins("*") // 生产环境应该限制具体域名
                .withSockJS(); // 支持SockJS降级
    }
}
