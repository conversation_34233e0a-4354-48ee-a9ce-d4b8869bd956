package com.drex.core.web.controller;

import com.drex.core.api.RemoteYouTubeAntiCheatService;
import com.drex.core.api.request.*;
import com.drex.core.api.response.*;
import com.drex.core.service.business.youtube.SystemConfigurationService;
import com.drex.core.service.business.youtube.TrustScoreCalculationService;
import com.kikitrade.framework.common.model.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * YouTube防刷系统Web控制器
 * 提供HTTP接口用于测试和管理
 */
@Slf4j
@RestController
@RequestMapping("/api/youtube-anticheat")
@Api(tags = "YouTube防刷系统")
public class YouTubeAntiCheatController {

    @Autowired
    private RemoteYouTubeAntiCheatService youTubeAntiCheatService;

    @Autowired
    private SystemConfigurationService systemConfigurationService;

    @Autowired
    private TrustScoreCalculationService trustScoreCalculationService;

    /**
     * 初始化视频观看会话
     */
    @PostMapping("/session/init")
    @ApiOperation("初始化视频观看会话")
    public Response<VideoSessionInitResponse> initVideoSession(
            @Valid @RequestBody VideoSessionInitRequest request) {
        try {
            log.info("HTTP: Initializing video session for user: {}, video: {}", 
                    request.getCustomerId(), request.getVideoId());
            return youTubeAntiCheatService.initVideoSession(request);
        } catch (Exception e) {
            log.error("HTTP: Failed to initialize video session", e);
            return Response.fail("INIT_SESSION_ERROR", "Failed to initialize session", null);
        }
    }

    /**
     * 上报事件数据
     */
    @PostMapping("/events/report")
    @ApiOperation("上报事件数据")
    public Response<RexyReportResponse> reportEvents(
            @Valid @RequestBody RexyReportRequest request) {
        try {
            log.info("HTTP: Processing event report for session: {}", request.getSessionId());
            return youTubeAntiCheatService.reportEvents(request);
        } catch (Exception e) {
            log.error("HTTP: Failed to process event report", e);
            return Response.fail("REPORT_EVENTS_ERROR", "Failed to process events", null);
        }
    }

    /**
     * 处理社交事件（获取奖励代码）
     */
    @PostMapping("/social/event")
    @ApiOperation("处理社交事件")
    public Response<SocialEventResponse> processSocialEvent(
            @Valid @RequestBody SocialEventRequest request) {
        try {
            log.info("HTTP: Processing social event for session: {}", request.getSessionId());
            return youTubeAntiCheatService.processSocialEvent(request);
        } catch (Exception e) {
            log.error("HTTP: Failed to process social event", e);
            return Response.fail("SOCIAL_EVENT_ERROR", "Failed to process social event", null);
        }
    }

    /**
     * 收集奖励
     */
    @PostMapping("/reward/collect")
    @ApiOperation("收集奖励")
    public Response<RewardCollectResponse> collectReward(
            @Valid @RequestBody RewardCollectRequest request) {
        try {
            log.info("HTTP: Processing reward collection for code: {}", request.getRewardCode());
            return youTubeAntiCheatService.collectReward(request);
        } catch (Exception e) {
            log.error("HTTP: Failed to collect reward", e);
            return Response.fail("COLLECT_REWARD_ERROR", "Failed to collect reward", null);
        }
    }

    /**
     * 获取会话状态
     */
    @GetMapping("/session/{sessionId}/status")
    @ApiOperation("获取会话状态")
    public Response<VideoSessionInitResponse.ReportConfig> getSessionStatus(
            @ApiParam("会话ID") @PathVariable String sessionId) {
        try {
            return youTubeAntiCheatService.getSessionStatus(sessionId);
        } catch (Exception e) {
            log.error("HTTP: Failed to get session status", e);
            return Response.fail("GET_SESSION_STATUS_ERROR", "Failed to get session status", null);
        }
    }

    /**
     * 获取用户观看历史
     */
    @GetMapping("/user/{customerId}/watch-history")
    @ApiOperation("获取用户观看历史")
    public Response<Object> getUserWatchHistory(
            @ApiParam("用户ID") @PathVariable String customerId,
            @ApiParam("视频ID") @RequestParam(required = false) String videoId) {
        try {
            return youTubeAntiCheatService.getUserWatchHistory(customerId, videoId);
        } catch (Exception e) {
            log.error("HTTP: Failed to get user watch history", e);
            return Response.fail("GET_WATCH_HISTORY_ERROR", "Failed to get watch history", null);
        }
    }

    /**
     * 获取用户信任分数历史
     */
    @GetMapping("/user/{customerId}/trust-score-history")
    @ApiOperation("获取用户信任分数历史")
    public Response<Object> getUserTrustScoreHistory(
            @ApiParam("用户ID") @PathVariable String customerId,
            @ApiParam("开始时间") @RequestParam(required = false) Long startTime,
            @ApiParam("结束时间") @RequestParam(required = false) Long endTime) {
        try {
            return youTubeAntiCheatService.getUserTrustScoreHistory(customerId, startTime, endTime);
        } catch (Exception e) {
            log.error("HTTP: Failed to get trust score history", e);
            return Response.fail("GET_TRUST_SCORE_HISTORY_ERROR", "Failed to get trust score history", null);
        }
    }

    /**
     * 更新系统配置
     */
    @PutMapping("/config/{configKey}")
    @ApiOperation("更新系统配置")
    public Response<Boolean> updateSystemConfig(
            @ApiParam("配置键") @PathVariable String configKey,
            @ApiParam("配置值") @RequestParam String configValue) {
        try {
            return youTubeAntiCheatService.updateSystemConfig(configKey, configValue);
        } catch (Exception e) {
            log.error("HTTP: Failed to update system config", e);
            return Response.fail("UPDATE_CONFIG_ERROR", "Failed to update config", false);
        }
    }

    /**
     * 添加用户到黑名单
     */
    @PostMapping("/blacklist/user/{customerId}")
    @ApiOperation("添加用户到黑名单")
    public Response<Boolean> addUserToBlacklist(
            @ApiParam("用户ID") @PathVariable String customerId,
            @ApiParam("原因") @RequestParam String reason) {
        try {
            return youTubeAntiCheatService.addUserToBlacklist(customerId, reason);
        } catch (Exception e) {
            log.error("HTTP: Failed to add user to blacklist", e);
            return Response.fail("ADD_USER_BLACKLIST_ERROR", "Failed to add user to blacklist", false);
        }
    }

    /**
     * 从黑名单移除用户
     */
    @DeleteMapping("/blacklist/user/{customerId}")
    @ApiOperation("从黑名单移除用户")
    public Response<Boolean> removeUserFromBlacklist(
            @ApiParam("用户ID") @PathVariable String customerId) {
        try {
            return youTubeAntiCheatService.removeUserFromBlacklist(customerId);
        } catch (Exception e) {
            log.error("HTTP: Failed to remove user from blacklist", e);
            return Response.fail("REMOVE_USER_BLACKLIST_ERROR", "Failed to remove user from blacklist", false);
        }
    }

    /**
     * 添加IP到黑名单
     */
    @PostMapping("/blacklist/ip/{ipAddress}")
    @ApiOperation("添加IP到黑名单")
    public Response<Boolean> addIpToBlacklist(
            @ApiParam("IP地址") @PathVariable String ipAddress,
            @ApiParam("原因") @RequestParam String reason) {
        try {
            return youTubeAntiCheatService.addIpToBlacklist(ipAddress, reason);
        } catch (Exception e) {
            log.error("HTTP: Failed to add IP to blacklist", e);
            return Response.fail("ADD_IP_BLACKLIST_ERROR", "Failed to add IP to blacklist", false);
        }
    }

    /**
     * 从黑名单移除IP
     */
    @DeleteMapping("/blacklist/ip/{ipAddress}")
    @ApiOperation("从黑名单移除IP")
    public Response<Boolean> removeIpFromBlacklist(
            @ApiParam("IP地址") @PathVariable String ipAddress) {
        try {
            return youTubeAntiCheatService.removeIpFromBlacklist(ipAddress);
        } catch (Exception e) {
            log.error("HTTP: Failed to remove IP from blacklist", e);
            return Response.fail("REMOVE_IP_BLACKLIST_ERROR", "Failed to remove IP from blacklist", false);
        }
    }

    /**
     * 获取系统统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取系统统计信息")
    public Response<Object> getSystemStatistics(
            @ApiParam("开始时间") @RequestParam(required = false) Long startTime,
            @ApiParam("结束时间") @RequestParam(required = false) Long endTime) {
        try {
            return youTubeAntiCheatService.getSystemStatistics(startTime, endTime);
        } catch (Exception e) {
            log.error("HTTP: Failed to get system statistics", e);
            return Response.fail("GET_STATISTICS_ERROR", "Failed to get statistics", null);
        }
    }

    /**
     * 获取欺诈检测报告
     */
    @GetMapping("/fraud-report")
    @ApiOperation("获取欺诈检测报告")
    public Response<Object> getFraudDetectionReport(
            @ApiParam("开始时间") @RequestParam(required = false) Long startTime,
            @ApiParam("结束时间") @RequestParam(required = false) Long endTime) {
        try {
            return youTubeAntiCheatService.getFraudDetectionReport(startTime, endTime);
        } catch (Exception e) {
            log.error("HTTP: Failed to get fraud detection report", e);
            return Response.fail("GET_FRAUD_REPORT_ERROR", "Failed to get fraud report", null);
        }
    }

    /**
     * 获取系统配置
     */
    @GetMapping("/config")
    @ApiOperation("获取系统配置")
    public Response<Map<String, Object>> getSystemConfiguration() {
        try {
            Map<String, Object> config = systemConfigurationService.getAllConfigurations();
            return Response.success(config);
        } catch (Exception e) {
            log.error("HTTP: Failed to get system configuration", e);
            return Response.fail("GET_CONFIG_ERROR", "Failed to get configuration", null);
        }
    }

    /**
     * 获取信任分数阈值
     */
    @GetMapping("/trust-score/thresholds")
    @ApiOperation("获取信任分数阈值")
    public Response<TrustScoreCalculationService.TrustScoreThresholds> getTrustScoreThresholds() {
        try {
            TrustScoreCalculationService.TrustScoreThresholds thresholds = 
                    trustScoreCalculationService.getThresholds();
            return Response.success(thresholds);
        } catch (Exception e) {
            log.error("HTTP: Failed to get trust score thresholds", e);
            return Response.fail("GET_THRESHOLDS_ERROR", "Failed to get thresholds", null);
        }
    }

    /**
     * 更新欺诈检测权重
     */
    @PutMapping("/fraud-weights")
    @ApiOperation("更新欺诈检测权重")
    public Response<Boolean> updateFraudDetectionWeights(
            @RequestBody Map<String, Double> weights) {
        try {
            boolean success = trustScoreCalculationService.updateWeights(weights);
            return Response.success(success);
        } catch (Exception e) {
            log.error("HTTP: Failed to update fraud detection weights", e);
            return Response.fail("UPDATE_WEIGHTS_ERROR", "Failed to update weights", false);
        }
    }

    /**
     * 获取欺诈检测权重
     */
    @GetMapping("/fraud-weights")
    @ApiOperation("获取欺诈检测权重")
    public Response<Map<String, Double>> getFraudDetectionWeights() {
        try {
            Map<String, Double> weights = trustScoreCalculationService.getDefaultWeights();
            return Response.success(weights);
        } catch (Exception e) {
            log.error("HTTP: Failed to get fraud detection weights", e);
            return Response.fail("GET_WEIGHTS_ERROR", "Failed to get weights", null);
        }
    }

    /**
     * 验证权重配置
     */
    @PostMapping("/fraud-weights/validate")
    @ApiOperation("验证权重配置")
    public Response<TrustScoreCalculationService.WeightValidationResult> validateWeights(
            @RequestBody Map<String, Double> weights) {
        try {
            TrustScoreCalculationService.WeightValidationResult result = 
                    trustScoreCalculationService.validateWeights(weights);
            return Response.success(result);
        } catch (Exception e) {
            log.error("HTTP: Failed to validate weights", e);
            return Response.fail("VALIDATE_WEIGHTS_ERROR", "Failed to validate weights", null);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @ApiOperation("健康检查")
    public Response<String> healthCheck() {
        return Response.success("YouTube Anti-Cheat System is running");
    }
}
