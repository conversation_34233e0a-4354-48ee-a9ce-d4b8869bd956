package com.drex.core;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootConfiguration
@SpringBootApplication(scanBasePackages = {"com.drex"})
@EnableDubbo
public class DrexCoreApplication {

    public static void main(String[] args) {
        SpringApplication.run(DrexCoreApplication.class, args);
    }
}
