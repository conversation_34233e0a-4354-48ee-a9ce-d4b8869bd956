# YouTube WebSocket实时奖励系统实现方案

## 系统概述

本系统基于现有的YouTube防刷系统架构，实现了WebSocket实时事件上报和奖励发放功能，将原有的`rexy/report`和`task/socialEvent`接口合并为统一的WebSocket接口，提供实时的进度计算和奖励发放机制。

## 核心功能

### 1. WebSocket实时通信
- **统一接口**: 合并事件上报和奖励获取为单一WebSocket接口
- **实时响应**: 前端定时上报事件，后端实时返回进度和奖励信息
- **心跳机制**: 保持连接稳定性，自动检测和清理无效连接
- **错误处理**: 完善的错误处理和重连机制

### 2. 进度计算逻辑
基于有效播放时长占视频总时长的百分比：
- **进度1**: 20%-40% → progress = 1
- **进度2**: 50%-70% → progress = 2  
- **进度3**: 80%-120% → progress = 3

有效播放时长计算公式：
```
累计有效播放时长 = 所有(播放到暂停)时间 - (失焦到聚焦)时间
```

### 3. 奖励发放机制
使用复合键防重复发放：
```
customerId + socialEvent + socialPlatform + socialContentId + sessionId + progress
```

字段说明：
- `socialEvent`: "watch"
- `socialPlatform`: "youtube"  
- `socialContentId`: YouTube视频ID
- `sessionId`: 会话ID
- `progress`: 进度等级（1、2、3）

## 技术架构

### 核心组件

#### 1. WebSocket处理层
- **YouTubeRealtimeHandler**: WebSocket消息处理器
- **WebSocketConfig**: WebSocket配置类
- **YouTubeWebSocketMessage**: 消息模型

#### 2. 业务服务层
- **RealtimeRewardService**: 实时奖励服务
- **EventDataProcessor**: 事件数据处理器
- **AsyncTool**: 异步处理工具

#### 3. 数据访问层
- **MaizeRecordBuilder**: 奖励记录数据访问
- **InformationBuilder**: 配置信息数据访问

### 异步处理架构

使用`AsyncTool`进行任务编排：
```java
// 并行执行事件处理和欺诈检测
CompletableFuture<EventResult> eventFuture = asyncTool.executeEventProcessing(eventTask);
CompletableFuture<Double> fraudFuture = asyncTool.executeFraudDetection(fraudTask);

// 基于前两个结果执行奖励计算
return eventFuture.thenCombine(fraudFuture, (event, fraud) -> {
    if (fraud > 0.7) return skipReward();
    return calculateReward(event, fraud);
});
```

## 消息协议

### 客户端发送消息

#### 事件上报消息
```json
{
  "messageType": "event_report",
  "messageId": "msg_123456789",
  "timestamp": 1640995200000,
  "eventData": {
    "sessionId": "session-123",
    "customerId": "customer-456",
    "deviceFinger": "device-fingerprint-789",
    "clientTimestamp": 1640995200000,
    "events": [
      {
        "eventType": "PLAYING",
        "timestamp": 1640995200000,
        "sequence": 1,
        "details": {
          "playingData": {
            "newState": "PLAYING",
            "currentTime": 120.5,
            "playbackRate": 1.0
          }
        }
      }
    ]
  }
}
```

#### 心跳消息
```json
{
  "messageType": "heartbeat",
  "messageId": "heartbeat_123456789",
  "timestamp": 1640995200000
}
```

### 服务端响应消息

#### 进度更新消息
```json
{
  "messageType": "progress_update",
  "messageId": "response_123456789",
  "timestamp": 1640995200000,
  "responseData": {
    "status": "SUCCESS",
    "processedEventCount": 5,
    "progressInfo": {
      "progress": 2,
      "effectiveWatchSeconds": 180,
      "videoDurationSeconds": 300,
      "watchPercentage": 0.6,
      "trustScore": 0.85,
      "rewardEligible": true,
      "nextRewardRequirement": "继续观看至80%可获得进度3奖励"
    },
    "nextReportInterval": 10
  }
}
```

#### 奖励通知消息
```json
{
  "messageType": "reward_notification",
  "messageId": "reward_123456789",
  "timestamp": 1640995200000,
  "responseData": {
    "status": "REWARD_AVAILABLE",
    "rewardInfo": {
      "rewardCode": "ABC123DEF456",
      "rewardScore": 150,
      "rewardLevel": "SILVER",
      "progress": 2,
      "expirationTime": 1640997000000,
      "description": "恭喜获得进度2奖励！"
    }
  }
}
```

## 配置管理

### YouTube奖励规则配置
存储在`Information`表的`rewardRules`字段中：

```json
{
  "type": "YouTube",
  "progressRewards": [
    {
      "progress": 1,
      "progressName": "初级观看",
      "minWatchPercentage": 0.2,
      "maxWatchPercentage": 0.4,
      "rewardScoreRange": {
        "minScore": 50,
        "maxScore": 100,
        "baseScore": 75,
        "rewardLevel": "BRONZE"
      },
      "minTrustScore": 0.3,
      "maxDailyRewards": 10,
      "rewardCodeExpirationMinutes": 30,
      "enabled": true
    }
  ],
  "globalConfig": {
    "minSessionDurationSeconds": 60,
    "maxSessionDurationSeconds": 7200,
    "fraudDetectionThreshold": 0.3,
    "enableRealtimeReward": true,
    "heartbeatIntervalSeconds": 30,
    "eventReportIntervalSeconds": 10,
    "maxConcurrentSessions": 5
  }
}
```

## 数据库设计

### MaizeRecord表扩展
新增字段支持复合键查询：
- 使用`sessionId + "_" + progress`作为复合ID
- 支持按复合键查询防重复发放

### 缓存策略
- **WebSocket会话缓存**: 存储活跃连接和会话信息
- **事件缓存**: Redis存储会话期间的事件数据
- **奖励缓存**: 临时缓存奖励计算结果

## 性能优化

### 1. 异步处理
- 事件处理、欺诈检测、奖励计算并行执行
- 使用专门的线程池避免阻塞
- 支持超时控制和降级处理

### 2. 连接管理
- 心跳检测自动清理无效连接
- 支持最大并发连接数限制
- 优雅关闭和资源清理

### 3. 数据优化
- 批量事件处理减少数据库访问
- 智能缓存策略提高响应速度
- 异步持久化避免阻塞实时响应

## 监控和运维

### 关键指标
- WebSocket连接数和活跃度
- 事件处理延迟和吞吐量
- 奖励发放成功率和错误率
- 线程池使用情况和性能指标

### 日志记录
- 详细的事件处理日志
- 奖励发放审计日志
- 异常和错误日志
- 性能监控日志

## 部署说明

### 1. 依赖配置
确保以下依赖已正确配置：
- Spring WebSocket支持
- Redis缓存配置
- 数据库连接池配置

### 2. 配置参数
```properties
# WebSocket配置
websocket.youtube.max-connections=1000
websocket.youtube.heartbeat-interval=30
websocket.youtube.event-report-interval=10

# 异步处理配置
async.event-processing.core-pool-size=4
async.event-processing.max-pool-size=16
async.reward-calculation.core-pool-size=2
async.reward-calculation.max-pool-size=8
```

### 3. 监控配置
- 配置JVM监控和线程池监控
- 设置WebSocket连接数告警
- 配置业务指标监控

## 测试策略

### 1. 单元测试
- WebSocket消息处理测试
- 异步任务编排测试
- 奖励计算逻辑测试

### 2. 集成测试
- 端到端WebSocket通信测试
- 数据库事务一致性测试
- 缓存同步测试

### 3. 性能测试
- 并发连接压力测试
- 事件处理性能测试
- 内存和CPU使用率测试

## 总结

本系统成功实现了YouTube防刷系统的WebSocket实时化改造，具备以下优势：

1. **实时性**: WebSocket双向通信，实时反馈进度和奖励
2. **高性能**: 异步处理架构，支持高并发场景
3. **可靠性**: 完善的错误处理和重连机制
4. **可扩展性**: 模块化设计，易于扩展新功能
5. **兼容性**: 保持与现有系统的完全兼容

系统已准备好部署到生产环境，为用户提供流畅的实时视频观看奖励体验。
