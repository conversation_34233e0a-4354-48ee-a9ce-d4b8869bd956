package com.drex.core.model.youtube;

/**
 * 欺诈指标阈值配置
 * 基于设计文档中的具体阈值和计算公式
 */
public class FraudIndicatorThresholds {

    // 2.1 重复事件 (REPEATED_EVENTS)
    public static final int REPEATED_EVENTS_THRESHOLD = 5; // 5次/分钟
    public static final int REPEATED_EVENTS_MAX_ALLOWED = 10; // 10次/分钟
    
    // 2.2 完成百分比异常 (ABNORMAL_COMPLETION_PERCENTAGE)
    public static final double COMPLETION_PERCENTAGE_EXPECTED = 0.9; // 90%
    public static final double COMPLETION_PERCENTAGE_MAX_ALLOWED = 1.5; // 150%
    
    // 2.3 低聚焦百分比 (LOW_FOCUS_DURATION)
    public static final double LOW_FOCUS_THRESHOLD = 0.7; // 70%
    public static final double LOW_FOCUS_MIN_ALLOWED = 0.3; // 30%
    
    // 2.4 长时间空闲 (LONG_IDLE_DURATION)
    public static final double LONG_IDLE_THRESHOLD = 0.2; // 20%
    public static final double LONG_IDLE_MAX_ALLOWED = 0.5; // 50%
    
    // 2.5 环境不一致 (ENVIRONMENT_INCONSISTENCY)
    public static final int ENVIRONMENT_ACCOUNT_THRESHOLD = 2; // 2个账号
    public static final int ENVIRONMENT_ACCOUNT_MAX_ALLOWED = 5; // 5个账号
    public static final int ENVIRONMENT_IP_CHANGE_THRESHOLD = 2; // 2次/小时
    public static final int ENVIRONMENT_IP_CHANGE_MAX_ALLOWED = 5; // 5次/小时
    
    // 2.6 时间戳异常 (TIMESTAMP_ANOMALY)
    public static final double TIMESTAMP_MAX_ALLOWED_DIFF_SECONDS = 5.0; // 5秒
    
    // 2.7 事件顺序异常 (EVENT_ORDER_ANOMALY)
    // 直接触发：若检测到任何顺序异常，IndicatorValue = 1.0
    
    // 2.8 异常播放速率 (EXCESSIVE_PLAYBACK_SPEED)
    public static final double PLAYBACK_RATE_THRESHOLD = 1.2;
    public static final double PLAYBACK_RATE_MAX_ALLOWED = 1.5;
    
    // 2.9 异常跳转 (ABNORMAL_SEEK)
    public static final int ABNORMAL_SEEK_THRESHOLD = 5; // 5次/分钟
    public static final int ABNORMAL_SEEK_MAX_ALLOWED = 10; // 10次/分钟
    
    // 2.10 指纹重复 (FINGERPRINT_DUPLICATION)
    public static final int FINGERPRINT_THRESHOLD = 2; // 2个账号
    public static final int FINGERPRINT_MAX_ALLOWED = 5; // 5个账号
    
    // 2.11 恶意IP (MALICIOUS_IP)
    // 直接触发：IP在ipReputations表标记为恶意，IndicatorValue = 1.0

    /**
     * 计算重复事件分数
     * 公式：IndicatorValue = min((repeatCount - 5) / (10 - 5), 1.0)
     */
    public static double calculateRepeatedEventsScore(double repeatCountPerMinute) {
        if (repeatCountPerMinute <= REPEATED_EVENTS_THRESHOLD) {
            return 0.0;
        }
        return Math.min((repeatCountPerMinute - REPEATED_EVENTS_THRESHOLD) / 
                (REPEATED_EVENTS_MAX_ALLOWED - REPEATED_EVENTS_THRESHOLD), 1.0);
    }

    /**
     * 计算完成百分比异常分数
     * 公式：IndicatorValue = min((completionPercentage - 0.9) / (1.5 - 0.9), 1.0)
     */
    public static double calculateCompletionPercentageAnomalyScore(double completionPercentage, 
                                                                  double realWatchTime, 
                                                                  double watchedDuration) {
        // 如果实际观看时间远小于watched_duration，IndicatorValue = 1.0
        if (realWatchTime < watchedDuration * 0.5) {
            return 1.0;
        }
        
        if (completionPercentage <= COMPLETION_PERCENTAGE_EXPECTED) {
            return 0.0;
        }
        
        return Math.min((completionPercentage - COMPLETION_PERCENTAGE_EXPECTED) / 
                (COMPLETION_PERCENTAGE_MAX_ALLOWED - COMPLETION_PERCENTAGE_EXPECTED), 1.0);
    }

    /**
     * 计算低聚焦百分比分数
     * 公式：IndicatorValue = min((0.7 - focusPercentage) / (0.7 - 0.3), 1.0)
     */
    public static double calculateLowFocusScore(double focusPercentage) {
        if (focusPercentage >= LOW_FOCUS_THRESHOLD) {
            return 0.0;
        }
        return Math.min((LOW_FOCUS_THRESHOLD - focusPercentage) / 
                (LOW_FOCUS_THRESHOLD - LOW_FOCUS_MIN_ALLOWED), 1.0);
    }

    /**
     * 计算长时间空闲分数
     * 公式：IndicatorValue = min((idlePercentage - 0.2) / (0.5 - 0.2), 1.0)
     */
    public static double calculateLongIdleScore(double idlePercentage) {
        if (idlePercentage <= LONG_IDLE_THRESHOLD) {
            return 0.0;
        }
        return Math.min((idlePercentage - LONG_IDLE_THRESHOLD) / 
                (LONG_IDLE_MAX_ALLOWED - LONG_IDLE_THRESHOLD), 1.0);
    }

    /**
     * 计算环境不一致分数
     * 公式：IndicatorValue = max(min((associatedAccountCount - 2) / (5 - 2), 1.0), 
     *                           min((ipChangeCount - 2) / (5 - 2), 1.0), 
     *                           isMaliciousIP)
     */
    public static double calculateEnvironmentInconsistencyScore(int associatedAccountCount, 
                                                               int ipChangeCount, 
                                                               boolean isMaliciousIP) {
        double accountScore = associatedAccountCount > ENVIRONMENT_ACCOUNT_THRESHOLD ? 
                Math.min((double)(associatedAccountCount - ENVIRONMENT_ACCOUNT_THRESHOLD) / 
                        (ENVIRONMENT_ACCOUNT_MAX_ALLOWED - ENVIRONMENT_ACCOUNT_THRESHOLD), 1.0) : 0.0;
        
        double ipChangeScore = ipChangeCount > ENVIRONMENT_IP_CHANGE_THRESHOLD ? 
                Math.min((double)(ipChangeCount - ENVIRONMENT_IP_CHANGE_THRESHOLD) / 
                        (ENVIRONMENT_IP_CHANGE_MAX_ALLOWED - ENVIRONMENT_IP_CHANGE_THRESHOLD), 1.0) : 0.0;
        
        double maliciousIpScore = isMaliciousIP ? 1.0 : 0.0;
        
        return Math.max(Math.max(accountScore, ipChangeScore), maliciousIpScore);
    }

    /**
     * 计算时间戳异常分数
     * 公式：IndicatorValue = min(timeDiffSeconds / 5, 1.0)
     */
    public static double calculateTimestampAnomalyScore(double timeDiffSeconds) {
        return Math.min(timeDiffSeconds / TIMESTAMP_MAX_ALLOWED_DIFF_SECONDS, 1.0);
    }

    /**
     * 计算异常播放速率分数
     * 公式：IndicatorValue = min((|playbackRate - 1.0| - 0.2) / (0.5 - 0.2), 1.0)
     */
    public static double calculateExcessivePlaybackSpeedScore(double playbackRate) {
        double deviation = Math.abs(playbackRate - 1.0);
        if (deviation <= 0.2) {
            return 0.0;
        }
        return Math.min((deviation - 0.2) / (PLAYBACK_RATE_MAX_ALLOWED - 1.0 - 0.2), 1.0);
    }

    /**
     * 计算异常跳转分数
     * 公式：IndicatorValue = min((seekCount - 5) / (10 - 5), 1.0)
     */
    public static double calculateAbnormalSeekScore(double seekCountPerMinute) {
        if (seekCountPerMinute <= ABNORMAL_SEEK_THRESHOLD) {
            return 0.0;
        }
        return Math.min((seekCountPerMinute - ABNORMAL_SEEK_THRESHOLD) / 
                (ABNORMAL_SEEK_MAX_ALLOWED - ABNORMAL_SEEK_THRESHOLD), 1.0);
    }

    /**
     * 计算指纹重复分数
     * 公式：IndicatorValue = min((associatedAccountCount - 2) / (5 - 2), 1.0)
     */
    public static double calculateFingerprintDuplicationScore(int associatedAccountCount) {
        if (associatedAccountCount <= FINGERPRINT_THRESHOLD) {
            return 0.0;
        }
        return Math.min((double)(associatedAccountCount - FINGERPRINT_THRESHOLD) / 
                (FINGERPRINT_MAX_ALLOWED - FINGERPRINT_THRESHOLD), 1.0);
    }

    /**
     * 计算恶意IP分数
     * 公式：IndicatorValue = isMaliciousIP ? 1.0 : 0.0
     */
    public static double calculateMaliciousIpScore(boolean isMaliciousIP) {
        return isMaliciousIP ? 1.0 : 0.0;
    }

    /**
     * 计算事件顺序异常分数
     * 公式：IndicatorValue = hasInvalidOrder ? 1.0 : 0.0
     */
    public static double calculateEventOrderAnomalyScore(boolean hasInvalidOrder) {
        return hasInvalidOrder ? 1.0 : 0.0;
    }

    /**
     * 验证所有阈值配置的合理性
     */
    public static boolean validateThresholds() {
        return REPEATED_EVENTS_THRESHOLD < REPEATED_EVENTS_MAX_ALLOWED &&
               COMPLETION_PERCENTAGE_EXPECTED < COMPLETION_PERCENTAGE_MAX_ALLOWED &&
               LOW_FOCUS_MIN_ALLOWED < LOW_FOCUS_THRESHOLD &&
               LONG_IDLE_THRESHOLD < LONG_IDLE_MAX_ALLOWED &&
               ENVIRONMENT_ACCOUNT_THRESHOLD < ENVIRONMENT_ACCOUNT_MAX_ALLOWED &&
               ENVIRONMENT_IP_CHANGE_THRESHOLD < ENVIRONMENT_IP_CHANGE_MAX_ALLOWED &&
               PLAYBACK_RATE_THRESHOLD < PLAYBACK_RATE_MAX_ALLOWED &&
               ABNORMAL_SEEK_THRESHOLD < ABNORMAL_SEEK_MAX_ALLOWED &&
               FINGERPRINT_THRESHOLD < FINGERPRINT_MAX_ALLOWED &&
               TIMESTAMP_MAX_ALLOWED_DIFF_SECONDS > 0;
    }

    /**
     * 获取所有阈值的描述信息
     */
    public static String getThresholdDescription() {
        return String.format(
            "FraudIndicatorThresholds Configuration:\n" +
            "- Repeated Events: %d-%d times/minute\n" +
            "- Completion Percentage: %.1f-%.1f\n" +
            "- Focus Duration: %.1f-%.1f\n" +
            "- Idle Duration: %.1f-%.1f\n" +
            "- Environment Accounts: %d-%d\n" +
            "- Environment IP Changes: %d-%d times/hour\n" +
            "- Timestamp Diff: %.1f seconds\n" +
            "- Playback Rate: %.1f-%.1f\n" +
            "- Seek Count: %d-%d times/minute\n" +
            "- Fingerprint Accounts: %d-%d",
            REPEATED_EVENTS_THRESHOLD, REPEATED_EVENTS_MAX_ALLOWED,
            COMPLETION_PERCENTAGE_EXPECTED, COMPLETION_PERCENTAGE_MAX_ALLOWED,
            LOW_FOCUS_MIN_ALLOWED, LOW_FOCUS_THRESHOLD,
            LONG_IDLE_THRESHOLD, LONG_IDLE_MAX_ALLOWED,
            ENVIRONMENT_ACCOUNT_THRESHOLD, ENVIRONMENT_ACCOUNT_MAX_ALLOWED,
            ENVIRONMENT_IP_CHANGE_THRESHOLD, ENVIRONMENT_IP_CHANGE_MAX_ALLOWED,
            TIMESTAMP_MAX_ALLOWED_DIFF_SECONDS,
            PLAYBACK_RATE_THRESHOLD, PLAYBACK_RATE_MAX_ALLOWED,
            ABNORMAL_SEEK_THRESHOLD, ABNORMAL_SEEK_MAX_ALLOWED,
            FINGERPRINT_THRESHOLD, FINGERPRINT_MAX_ALLOWED
        );
    }
}
