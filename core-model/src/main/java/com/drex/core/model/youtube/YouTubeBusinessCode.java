package com.drex.core.model.youtube;

import lombok.Getter;

/**
 * YouTube防刷系统业务代码枚举
 * 扩展现有的RexyBusinessCode，专门用于YouTube防刷功能
 */
@Getter
public enum YouTubeBusinessCode {

    // YouTube防刷系统业务代码 (2000-2999)
    VIDEO_SESSION_INIT(2000, "视频会话初始化"),
    VIDEO_SESSION_UPDATE(2001, "视频会话更新"),
    VIDEO_SESSION_COMPLETE(2002, "视频会话完成"),
    
    EVENT_REPORT(2010, "事件数据上报"),
    EVENT_VALIDATION(2011, "事件数据验证"),
    EVENT_ANALYSIS(2012, "事件数据分析"),
    
    FRAUD_DETECTION(2020, "欺诈检测"),
    TRUST_SCORE_CALCULATION(2021, "信任分数计算"),
    RISK_ASSESSMENT(2022, "风险评估"),
    
    REWARD_GENERATION(2030, "奖励生成"),
    REWARD_VALIDATION(2031, "奖励验证"),
    REWARD_DISTRIBUTION(2032, "奖励发放"),
    
    USER_BLACKLIST_ADD(2040, "用户加入黑名单"),
    USER_BLACKLIST_REMOVE(2041, "用户移出黑名单"),
    IP_BLACKLIST_ADD(2042, "IP加入黑名单"),
    IP_BLACKLIST_REMOVE(2043, "IP移出黑名单"),
    
    FINGERPRINT_ANALYSIS(2050, "指纹分析"),
    IP_REPUTATION_CHECK(2051, "IP信誉检查"),
    ENVIRONMENT_ANALYSIS(2052, "环境分析"),
    
    PLAYBACK_ANALYSIS(2060, "播放行为分析"),
    FOCUS_ANALYSIS(2061, "焦点行为分析"),
    INTERACTION_ANALYSIS(2062, "交互行为分析"),
    
    SYSTEM_CONFIG_UPDATE(2070, "系统配置更新"),
    CACHE_CLEANUP(2071, "缓存清理"),
    DATA_MIGRATION(2072, "数据迁移"),
    
    MONITORING_STATISTICS(2080, "监控统计"),
    FRAUD_REPORT(2081, "欺诈报告"),
    PERFORMANCE_METRICS(2082, "性能指标"),
    
    // 错误和异常代码 (2900-2999)
    SESSION_EXPIRED(2900, "会话过期"),
    INVALID_SIGNATURE(2901, "签名无效"),
    ENCRYPTION_FAILED(2902, "加密失败"),
    DECRYPTION_FAILED(2903, "解密失败"),
    DATA_VALIDATION_FAILED(2904, "数据验证失败"),
    FRAUD_DETECTED(2905, "检测到欺诈"),
    TRUST_SCORE_LOW(2906, "信任分数过低"),
    REWARD_ALREADY_CLAIMED(2907, "奖励已领取"),
    USER_BLACKLISTED(2908, "用户被拉黑"),
    IP_BLACKLISTED(2909, "IP被拉黑"),
    RATE_LIMIT_EXCEEDED(2910, "请求频率超限"),
    SYSTEM_MAINTENANCE(2911, "系统维护中"),
    UNKNOWN_ERROR(2999, "未知错误");

    private final Integer code;
    private final String description;

    YouTubeBusinessCode(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static YouTubeBusinessCode getByCode(Integer code) {
        for (YouTubeBusinessCode businessCode : values()) {
            if (businessCode.getCode().equals(code)) {
                return businessCode;
            }
        }
        return UNKNOWN_ERROR;
    }

    /**
     * 判断是否为错误代码
     */
    public boolean isError() {
        return this.code >= 2900;
    }

    /**
     * 判断是否为成功代码
     */
    public boolean isSuccess() {
        return this.code < 2900;
    }
}
