<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.drex</groupId>
        <artifactId>drex-core</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>core-dal</artifactId>
    <name>core-dal</name>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.property.path>..</project.property.path>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-ots-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>protobuf-java</artifactId>
            <groupId>com.google.protobuf</groupId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.drex</groupId>
            <artifactId>core-model</artifactId>
        </dependency>
    </dependencies>
</project>
