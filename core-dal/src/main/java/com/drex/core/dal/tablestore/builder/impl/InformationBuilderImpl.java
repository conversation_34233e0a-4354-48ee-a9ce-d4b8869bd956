package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.InformationBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.Information;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Repository("informationBuilder")
public class InformationBuilderImpl extends WideColumnStoreBuilder<Information> implements InformationBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName() {
        return Constant.TABLE_NAME_INFORMATION;
    }

    @PostConstruct
    public void init() {
        super.init(Information.class);
    }

    @Override
    public Information getById(String id) {
        Information entity = new Information();
        entity.setId(id);
        return getRow(entity);
    }

    @Override
    public Boolean insert(Information information) {
        if (information.getId() == null) {
            information.setId(idUtils.nextId());
        }
        if (information.getCreated() == null) {
            information.setCreated(System.currentTimeMillis());
        }
        information.setModified(System.currentTimeMillis());
        return super.putRow(information);
    }

    @Override
    public Boolean update(Information information) {
        information.setModified(System.currentTimeMillis());
        return super.updateRow(information);
    }

    @Override
    public Boolean delete(String id) {
        Information entity = new Information();
        entity.setId(id);
        return super.deleteRow(entity);
    }

    @Override
    public List<Information> getByType(String type) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_TYPE, type));

        Sort sort = createDefaultSort();
        return search(boolQuery.build(), sort, 0, 100, Constant.SEARCH_INDEX_INFORMATION_INDEX);
    }

    @Override
    public List<Information> getByRecommend(Boolean isRecommend, String position) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_IS_RECOMMEND, isRecommend))
                .must(QueryBuilders.term(Constant.COLUMN_NAME_POSITION, position));

        Sort sort = createDefaultSort();
        return search(boolQuery.build(), sort, 0, 100, Constant.SEARCH_INDEX_INFORMATION_INDEX);
    }

    @Override
    public List<Information> getByTypeAndRecommend(String type, Boolean isRecommend) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_TYPE, type))
                .must(QueryBuilders.term(Constant.COLUMN_NAME_IS_RECOMMEND, isRecommend));

        Sort sort = createDefaultSort();
        return search(boolQuery.build(), sort, 0, 100, Constant.SEARCH_INDEX_INFORMATION_INDEX);
    }

    @Override
    public List<Information> listAll(int offset, int limit, String position) {

        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_POSITION, position));

        if (offset < 0) {
            offset = 0;
        }
        if (limit <= 0) {
            limit = 100;
        }
        Sort sort = createDefaultSort(position);
        return search(boolQuery.build(), sort, offset, limit, Constant.SEARCH_INDEX_INFORMATION_INDEX);
    }

    private Sort createDefaultSort() {
       return createDefaultSort(null);
    }

    private Sort createDefaultSort(String position) {
        List<Sort.Sorter> sorters = new ArrayList<>();
        // 按排序值排序
        sorters.add(new FieldSort(Constant.COLUMN_NAME_SORT, SortOrder.DESC));
        if("discovery_blog".equals(position)) {
            sorters.add(new FieldSort(Constant.COLUMN_NAME_CREATED, SortOrder.DESC));
        }
        if("discovery_events".equals(position)){
            sorters.add(new FieldSort(Constant.COLUMN_NAME_ACTIVITY_START_TIME, SortOrder.ASC));
        }
        return new Sort(sorters);
    }
}
