package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.RexyClaimRecordBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.RexyClaimRecord;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Repository("rexyClaimRecordBuilder")
public class RexyClaimRecordBuilderImpl extends WideColumnStoreBuilder<RexyClaimRecord> implements RexyClaimRecordBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName(){
        // 使用常量替换写死的表名
        return Constant.TABLE_NAME_REXY_CLAIM_RECORD;
    }

    @PostConstruct
    public void init() {
        super.init(RexyClaimRecord.class);
    }

    @Override
    public RexyClaimRecord getByRecordId(String recordId) {
        RexyClaimRecord entity = new RexyClaimRecord();
        entity.setRecordId(recordId);
        return getRow(entity);
    }

    @Override
    public Boolean insert(RexyClaimRecord rexyClaimRecord) {
        if(rexyClaimRecord.getRecordId() == null){
            rexyClaimRecord.setRecordId(idUtils.nextId());
        }
        return super.putRow(rexyClaimRecord);
    }

    @Override
    public Boolean update(RexyClaimRecord rexyClaimRecord) {
        return super.updateRow(rexyClaimRecord, List.of("status"));
    }

    @Override
    public Boolean updateHashCode(RexyClaimRecord rexyClaimRecord) {
        return super.updateRow(rexyClaimRecord, List.of("status","transfer_hash_code","point","create"));
    }

    @Override
    public BigDecimal getByCustomerIdAndStatus(String customerId, String status) {
        String aggName = "sum_agg_process_point_by_customer_side";

        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId))
                .must(QueryBuilders.term(Constant.COLUMN_NAME_STATUS, status));

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.sum(aggName, "point")).build();

        return BigDecimal.valueOf(searchSum(searchQuery, Constant.SEARCH_INDEX_REXY_CLAIM_RECORD, aggName));
    }

    public List<RexyClaimRecord> getByStatus(String status){
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_STATUS, status))
                .must(QueryBuilders.range(Constant.COLUMN_NAME_CREATE).greaterThan(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(60)).lessThan(System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(1)));
        return search(boolQuery.build(), null, 0, 100, Constant.SEARCH_INDEX_REXY_CLAIM_RECORD);
    }
}
