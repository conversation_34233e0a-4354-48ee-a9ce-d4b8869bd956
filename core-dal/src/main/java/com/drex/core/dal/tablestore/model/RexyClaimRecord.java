package com.drex.core.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.*;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Table(name = Constant.TABLE_NAME_REXY_CLAIM_RECORD)
@Data
public class RexyClaimRecord implements Serializable {



    @PartitionKey(name = Constant.COLUMN_NAME_RECORD_ID, type = PartitionKey.Type.STRING)
    private String recordId;

    @Column(name = Constant.COLUMN_NAME_CUSTOMER_ID, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_REXY_CLAIM_RECORD, column = Constant.COLUMN_NAME_CUSTOMER_ID)
    private String customerId;

    @Column(name = Constant.COLUMN_NAME_ADDRESS)
    private String address;

    @Column(name = Constant.COLUMN_NAME_POINT, type = Column.Type.DOUBLE)
    private BigDecimal point;

    @Column(name = Constant.COLUMN_NAME_BASKET_TYPE)
    private String basketType;

    @Column(name = Constant.COLUMN_NAME_TRANSFER_HASH_CODE)
    private String transferHashCode;

    @Column(name = Constant.COLUMN_NAME_STATUS, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_REXY_CLAIM_RECORD, column = Constant.COLUMN_NAME_STATUS)
    private String status;

    @Column(name = Constant.COLUMN_NAME_CREATE)
    @SearchIndex(name = Constant.SEARCH_INDEX_REXY_CLAIM_RECORD, column = Constant.COLUMN_NAME_CREATE)
    private Long create;

    @Column(name = Constant.COLUMN_NAME_MODIFIED)
    private Long modified;
}
