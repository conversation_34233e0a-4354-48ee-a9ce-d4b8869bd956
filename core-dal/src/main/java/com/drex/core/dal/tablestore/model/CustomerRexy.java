package com.drex.core.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

@Table(name = Constant.TABLE_NAME_CUSTOMER_REXY)
@Data
public class CustomerRexy implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_CUSTOMER_ID, type = PartitionKey.Type.STRING)
    @SearchIndex(name = Constant.SEARCH_INDEX_CUSTOMER_REXY, column = Constant.COLUMN_NAME_CUSTOMER_ID)
    private String customerId;

    @PartitionKey(name = Constant.COLUMN_NAME_REXY_ID, type = PartitionKey.Type.STRING)
    private String rexyId;

    @Column(name = Constant.COLUMN_NAME_STATUS, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_CUSTOMER_REXY, column = Constant.COLUMN_NAME_STATUS)
    private String status;

    @Column(name = Constant.COLUMN_NAME_REXY_NAME, isDefined = true)
    private String rexyName;

    @Column(name = Constant.COLUMN_NAME_REXY_LEVEL, isDefined = true)
    private String rexyLevel;

    @Column(name = Constant.COLUMN_NAME_REXY_RATE, isDefined = true)
    private Integer rexyRate;

    @Column(name = Constant.COLUMN_NAME_REXY_BASKET_LIMIT, isDefined = true)
    private Integer rexyBasketLimit;

    @Column(name = Constant.COLUMN_NAME_CREATED, isDefined = true)
    private Long created;

    @Column(name = Constant.COLUMN_NAME_REXY_AVATAR, isDefined = true)
    private String rexyAvatar;
}
