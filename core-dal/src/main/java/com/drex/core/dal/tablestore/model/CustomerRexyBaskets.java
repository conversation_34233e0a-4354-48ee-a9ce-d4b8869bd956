package com.drex.core.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Table(name = Constant.TABLE_NAME_CUSTOMER_REXY_BASKETS)
@Data
public class CustomerRexyBaskets implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_CUSTOMER_ID, type = PartitionKey.Type.STRING)
    private String customerId;

    @PartitionKey(name = Constant.COLUMN_NAME_BASKET_TYPE, type = PartitionKey.Type.STRING, value = 1)
    private String basketType;

    @Column(name = Constant.COLUMN_NAME_RECEIVED, isDefined = true)
    private BigDecimal received;

    @Column(name = Constant.COLUMN_NAME_BASKET_LIMIT, isDefined = true)
    private BigDecimal basketLimit;

    @Column(name = Constant.COLUMN_NAME_LAST_CLAIM_TIME, isDefined = true)
    private Long lastClaimTime;

    @Column(name = Constant.COLUMN_NAME_MODIFIED, isDefined = true)
    private Long modified;
}
