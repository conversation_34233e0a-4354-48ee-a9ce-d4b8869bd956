package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.UserFingerprint;

import java.util.List;

/**
 * 用户指纹数据访问接口
 */
public interface UserFingerprintBuilder {

    /**
     * 根据ID获取用户指纹
     *
     * @param id 指纹ID
     * @return 用户指纹
     */
    UserFingerprint getById(String id);

    /**
     * 根据指纹哈希获取用户指纹
     *
     * @param fingerprintHash 指纹哈希
     * @return 用户指纹
     */
    UserFingerprint getByFingerprintHash(String fingerprintHash);

    /**
     * 根据用户ID获取用户指纹列表
     *
     * @param customerId 用户ID
     * @return 用户指纹列表
     */
    List<UserFingerprint> getByCustomerId(String customerId);

    /**
     * 根据IP地址获取用户指纹列表
     *
     * @param ipAddress IP地址
     * @return 用户指纹列表
     */
    List<UserFingerprint> getByIpAddress(String ipAddress);

    /**
     * 获取可疑指纹列表
     *
     * @return 可疑指纹列表
     */
    List<UserFingerprint> getSuspiciousFingerprints();

    /**
     * 插入用户指纹
     *
     * @param fingerprint 用户指纹
     * @return 是否成功
     */
    Boolean insert(UserFingerprint fingerprint);

    /**
     * 更新用户指纹
     *
     * @param fingerprint 用户指纹
     * @return 是否成功
     */
    Boolean update(UserFingerprint fingerprint);

    /**
     * 删除用户指纹
     *
     * @param id 指纹ID
     * @return 是否成功
     */
    Boolean delete(String id);

    /**
     * 检查指纹是否与多个用户关联
     *
     * @param fingerprintHash 指纹哈希
     * @param threshold 阈值
     * @return 是否超过阈值
     */
    Boolean isAssociatedWithMultipleUsers(String fingerprintHash, int threshold);

    /**
     * 根据时间范围获取指纹记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 指纹记录列表
     */
    List<UserFingerprint> getByTimeRange(Long startTime, Long endTime);
}
