package com.drex.core.dal.tablestore;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component("idUtils")
public class IdUtils {

    private static Snowflake snowflake;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public String nextId() {
        if(snowflake == null){
            long workerId = redisTemplate.opsForValue().increment("seq:workId");
            snowflake = IdUtil.getSnowflake(workerId % 32, 2);
        }
        return snowflake.nextIdStr();
    }
}
