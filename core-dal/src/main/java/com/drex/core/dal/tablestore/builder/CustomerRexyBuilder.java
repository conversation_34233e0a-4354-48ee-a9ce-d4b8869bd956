package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.CustomerRexy;

import java.util.List;

public interface CustomerRexyBuilder {

    /**
     * 根据用户 ID 和恐龙 ID 获取用户拥有的恐龙信息。
     *
     * @param customerId 用户的唯一标识符。
     * @param rexyId 恐龙的唯一标识符。
     * @return 返回与指定用户 ID 和恐龙 ID 对应的恐龙对象。如果找不到对应的恐龙，则返回 null。
     */
    CustomerRexy getByCustomerAndRexyId(String customerId, String rexyId);

    /**
     * 插入一个新的用户拥有的恐龙记录到数据库中。
     *
     * @param customerRexy 要插入的用户恐龙对象
     * @return 如果插入成功，返回 true；否则返回 false
     */
    Boolean insert(CustomerRexy customerRexy);

    Boolean delete(CustomerRexy customerRexy);

    Boolean batchDelete(List<CustomerRexy> customerRexyList);

    Boolean updateStatus(CustomerRexy customerRexy);
    /**
     * 根据用户ID和状态查询用户拥有的恐龙
     *
     * @param customerId
     * @param status
     * @return
     */
    List<CustomerRexy> getByCustomerAndStatus(String customerId, String status);

    List<CustomerRexy> getByCustomer(String customerId);
}
