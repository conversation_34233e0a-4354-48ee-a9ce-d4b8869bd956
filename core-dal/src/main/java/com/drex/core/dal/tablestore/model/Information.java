package com.drex.core.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Table(name = Constant.TABLE_NAME_INFORMATION)
@Data
public class Information implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_ID, type = PartitionKey.Type.STRING)
    private String id;

    @Column(name = Constant.COLUMN_NAME_TYPE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_TYPE)
    private String type;

    @Column(name = Constant.COLUMN_NAME_NAME, isDefined = true)
    private String name;

    @Column(name = Constant.COLUMN_NAME_LOGO, isDefined = true)
    private String logo;

    @Column(name = Constant.COLUMN_NAME_TITLE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_TITLE)
    private String title;

    @Column(name = Constant.COLUMN_NAME_SUB_TITLE, isDefined = true)
    private String subTitle;

    @Column(name = Constant.COLUMN_NAME_SUMMARY, isDefined = true)
    private String summary;

    @Column(name = Constant.COLUMN_NAME_CONTENT, isDefined = true)
    private String content;

    @Column(name = Constant.COLUMN_NAME_IMAGE, isDefined = true)
    private String image;

    @Column(name = Constant.COLUMN_NAME_LINK, isDefined = true)
    private String link;

    @Column(name = Constant.COLUMN_NAME_CATEGORY, isDefined = true)
    private List<String> category;

    @Column(name = Constant.COLUMN_NAME_IS_RECOMMEND, isDefined = true, type = Column.Type.BOOLEAN)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_IS_RECOMMEND, fieldType = FieldType.BOOLEAN)
    private Boolean isRecommend;

    @Column(name = Constant.COLUMN_NAME_SORT, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_SORT, fieldType = FieldType.LONG)
    private Integer sort;

    @Column(name = Constant.COLUMN_NAME_TAG, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_TAG, fieldType = FieldType.LONG)
    private String tag;

    @Column(name = Constant.COLUMN_NAME_DATE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_DATE, fieldType = FieldType.LONG)
    private Long date;

    @Column(name = Constant.COLUMN_NAME_CREATED, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_CREATED, fieldType = FieldType.LONG)
    private Long created;

    @Column(name = Constant.COLUMN_NAME_MODIFIED, isDefined = true)
    private Long modified;

    @Column(name = Constant.COLUMN_NAME_ORGANIZER, isDefined = true)
    private String organizer;

    @Column(name = Constant.COLUMN_NAME_LOCATION, isDefined = true)
    private String location;

    @Column(name = Constant.COLUMN_NAME_ACTIVITY_START_TIME, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_ACTIVITY_START_TIME, fieldType = FieldType.LONG)
    private Long activityStartTime;

    @Column(name = Constant.COLUMN_NAME_ACTIVITY_END_TIME, isDefined = true)
    private Long activityEndTime;


    @Column(name = Constant.COLUMN_NAME_POSITION, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_POSITION)
    private String position;

    @Column(name = Constant.COLUMN_NAME_IS_REWARD, isDefined = true, type = Column.Type.BOOLEAN)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_IS_REWARD, fieldType = FieldType.BOOLEAN)
    private Boolean isReward;

    @Column(name = Constant.COLUMN_NAME_REWARD_AMOUNT, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_REWARD_AMOUNT, fieldType = FieldType.LONG)
    private String rewardAmount;

    @Column(name = Constant.COLUMN_NAME_REWARD_RULES, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_INFORMATION_INDEX, column = Constant.COLUMN_NAME_REWARD_RULES)
    private String rewardRules;
}
