package com.drex.core.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

@Table(name = Constant.TABLE_NAME_REXY_BASKET_RECORD)
@Data
public class RexyBasketRecord implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_ID, type = PartitionKey.Type.STRING)
    private String id;

    @Column(name = Constant.COLUMN_NAME_CUSTOMER_ID, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_REXY_BASKET_RECORD, column = Constant.COLUMN_NAME_CUSTOMER_ID)
    private String customerId;

    @Column(name = Constant.COLUMN_NAME_BASKET_TYPE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_REXY_BASKET_RECORD, column = Constant.COLUMN_NAME_BASKET_TYPE)
    private String basketType;

    @Column(name = Constant.COLUMN_NAME_BUSINESS_ID, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_REXY_BASKET_RECORD, column = Constant.COLUMN_NAME_BUSINESS_ID)
    private String businessId;

    @Column(name = Constant.COLUMN_NAME_BUSINESS_CODE, isDefined = true)
    private Integer businessCode;

    @Column(name = Constant.COLUMN_NAME_OPERATE_TYPE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_REXY_BASKET_RECORD, column = Constant.COLUMN_NAME_OPERATE_TYPE)
    private String operateType;

    @Column(name = Constant.COLUMN_NAME_AMOUNT, isDefined = true)
    private Integer amount;

    @Column(name = Constant.COLUMN_NAME_AVAILABLE, isDefined = true)
    private Integer available;

    @Column(name = Constant.COLUMN_NAME_STATUS, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_REXY_BASKET_RECORD, column = Constant.COLUMN_NAME_STATUS)
    private String status;

    @Column(name = Constant.COLUMN_NAME_CREATED, isDefined = true)
    private Long created;
}
