package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.RexyConfig;
import com.kikitrade.framework.common.model.TokenPage;

import java.util.List;

public interface RexyConfigBuilder {

    RexyConfig getDefaultByLevel(String level);

    /**
     * 根据 ID 获取恐龙配置信息。
     *
     * @param id 恐龙配置的唯一标识符。
     * @return 返回与指定 ID 对应的恐龙配置对象。如果找不到对应的配置，则返回 null。
     */
    RexyConfig getById(String id);

    /**
     * 插入一个新的恐龙配置记录到数据库中。
     *
     * @param rexyConfig 要插入的恐龙配置对象
     * @return 如果插入成功，返回 true；否则返回 false
     */
    Boolean insert(RexyConfig rexyConfig);

    Boolean deleteById(String id);

    /**
     * 获取所有恐龙配置列表
     *
     * @return 恐龙配置列表
     */
    List<RexyConfig> listAll();
}
