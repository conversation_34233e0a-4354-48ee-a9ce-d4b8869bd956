package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.Notice;

import java.util.List;

public interface NoticeBuilder {

    /**
     * 根据ID获取通知
     *
     * @param id 通知的唯一标识符
     * @return 返回与指定ID对应的通知对象。如果找不到对应的通知，则返回null
     */
    Notice getById(String id);

    /**
     * 插入一条新的通知记录
     *
     * @param notice 要插入的通知对象
     * @return 如果插入成功，返回true；否则返回false
     */
    Boolean insert(Notice notice);

    /**
     * 更新通知记录
     *
     * @param notice 要更新的通知对象
     * @return 如果更新成功，返回true；否则返回false
     */
    Boolean update(Notice notice);

    /**
     * 删除通知记录
     *
     * @param id 要删除的通知ID
     * @return 如果删除成功，返回true；否则返回false
     */
    Boolean delete(String id);

    /**
     * 根据状态和发送目标获取通知列表
     *
     * @param status 通知状态
     * @return 返回符合条件的通知列表
     */
    List<Notice> getByStatus(String status, Long beginTime);

}
