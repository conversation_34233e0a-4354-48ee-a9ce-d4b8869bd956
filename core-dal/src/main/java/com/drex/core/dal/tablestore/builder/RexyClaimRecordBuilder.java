package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.RexyClaimRecord;

import java.math.BigDecimal;
import java.util.List;

public interface RexyClaimRecordBuilder {

    RexyClaimRecord getByRecordId(String recordId);

    Boolean insert(RexyClaimRecord rexyClaimRecord);

    Boolean update(RexyClaimRecord rexyClaimRecord);

    Boolean updateHashCode(RexyClaimRecord rexyClaimRecord);

    BigDecimal getByCustomerIdAndStatus(String customerId, String status);

    List<RexyClaimRecord> getByStatus(String status);
}
