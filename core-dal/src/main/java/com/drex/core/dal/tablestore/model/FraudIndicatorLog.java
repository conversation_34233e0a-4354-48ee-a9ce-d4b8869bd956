package com.drex.core.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

/**
 * 欺诈指标日志表
 * 记录会话期间的各种欺诈风险指标
 */
@Table(name = Constant.TABLE_NAME_FRAUD_INDICATOR_LOG)
@Data
public class FraudIndicatorLog implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_ID, type = PartitionKey.Type.STRING)
    private String id;

    @Column(name = Constant.COLUMN_NAME_SESSION_ID, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, column = Constant.COLUMN_NAME_SESSION_ID)
    private String sessionId;

    @Column(name = Constant.COLUMN_NAME_CUSTOMER_ID, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, column = Constant.COLUMN_NAME_CUSTOMER_ID)
    private String customerId;

    @Column(name = Constant.COLUMN_NAME_INDICATOR_TYPE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, column = Constant.COLUMN_NAME_INDICATOR_TYPE)
    private String indicatorType;

    @Column(name = Constant.COLUMN_NAME_DETAILS, isDefined = true)
    private String details;

    @Column(name = Constant.COLUMN_NAME_SCORE_IMPACT, type = Column.Type.DOUBLE, isDefined = true)
    private Double scoreImpact;

    @Column(name = Constant.COLUMN_NAME_TIMESTAMP, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, column = Constant.COLUMN_NAME_TIMESTAMP, fieldType = FieldType.LONG)
    private Long timestamp;

    @Column(name = Constant.COLUMN_NAME_CREATED, isDefined = true)
    private Long created;
}
