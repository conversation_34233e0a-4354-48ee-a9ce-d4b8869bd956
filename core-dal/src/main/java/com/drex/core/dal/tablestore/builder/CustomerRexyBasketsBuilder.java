package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.CustomerRexyBaskets;

public interface CustomerRexyBasketsBuilder {

    /**
     * 根据用户 ID 获取用户的恐龙篮子信息。
     *
     * @param customerId 用户的唯一标识符。
     *        basketType 篮子类型
     * @return 返回与指定用户 ID 对应的恐龙篮子对象。如果找不到对应的篮子，则返回 null。
     */
    CustomerRexyBaskets getByCustomerId(String customerId, String basketType);

    /**
     * 插入一个新的用户恐龙篮子记录到数据库中。
     *
     * @param customerRexyBaskets 要插入的用户恐龙篮子对象
     * @return 如果插入成功，返回 true；否则返回 false
     */
    Boolean insert(CustomerRexyBaskets customerRexyBaskets);

    /**
     * 更新用户恐龙篮
     *
     */
    Boolean update(CustomerRexyBaskets customerRexyBaskets);
}
