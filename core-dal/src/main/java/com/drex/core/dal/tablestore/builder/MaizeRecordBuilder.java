package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.MaizeRecord;
import java.util.List;

public interface MaizeRecordBuilder {

    boolean save(MaizeRecord maizeRecordDO);

    MaizeRecord findMaizeRecords(String customerId, String socialEvent, String socialContentId);

    /**
     * 根据复合键查询奖励记录
     * 用于检查是否已发放特定进度的奖励
     *
     * @param customerId 用户ID
     * @param socialEvent 社交事件类型（固定值"watch"）
     * @param socialPlatform 社交平台（固定值"youtube"）
     * @param socialContentId YouTube视频ID
     * @param sessionId 会话ID
     * @param progress 进度等级（1、2、3）
     * @return 奖励记录，如果不存在则返回null
     */
    MaizeRecord findMaizeRecordByCompositeKey(String customerId, String socialEvent, String socialPlatform,
                                            String socialContentId, String sessionId, Integer progress);

}

