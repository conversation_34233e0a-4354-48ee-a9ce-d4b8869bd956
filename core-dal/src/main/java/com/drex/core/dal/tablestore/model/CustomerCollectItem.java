package com.drex.core.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

@Table(name = Constant.TABLE_NAME_CUSTOMER_COLLECT_ITEM)
@Data
public class CustomerCollectItem implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_CUSTOMER_ID, type = PartitionKey.Type.STRING)
    private String customerId;

    @PartitionKey(name = Constant.COLUMN_NAME_RESOURCE_ID, type = PartitionKey.Type.STRING)
    private String resourceId;

    @PartitionKey(name = Constant.COLUMN_NAME_TYPE, type = PartitionKey.Type.STRING)
    private String type;

    @PartitionKey(name = Constant.COLUMN_NAME_PLATFORM, type = PartitionKey.Type.STRING)
    private String platform;

    @Column(name = Constant.COLUMN_NAME_PARAM, isDefined = true)
    private String param;

    @Column(name = Constant.COLUMN_NAME_CREATE, isDefined = true)
    private Long create;
}
