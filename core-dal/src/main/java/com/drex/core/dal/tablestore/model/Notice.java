package com.drex.core.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

@Table(name = Constant.TABLE_NAME_NOTICE)
@Data
public class Notice implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_ID, type = PartitionKey.Type.STRING)
    private String id;

    @Column(name = Constant.COLUMN_NAME_TITLE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_NOTICE_INDEX, column = Constant.COLUMN_NAME_TITLE)
    private String title;

    @Column(name = Constant.COLUMN_NAME_SUB_TITLE, isDefined = true)
    private String subTitle;

    @Column(name = Constant.COLUMN_NAME_CONTENT, isDefined = true)
    private String content;

    @Column(name = Constant.COLUMN_NAME_LINK, isDefined = true)
    private String link;

    @Column(name = Constant.COLUMN_NAME_NOTIFY_TIME, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_NOTICE_INDEX, column = Constant.COLUMN_NAME_NOTIFY_TIME, fieldType = FieldType.LONG)
    private Long notifyTime;

    @Column(name = Constant.COLUMN_NAME_STATUS, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_NOTICE_INDEX, column = Constant.COLUMN_NAME_STATUS)
    private String status;

    @Column(name = Constant.COLUMN_NAME_SEND_TO, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_NOTICE_INDEX, column = Constant.COLUMN_NAME_SEND_TO)
    private Integer sendTo;

    @Column(name = Constant.COLUMN_NAME_CREATED, isDefined = true)
    private Long created;

    @Column(name = Constant.COLUMN_NAME_MODIFIED, isDefined = true)
    private Long modified;
}
