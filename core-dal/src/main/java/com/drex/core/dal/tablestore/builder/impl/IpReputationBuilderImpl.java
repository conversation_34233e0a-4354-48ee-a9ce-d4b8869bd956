package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.Query;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.query.RangeQuery;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.core.dal.tablestore.builder.IpReputationBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.IpReputation;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.List;

@Repository("ipReputationBuilder")
public class IpReputationBuilderImpl extends WideColumnStoreBuilder<IpReputation> implements IpReputationBuilder {

    public String getTableName() {
        return Constant.TABLE_NAME_IP_REPUTATION;
    }

    @PostConstruct
    public void init() {
        super.init(IpReputation.class);
    }

    @Override
    public IpReputation getByIpAddress(String ipAddress) {
        IpReputation entity = new IpReputation();
        entity.setIpAddress(ipAddress);
        return getRow(entity);
    }

    @Override
    public List<IpReputation> getProxyIps() {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_IS_PROXY, true).build()
        ));

        Sort sort = new Sort(List.of(new FieldSort(Constant.COLUMN_NAME_LAST_CHECKED_AT, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_IP_REPUTATION, boolQuery, sort);
    }

    @Override
    public List<IpReputation> getVpnIps() {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_IS_VPN, true).build()
        ));

        Sort sort = new Sort(List.of(new FieldSort(Constant.COLUMN_NAME_LAST_CHECKED_AT, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_IP_REPUTATION, boolQuery, sort);
    }

    @Override
    public List<IpReputation> getTorExitNodeIps() {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_IS_TOR_EXIT_NODE, true).build()
        ));

        Sort sort = new Sort(List.of(new FieldSort(Constant.COLUMN_NAME_LAST_CHECKED_AT, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_IP_REPUTATION, boolQuery, sort);
    }

    @Override
    public List<IpReputation> getDataCenterIps() {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_IS_DATA_CENTER, true).build()
        ));

        Sort sort = new Sort(List.of(new FieldSort(Constant.COLUMN_NAME_LAST_CHECKED_AT, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_IP_REPUTATION, boolQuery, sort);
    }

    @Override
    public List<IpReputation> getByCountryCode(String countryCode) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_COUNTRY_CODE, countryCode).build()
        ));

        Sort sort = new Sort(List.of(new FieldSort(Constant.COLUMN_NAME_LAST_CHECKED_AT, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_IP_REPUTATION, boolQuery, sort);
    }

    @Override
    public List<IpReputation> getByReputationScoreRange(Integer minScore, Integer maxScore) {
        RangeQuery rangeQuery = new RangeQuery();
        rangeQuery.setFieldName(Constant.COLUMN_NAME_REPUTATION_SCORE);
        rangeQuery.setFrom(ColumnValue.fromLong(minScore), true);
        rangeQuery.setTo(ColumnValue.fromLong(maxScore), true);

        Sort sort = new Sort(List.of(new FieldSort(Constant.COLUMN_NAME_REPUTATION_SCORE, SortOrder.DESC)));
        return searchAll(Constant.SEARCH_INDEX_IP_REPUTATION, rangeQuery, sort);
    }

    @Override
    public Boolean insert(IpReputation ipReputation) {
        if (ipReputation.getCreated() == null) {
            ipReputation.setCreated(System.currentTimeMillis());
        }
        if (ipReputation.getLastCheckedAt() == null) {
            ipReputation.setLastCheckedAt(System.currentTimeMillis());
        }
        ipReputation.setUpdatedAt(System.currentTimeMillis());
        return super.putRow(ipReputation);
    }

    @Override
    public Boolean update(IpReputation ipReputation) {
        ipReputation.setLastCheckedAt(System.currentTimeMillis());
        ipReputation.setUpdatedAt(System.currentTimeMillis());
        return super.putRow(ipReputation);
    }

    @Override
    public Boolean delete(String ipAddress) {
        IpReputation entity = new IpReputation();
        entity.setIpAddress(ipAddress);
        return super.deleteRow(entity);
    }

    @Override
    public Boolean batchInsert(List<IpReputation> ipReputations) {
        for (IpReputation ipReputation : ipReputations) {
            if (ipReputation.getCreated() == null) {
                ipReputation.setCreated(System.currentTimeMillis());
            }
            if (ipReputation.getLastCheckedAt() == null) {
                ipReputation.setLastCheckedAt(System.currentTimeMillis());
            }
            ipReputation.setUpdatedAt(System.currentTimeMillis());
        }
        return super.batchPutRow(ipReputations, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public Boolean isMaliciousIp(String ipAddress) {
        IpReputation ipReputation = getByIpAddress(ipAddress);
        if (ipReputation == null) {
            return false;
        }
        
        // 如果是代理、VPN、Tor出口节点或数据中心IP，或者信誉分数低于阈值，则认为是恶意IP
        return Boolean.TRUE.equals(ipReputation.getIsProxy()) ||
               Boolean.TRUE.equals(ipReputation.getIsVpn()) ||
               Boolean.TRUE.equals(ipReputation.getIsTorExitNode()) ||
               Boolean.TRUE.equals(ipReputation.getIsDataCenter()) ||
               (ipReputation.getReputationScore() != null && ipReputation.getReputationScore() < 50);
    }

    @Override
    public List<IpReputation> getIpsNeedingUpdate(Long threshold) {
        RangeQuery rangeQuery = new RangeQuery();
        rangeQuery.setFieldName(Constant.COLUMN_NAME_LAST_CHECKED_AT);
        rangeQuery.setTo(ColumnValue.fromLong(System.currentTimeMillis() - threshold), true);

        Sort sort = new Sort(List.of(new FieldSort(Constant.COLUMN_NAME_LAST_CHECKED_AT, SortOrder.ASC)));

        return searchAll(Constant.SEARCH_INDEX_IP_REPUTATION, rangeQuery, sort);
    }

    /**
     * 搜索所有匹配的记录
     */
    private List<IpReputation> searchAll(String indexName, Query query, Sort sort) {
        return pageSearchQuery(query, sort, 0, 100, indexName).getRows();
    }

    /**
     * 搜索所有匹配的记录（BoolQuery版本）
     */
    private List<IpReputation> searchAll(String indexName, BoolQuery boolQuery, Sort sort) {
        return pageSearchQuery(boolQuery, sort, 0, 100, indexName).getRows();
    }
}
