package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.RexyBasketRecord;

import java.util.List;

public interface RexyBasketRecordBuilder {

    /**
     * 根据记录 ID 获取篮子更新记录信息。
     *
     * @param id 记录的唯一标识符。
     * @return 返回与指定记录 ID 对应的篮子更新记录对象。如果找不到对应的记录，则返回 null。
     */
    RexyBasketRecord getById(String id);

    /**
     * 插入一个新的篮子更新记录到数据库中。
     *
     * @param rexyBasketRecord 要插入的篮子更新记录对象
     * @return 如果插入成功，返回 true；否则返回 false
     */
    Boolean insert(RexyBasketRecord rexyBasketRecord);

    /**
     * 查询用户处理中的篮子记录
     *
     * @param customerId 用户ID
     * @param basketType 篮子类型
     * @param operateType 操作类型
     * @param status 状态
     * @return 处理中的记录列表最新一条
     */
    RexyBasketRecord getProcessingRecords(String customerId, String basketType, String operateType, Integer status);
}
