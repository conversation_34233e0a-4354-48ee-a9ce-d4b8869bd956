package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.CustomerRexyBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.CustomerRexy;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;

@Repository("customerRexyBuilder")
public class CustomerRexyBuilderImpl extends WideColumnStoreBuilder<CustomerRexy> implements CustomerRexyBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName() {
        // 使用常量替换写死的表名
        return Constant.TABLE_NAME_CUSTOMER_REXY;
    }

    @PostConstruct
    public void init() {
        super.init(CustomerRexy.class);
    }

    @Override
    public Boolean insert(CustomerRexy customerRexy) {
        return super.putRow(customerRexy);
    }

    @Override
    public Boolean delete(CustomerRexy customerRexy) {
       return super.deleteRow(customerRexy);
    }

    @Override
    public Boolean batchDelete(List<CustomerRexy> customerRexyList) {
        return super.batchDeleteRows(customerRexyList);
    }

    public Boolean updateStatus(CustomerRexy customerRexy) {
        return super.updateRow(customerRexy, Collections.singletonList(Constant.COLUMN_NAME_STATUS));
    }

    @Override
    public CustomerRexy getByCustomerAndRexyId(String customerId, String rexyId) {
        CustomerRexy entity = new CustomerRexy();
        entity.setCustomerId(customerId);
        entity.setRexyId(rexyId);
        return getRow(entity);
    }

    @Override
    public List<CustomerRexy> getByCustomerAndStatus(String customerId, String status) {
        BoolQuery.Builder filter = QueryBuilders.bool()
                .filter(QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId))
                .filter(QueryBuilders.term(Constant.COLUMN_NAME_STATUS, status));
        return search(filter.build(), null,0, 100, Constant.SEARCH_INDEX_CUSTOMER_REXY);
    }

    @Override
    public List<CustomerRexy> getByCustomer(String customerId) {
        BoolQuery query = QueryBuilders.bool()
                .filter(QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId))
                .build();

        return search(query, null, 0, 100, Constant.SEARCH_INDEX_CUSTOMER_REXY);
    }
}
