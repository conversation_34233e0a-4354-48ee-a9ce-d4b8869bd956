package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.RexyBasketRecordBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.RexyBasketRecord;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;

@Repository("rexyBasketRecordBuilder")
public class RexyBasketRecordBuilderImpl extends WideColumnStoreBuilder<RexyBasketRecord> implements RexyBasketRecordBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName(){
        return Constant.TABLE_NAME_REXY_BASKET_RECORD;
    }

    @PostConstruct
    public void init() {
        super.init(RexyBasketRecord.class);
    }

    @Override
    public RexyBasketRecord getById(String id) {
        RexyBasketRecord entity = new RexyBasketRecord();
        entity.setId(id);
        return getRow(entity);
    }

    @Override
    public Boolean insert(RexyBasketRecord rexyBasketRecord) {
        if(rexyBasketRecord.getId() == null){
            rexyBasketRecord.setId(idUtils.nextId());
        }
        return super.putRow(rexyBasketRecord);
    }

    @Override
    public RexyBasketRecord getProcessingRecords(String customerId, String basketType, String operateType, Integer status) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId))
                .must(QueryBuilders.term(Constant.COLUMN_NAME_BASKET_TYPE, basketType));
        if (operateType != null) {
            boolQuery.must(QueryBuilders.term(Constant.COLUMN_NAME_OPERATE_TYPE, operateType));
        }
        if (status != null) {
            boolQuery.must(QueryBuilders.term(Constant.COLUMN_NAME_STATUS, status));
        }
        return super.searchOne(boolQuery.build(), Constant.SEARCH_INDEX_REXY_BASKET_RECORD);
    }
}
