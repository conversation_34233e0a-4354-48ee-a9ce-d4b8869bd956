package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.NoticeBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.Notice;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Repository("noticeBuilder")
public class NoticeBuilderImpl extends WideColumnStoreBuilder<Notice> implements NoticeBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName() {
        return Constant.TABLE_NAME_NOTICE;
    }

    @PostConstruct
    public void init() {
        super.init(Notice.class);
    }

    @Override
    public Notice getById(String id) {
        Notice entity = new Notice();
        entity.setId(id);
        return getRow(entity);
    }

    @Override
    public Boolean insert(Notice notice) {
        if (notice.getId() == null) {
            notice.setId(idUtils.nextId());
        }
        if (notice.getCreated() == null) {
            notice.setCreated(System.currentTimeMillis());
        }
        notice.setModified(System.currentTimeMillis());
        return super.putRow(notice);
    }

    @Override
    public Boolean update(Notice notice) {
        notice.setModified(System.currentTimeMillis());
        return super.updateRow(notice);
    }

    @Override
    public Boolean delete(String id) {
        Notice entity = new Notice();
        entity.setId(id);
        return super.deleteRow(entity);
    }

    @Override
    public List<Notice> getByStatus(String status, Long beginTime) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_STATUS, status))
                .must(QueryBuilders.range(Constant.COLUMN_NAME_NOTIFY_TIME).greaterThan(beginTime))
                .must(QueryBuilders.range(Constant.COLUMN_NAME_NOTIFY_TIME).lessThanOrEqual(System.currentTimeMillis()));
        Sort sort = createDefaultSort();
        return search(boolQuery.build(), sort, 0, 100, Constant.SEARCH_INDEX_NOTICE_INDEX);
    }

    /**
     * 创建默认排序
     * 按通知时间倒序排序
     */
    private Sort createDefaultSort() {
        List<Sort.Sorter> sorters = new ArrayList<>();
        // 按通知时间倒序
        sorters.add(new FieldSort(Constant.COLUMN_NAME_NOTIFY_TIME, SortOrder.ASC));
        return new Sort(sorters);
    }
}
