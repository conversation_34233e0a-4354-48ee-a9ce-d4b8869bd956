package com.drex.core.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户指纹表
 * 存储用户的浏览器/设备指纹信息
 */
@Table(name = Constant.TABLE_NAME_USER_FINGERPRINT)
@Data
public class UserFingerprint implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_ID, type = PartitionKey.Type.STRING)
    private String id;

    @Column(name = Constant.COLUMN_NAME_CUSTOMER_ID, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_USER_FINGERPRINT, column = Constant.COLUMN_NAME_CUSTOMER_ID)
    private String customerId;

    @Column(name = Constant.COLUMN_NAME_FINGERPRINT_HASH, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_USER_FINGERPRINT, column = Constant.COLUMN_NAME_FINGERPRINT_HASH)
    private String fingerprintHash;

    @Column(name = Constant.COLUMN_NAME_ATTRIBUTES, isDefined = true)
    private String attributes;

    @Column(name = Constant.COLUMN_NAME_IP_ADDRESS, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_USER_FINGERPRINT, column = Constant.COLUMN_NAME_IP_ADDRESS)
    private String ipAddress;

    @Column(name = Constant.COLUMN_NAME_FIRST_SEEN_AT, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_USER_FINGERPRINT, column = Constant.COLUMN_NAME_FIRST_SEEN_AT, fieldType = FieldType.LONG)
    private Long firstSeenAt;

    @Column(name = Constant.COLUMN_NAME_LAST_SEEN_AT, isDefined = true)
    private Long lastSeenAt;

    @Column(name = Constant.COLUMN_NAME_ASSOCIATED_USER_IDS, isDefined = true)
    private String associatedUserIds;

    @Column(name = Constant.COLUMN_NAME_IS_SUSPICIOUS, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_USER_FINGERPRINT, column = Constant.COLUMN_NAME_IS_SUSPICIOUS)
    private Boolean isSuspicious;

    @Column(name = Constant.COLUMN_NAME_CREATED, isDefined = true)
    private Long created;

    @Column(name = Constant.COLUMN_NAME_MODIFIED, isDefined = true)
    private Long modified;
}
