package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.FraudIndicatorLog;

import java.util.List;

/**
 * 欺诈指标日志数据访问接口
 */
public interface FraudIndicatorLogBuilder {

    /**
     * 根据ID获取欺诈指标日志
     *
     * @param id 日志ID
     * @return 欺诈指标日志
     */
    FraudIndicatorLog getById(String id);

    /**
     * 根据会话ID获取欺诈指标日志列表
     *
     * @param sessionId 会话ID
     * @return 欺诈指标日志列表
     */
    List<FraudIndicatorLog> getBySessionId(String sessionId);

    /**
     * 根据用户ID获取欺诈指标日志列表
     *
     * @param customerId 用户ID
     * @return 欺诈指标日志列表
     */
    List<FraudIndicatorLog> getByCustomerId(String customerId);

    /**
     * 根据指标类型获取欺诈指标日志列表
     *
     * @param indicatorType 指标类型
     * @return 欺诈指标日志列表
     */
    List<FraudIndicatorLog> getByIndicatorType(String indicatorType);

    /**
     * 根据会话ID和指标类型获取欺诈指标日志列表
     *
     * @param sessionId 会话ID
     * @param indicatorType 指标类型
     * @return 欺诈指标日志列表
     */
    List<FraudIndicatorLog> getBySessionIdAndIndicatorType(String sessionId, String indicatorType);

    /**
     * 插入欺诈指标日志
     *
     * @param log 欺诈指标日志
     * @return 是否成功
     */
    Boolean insert(FraudIndicatorLog log);

    /**
     * 批量插入欺诈指标日志
     *
     * @param logs 欺诈指标日志列表
     * @return 是否成功
     */
    Boolean batchInsert(List<FraudIndicatorLog> logs);

    /**
     * 删除欺诈指标日志
     *
     * @param id 日志ID
     * @return 是否成功
     */
    Boolean delete(String id);

    /**
     * 根据时间范围获取欺诈指标日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 欺诈指标日志列表
     */
    List<FraudIndicatorLog> getByTimeRange(Long startTime, Long endTime);
}
