package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.IpReputation;

import java.util.List;

/**
 * IP信誉数据访问接口
 */
public interface IpReputationBuilder {

    /**
     * 根据IP地址获取信誉信息
     *
     * @param ipAddress IP地址
     * @return IP信誉信息
     */
    IpReputation getByIpAddress(String ipAddress);

    /**
     * 获取所有代理IP列表
     *
     * @return 代理IP列表
     */
    List<IpReputation> getProxyIps();

    /**
     * 获取所有VPN IP列表
     *
     * @return VPN IP列表
     */
    List<IpReputation> getVpnIps();

    /**
     * 获取所有Tor出口节点IP列表
     *
     * @return Tor出口节点IP列表
     */
    List<IpReputation> getTorExitNodeIps();

    /**
     * 获取所有数据中心IP列表
     *
     * @return 数据中心IP列表
     */
    List<IpReputation> getDataCenterIps();

    /**
     * 根据国家代码获取IP列表
     *
     * @param countryCode 国家代码
     * @return IP列表
     */
    List<IpReputation> getByCountryCode(String countryCode);

    /**
     * 根据信誉分数范围获取IP列表
     *
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @return IP列表
     */
    List<IpReputation> getByReputationScoreRange(Integer minScore, Integer maxScore);

    /**
     * 插入IP信誉信息
     *
     * @param ipReputation IP信誉信息
     * @return 是否成功
     */
    Boolean insert(IpReputation ipReputation);

    /**
     * 更新IP信誉信息
     *
     * @param ipReputation IP信誉信息
     * @return 是否成功
     */
    Boolean update(IpReputation ipReputation);

    /**
     * 删除IP信誉信息
     *
     * @param ipAddress IP地址
     * @return 是否成功
     */
    Boolean delete(String ipAddress);

    /**
     * 批量插入IP信誉信息
     *
     * @param ipReputations IP信誉信息列表
     * @return 是否成功
     */
    Boolean batchInsert(List<IpReputation> ipReputations);

    /**
     * 检查IP是否为恶意IP
     *
     * @param ipAddress IP地址
     * @return 是否为恶意IP
     */
    Boolean isMaliciousIp(String ipAddress);

    /**
     * 获取需要更新的IP列表（基于最后检查时间）
     *
     * @param threshold 时间阈值（毫秒）
     * @return 需要更新的IP列表
     */
    List<IpReputation> getIpsNeedingUpdate(Long threshold);
}
