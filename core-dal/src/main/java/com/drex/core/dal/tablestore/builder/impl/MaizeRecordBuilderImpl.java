package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.random.R;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class MaizeRecordBuilderImpl extends WideColumnStoreBuilder<MaizeRecord> implements MaizeRecordBuilder {

    @Resource
    private IdUtils idUtils;

    @PostConstruct
    public void init() {
        super.init(MaizeRecord.class);
    }

    @Override
    public String tableName() {
        return "maize_record";
    }

    @Override
    public boolean save(MaizeRecord maizeRecord) {
        if(maizeRecord.getId() == null){
            maizeRecord.setId(idUtils.nextId());
        }
        return super.putRow(maizeRecord, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public MaizeRecord findMaizeRecords(String customerId, String socialEvent, String socialContentId) {
        List<RangeQueryParameter> queryParameters  = new ArrayList();
        queryParameters.add(new RangeQueryParameter("social_content_id", PrimaryKeyValue.fromString(socialContentId)));
        queryParameters.add(new RangeQueryParameter("social_event", PrimaryKeyValue.fromString(socialEvent)));
        queryParameters.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.fromString(customerId)));
        queryParameters.add(new RangeQueryParameter("id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQueryOne(MaizeRecord.IDX_MAIZE_RECORD, queryParameters);
    }
}
