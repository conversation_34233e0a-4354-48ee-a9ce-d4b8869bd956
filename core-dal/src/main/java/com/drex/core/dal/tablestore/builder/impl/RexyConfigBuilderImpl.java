package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.RexyConfigBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.RexyConfig;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Repository("rexyConfigBuilder")
public class RexyConfigBuilderImpl extends WideColumnStoreBuilder<RexyConfig> implements RexyConfigBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName(){
        // 使用常量替换写死的表名
        return Constant.TABLE_NAME_REXY_CONFIG;
    }

    @PostConstruct
    public void init() {
        super.init(RexyConfig.class);
    }

    @Override
    public RexyConfig getDefaultByLevel(String level) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term(Constant.COLUMN_NAME_LEVEL, level))
                .must(QueryBuilders.term(Constant.COLUMN_NAME_IS_DEFAULT, true));

        return super.searchOne(boolQuery.build(), Constant.SEARCH_INDEX_SEARCH_LEVEL_INDEX);
    }

    @Override
    public RexyConfig getById(String id) {
        RexyConfig entity = new RexyConfig();
        entity.setId(id);
        return getRow(entity);
    }

    @Override
    public Boolean insert(RexyConfig rexyConfig) {
        if (rexyConfig.getId() == null) {
            rexyConfig.setId(idUtils.nextId());
        }
        return super.putRow(rexyConfig);
    }

    @Override
    public Boolean deleteById(String id) {
        RexyConfig rexyConfig = new  RexyConfig();
        rexyConfig.setId(id);
        return super.deleteRow(rexyConfig);
    }

    @Override
    public List<RexyConfig> listAll() {
        List<RangeQueryParameter> parameters = new ArrayList<>();
        parameters.add(new RangeQueryParameter("id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQuery(parameters);
    }
}
