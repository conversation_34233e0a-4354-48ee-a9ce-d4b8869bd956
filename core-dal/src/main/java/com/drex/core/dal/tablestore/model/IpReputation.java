package com.drex.core.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

/**
 * IP信誉表
 * 存储IP地址的信誉信息
 */
@Table(name = Constant.TABLE_NAME_IP_REPUTATION)
@Data
public class IpReputation implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_IP_ADDRESS, type = PartitionKey.Type.STRING)
    private String ipAddress;

    @Column(name = Constant.COLUMN_NAME_REPUTATION_SCORE, isDefined = true)
    private Integer reputationScore;

    @Column(name = Constant.COLUMN_NAME_IS_PROXY, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_IP_REPUTATION, column = Constant.COLUMN_NAME_IS_PROXY)
    private Boolean isProxy;

    @Column(name = Constant.COLUMN_NAME_IS_VPN, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_IP_REPUTATION, column = Constant.COLUMN_NAME_IS_VPN)
    private Boolean isVpn;

    @Column(name = Constant.COLUMN_NAME_IS_TOR_EXIT_NODE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_IP_REPUTATION, column = Constant.COLUMN_NAME_IS_TOR_EXIT_NODE)
    private Boolean isTorExitNode;

    @Column(name = Constant.COLUMN_NAME_IS_DATA_CENTER, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_IP_REPUTATION, column = Constant.COLUMN_NAME_IS_DATA_CENTER)
    private Boolean isDataCenter;

    @Column(name = Constant.COLUMN_NAME_COUNTRY_CODE, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_IP_REPUTATION, column = Constant.COLUMN_NAME_COUNTRY_CODE)
    private String countryCode;

    @Column(name = Constant.COLUMN_NAME_LAST_CHECKED_AT, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_IP_REPUTATION, column = Constant.COLUMN_NAME_LAST_CHECKED_AT, fieldType = FieldType.LONG)
    private Long lastCheckedAt;

    @Column(name = Constant.COLUMN_NAME_SOURCE, isDefined = true)
    private String source;

    @Column(name = Constant.COLUMN_NAME_CREATED, isDefined = true)
    private Long created;

    @Column(name = Constant.COLUMN_NAME_UPDATED_AT, isDefined = true)
    private Long updatedAt;
}
