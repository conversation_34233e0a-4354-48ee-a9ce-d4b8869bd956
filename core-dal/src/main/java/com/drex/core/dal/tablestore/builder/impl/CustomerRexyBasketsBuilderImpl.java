package com.drex.core.dal.tablestore.builder.impl;

import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.CustomerRexyBasketsBuilder;
import com.drex.core.dal.tablestore.builder.CustomerRexyBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.CustomerRexy;
import com.drex.core.dal.tablestore.model.CustomerRexyBaskets;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.List;

@Repository("customerRexyBasketsBuilder")
public class CustomerRexyBasketsBuilderImpl extends WideColumnStoreBuilder<CustomerRexyBaskets> implements CustomerRexyBasketsBuilder {

    @Autowired
    private IdUtils idUtils;
    @Resource
    private CustomerRexyBuilder customerRexyBuilder;

    public String getTableName(){
        // 使用常量替换写死的表名
        return Constant.TABLE_NAME_CUSTOMER_REXY_BASKETS;
    }

    @PostConstruct
    public void init() {
        super.init(CustomerRexyBaskets.class);
    }

    @Override
    public CustomerRexyBaskets getByCustomerId(String customerId, String basketType) {
        CustomerRexyBaskets entity = new CustomerRexyBaskets();
        entity.setCustomerId(customerId);
        entity.setBasketType(basketType);
        return getRow(entity);
    }

    @Override
    public Boolean insert(CustomerRexyBaskets customerRexyBaskets) {
        return super.putRow(customerRexyBaskets);
    }

    @Override
    public Boolean update(CustomerRexyBaskets customerRexyBaskets) {
        return super.updateRow(customerRexyBaskets);
    }
}
