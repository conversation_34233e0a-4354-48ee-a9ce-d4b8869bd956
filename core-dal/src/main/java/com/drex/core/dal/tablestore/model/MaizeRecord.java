package com.drex.core.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;

@Data
@Table(name = "maize_record")
public class MaizeRecord implements Serializable {

    public static final String IDX_MAIZE_RECORD = "idx_maize_record";

    @PartitionKey(name = "id")
    @Index(name = IDX_MAIZE_RECORD, pkColumn = "id", pkValue = 3)
    private String id;

    @PartitionKey(name = "customer_id")
    @Index(name = IDX_MAIZE_RECORD, pkColumn = "customer_id", pkValue = 2)
    private String customerId;

    @Column(name = "social_platform", isDefined = true)
    private String socialPlatform;

    @Column(name = "social_event", isDefined = true)
    @Index(name = IDX_MAIZE_RECORD, pkColumn = "social_event", pkValue = 1)
    private String socialEvent;

    @Column(name = "social_content_id", isDefined = true)
    @Index(name = IDX_MAIZE_RECORD, pkColumn = "social_content_id")
    private String socialContentId;

    @Column(name = "maize_code", isDefined = true)
    private String maizeCode;

    @Column(name = "maize_level", isDefined = true)
    private String maizeLevel;

    @Column(name = "maize_score", isDefined = true, type = Column.Type.INTEGER)
    private Long maizeScore;

    @Column(name = "session_id", isDefined = true)
    private String sessionId;

    @Column(name = "progress", isDefined = true, type = Column.Type.INTEGER)
    private Integer progress;

    private String collectStatus;

    @Column(name = "create_time", isDefined = true, type = Column.Type.INTEGER)
    private Long createTime;

}
