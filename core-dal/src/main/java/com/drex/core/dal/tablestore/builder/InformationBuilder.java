package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.Information;

import java.util.List;

public interface InformationBuilder {

    /**
     * 根据ID获取信息
     *
     * @param id 信息的唯一标识符
     * @return 返回与指定ID对应的信息对象。如果找不到对应的信息，则返回null
     */
    Information getById(String id);

    /**
     * 插入一条新的信息记录
     *
     * @param information 要插入的信息对象
     * @return 如果插入成功，返回true；否则返回false
     */
    Boolean insert(Information information);

    /**
     * 更新信息记录
     *
     * @param information 要更新的信息对象
     * @return 如果更新成功，返回true；否则返回false
     */
    Boolean update(Information information);

    /**
     * 删除信息记录
     *
     * @param id 要删除的信息ID
     * @return 如果删除成功，返回true；否则返回false
     */
    Boolean delete(String id);

    /**
     * 根据类型获取信息列表
     *
     * @param type 信息类型
     * @return 返回指定类型的信息列表
     */
    List<Information> getByType(String type);

    /**
     * 获取推荐信息列表
     *
     * @param isRecommend 是否推荐
     * @return 返回推荐的信息列表
     */
    List<Information> getByRecommend(Boolean isRecommend, String position);

    /**
     * 根据类型和推荐状态获取信息列表
     *
     * @param type 信息类型
     * @param isRecommend 是否推荐
     * @return 返回符合条件的信息列表
     */
    List<Information> getByTypeAndRecommend(String type, Boolean isRecommend);

    /**
     * 获取所有信息列表
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 返回所有的信息列表
     */
    List<Information> listAll(int offset, int limit, String position);
}
