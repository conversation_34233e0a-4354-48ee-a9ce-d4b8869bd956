package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.VideoViewingSession;

import java.util.List;

/**
 * 视频观看会话数据访问接口
 */
public interface VideoViewingSessionBuilder {

    /**
     * 根据会话ID获取会话信息
     *
     * @param sessionId 会话ID
     * @return 会话信息
     */
    VideoViewingSession getBySessionId(String sessionId);

    /**
     * 根据用户ID和视频ID获取会话信息
     *
     * @param customerId 用户ID
     * @param videoId 视频ID
     * @return 会话信息列表
     */
    List<VideoViewingSession> getByCustomerIdAndVideoId(String customerId, String videoId);

    /**
     * 根据用户ID获取会话信息列表
     *
     * @param customerId 用户ID
     * @return 会话信息列表
     */
    List<VideoViewingSession> getByCustomerId(String customerId);

    /**
     * 根据会话状态获取会话信息列表
     *
     * @param status 会话状态
     * @return 会话信息列表
     */
    List<VideoViewingSession> getByStatus(String status);

    /**
     * 插入会话信息
     *
     * @param session 会话信息
     * @return 是否成功
     */
    Boolean insert(VideoViewingSession session);

    /**
     * 更新会话信息
     *
     * @param session 会话信息
     * @return 是否成功
     */
    Boolean update(VideoViewingSession session);

    /**
     * 删除会话信息
     *
     * @param sessionId 会话ID
     * @return 是否成功
     */
    Boolean delete(String sessionId);

    /**
     * 检查用户是否已经对该视频获得过奖励
     *
     * @param customerId 用户ID
     * @param videoId 视频ID
     * @return 是否已获得奖励
     */
    Boolean hasRewardGranted(String customerId, String videoId);
}
