package com.drex.core.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

@Table(name = Constant.TABLE_NAME_REXY_CONFIG)
@Data
public class RexyConfig implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_ID)
    private String id;

    @Column(name = Constant.COLUMN_NAME_NAME, isDefined = true)
    private String name;

    @Column(name = Constant.COLUMN_NAME_LEVEL, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_SEARCH_LEVEL_INDEX, column = Constant.COLUMN_NAME_LEVEL)
    private String level;

    @Column(name = Constant.COLUMN_NAME_RATE, isDefined = true)
    private Integer rate;

    @Column(name = Constant.COLUMN_NAME_LIMIT, isDefined = true)
    private Integer limit;

    @Column(name = Constant.COLUMN_NAME_EFFECTIVE_TIME, isDefined = true)
    private Long effectiveTime;

    @Column(name = Constant.COLUMN_NAME_EXPIRATION_TIME, isDefined = true)
    private Long expirationTime;

    @Column(name = Constant.COLUMN_NAME_IS_DEFAULT, isDefined = true, defaultValue = "false", type = Column.Type.BOOLEAN)
    @SearchIndex(name = Constant.SEARCH_INDEX_SEARCH_LEVEL_INDEX, column = Constant.COLUMN_NAME_IS_DEFAULT, fieldType = FieldType.BOOLEAN)
    private Boolean isDefault;

    @Column(name = Constant.COLUMN_NAME_AVATAR)
    private String avatar;

    @Column(name = Constant.COLUMN_NAME_MINI_AVATAR)
    private String miniAvatar;

    @Column(name = Constant.COLUMN_NAME_CIRCLE_AVATAR)
    private String circleAvatar;
}
