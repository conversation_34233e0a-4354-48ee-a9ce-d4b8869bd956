package com.drex.core.service.business.youtube;

import com.drex.core.service.cache.model.SessionEvent;

import java.util.List;
import java.util.Map;

/**
 * 异步事件处理服务接口
 * 负责后台异步处理事件数据和计算欺诈指标
 */
public interface AsyncEventProcessingService {

    /**
     * 异步处理事件数据
     * 
     * @param sessionId 会话ID
     * @param events 事件列表
     * @param videoDurationSeconds 视频时长
     */
    void processEventsAsync(String sessionId, List<SessionEvent> events, int videoDurationSeconds);

    /**
     * 异步计算并缓存欺诈指标
     * 
     * @param sessionId 会话ID
     * @param events 事件列表
     * @param videoDurationSeconds 视频时长
     */
    void calculateAndCacheFraudIndicators(String sessionId, List<SessionEvent> events, int videoDurationSeconds);

    /**
     * 异步更新会话状态
     * 
     * @param sessionId 会话ID
     * @param riskScore 风控分数
     * @param fraudIndicators 欺诈指标
     */
    void updateSessionStatusAsync(String sessionId, double riskScore, Map<String, Double> fraudIndicators);

    /**
     * 检查异步处理是否完成
     * 
     * @param sessionId 会话ID
     * @return 是否完成
     */
    boolean isProcessingComplete(String sessionId);

    /**
     * 获取异步处理进度
     * 
     * @param sessionId 会话ID
     * @return 处理进度 (0.0 - 1.0)
     */
    double getProcessingProgress(String sessionId);

    /**
     * 异步处理结果
     */
    class AsyncProcessingResult {
        private boolean success;
        private String errorMessage;
        private double riskScore;
        private Map<String, Double> fraudIndicators;
        private long processingTime;

        public AsyncProcessingResult(boolean success, double riskScore) {
            this.success = success;
            this.riskScore = riskScore;
            this.processingTime = System.currentTimeMillis();
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public double getRiskScore() { return riskScore; }
        public Map<String, Double> getFraudIndicators() { return fraudIndicators; }
        public void setFraudIndicators(Map<String, Double> fraudIndicators) { 
            this.fraudIndicators = fraudIndicators; 
        }
        public long getProcessingTime() { return processingTime; }
    }
}
