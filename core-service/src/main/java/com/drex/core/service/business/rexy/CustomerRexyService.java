package com.drex.core.service.business.rexy;

import com.drex.core.api.common.RexyConstant;
import com.drex.core.dal.tablestore.model.CustomerRexy;
import com.drex.customer.api.response.CustomerDTO;

import java.util.List;

/**
 *  用户持有恐龙服务
 */
public interface CustomerRexyService {

    void initCustomerRexy(CustomerDTO customerDTO);

    List<CustomerRexy> getByCustomerAndStatus(String customerId, RexyConstant.CustomerRexyStatus status);

    List<CustomerRexy> getByCustomer(String customerId);

    //更改用户持有恐龙
    Boolean updateCustomerRexyByLevel(CustomerDTO customerDTO, String kycLevel);
}
