package com.drex.core.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * YouTube奖励配置属性类
 * 从application.properties读取配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "youtube.reward")
public class YouTubeRewardProperties {

    /**
     * 进度1配置
     */
    private ProgressConfig progress1 = new ProgressConfig();

    /**
     * 进度2配置
     */
    private ProgressConfig progress2 = new ProgressConfig();

    /**
     * 进度3配置
     */
    private ProgressConfig progress3 = new ProgressConfig();

    /**
     * 进度配置
     */
    @Data
    public static class ProgressConfig {
        /**
         * 最小观看百分比
         */
        private Double minWatchPercentage = 0.0;

        /**
         * 最大观看百分比
         */
        private Double maxWatchPercentage = 1.0;

        /**
         * 奖励金额
         */
        private Long rewardAmount = 0L;

    }


    /**
     * 验证配置有效性
     */
    public boolean isValid() {
        return validateProgressConfig(progress1) && 
               validateProgressConfig(progress2) && 
               validateProgressConfig(progress3);
    }

    private boolean validateProgressConfig(ProgressConfig config) {
        return config.getMinWatchPercentage() != null &&
               config.getMaxWatchPercentage() != null &&
               config.getMinWatchPercentage() <= config.getMaxWatchPercentage();
    }
}
