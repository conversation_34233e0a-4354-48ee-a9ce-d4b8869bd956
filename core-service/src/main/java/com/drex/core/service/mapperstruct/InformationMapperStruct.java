package com.drex.core.service.mapperstruct;

import com.drex.core.api.response.InformationDTO;
import com.drex.core.dal.tablestore.model.Information;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface InformationMapperStruct {

    /**
     * Information 转 InformationDTO
     *
     * @param information Information实体
     * @return InformationDTO
     */
    InformationDTO toInformationDTO(Information information);

    /**
     * InformationDTO 转 Information
     *
     * @param informationDTO InformationDTO
     * @return Information实体
     */
    Information toInformation(InformationDTO informationDTO);

    /**
     * Information列表 转 InformationDTO列表
     *
     * @param informationList Information实体列表
     * @return InformationDTO列表
     */
    List<InformationDTO> toInformationDTOList(List<Information> informationList);
}
