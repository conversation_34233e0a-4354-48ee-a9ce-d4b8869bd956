package com.drex.core.service.mapperstruct;

import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.core.dal.tablestore.model.CustomerRexy;
import com.drex.core.dal.tablestore.model.CustomerRexyBaskets;
import com.drex.core.dal.tablestore.model.RexyConfig;
import com.drex.core.api.request.CustomerRexyDTO;
import com.drex.core.api.request.RexyConfigDTO;
import com.drex.customer.api.response.CustomerDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = IntegerToBigDecimalConverter.class)
public interface RexyMapperStruct {

    @Mapping(source = "customer.customerId", target = "customerId")
    @Mapping(source = "rexyConfig.id", target = "rexyId")
    @Mapping(source = "rexyConfig.name", target = "rexyName")
    @Mapping(source = "rexyConfig.level", target = "rexyLevel")
    @Mapping(source = "rexyConfig.rate", target = "rexyRate")
    @Mapping(source = "rexyConfig.limit", target = "rexyBasketLimit")
    @Mapping(source = "rexyConfig.circleAvatar", target = "rexyAvatar")
    @Mapping(target = "created", expression = "java(java.time.LocalDateTime.now().toEpochSecond( java.time.ZoneOffset.UTC))")
    CustomerRexy toCustomerRexy(CustomerDTO customer, RexyConfigDTO rexyConfig);

    CustomerRexyDTO toCustomerRexyDTO(CustomerRexy customerRexy);

    RexyConfigDTO toRexyConfigDTO(RexyConfig rexyConfig);

    List<RexyConfigDTO> toRexyConfigDTOList(List<RexyConfig> rexyConfigList);

    RexyConfig toRexyConfig(RexyConfigDTO rexyConfigDTO);

    @Mapping(source = "customerRexy.rexyRate", target = "basketRate")
    @Mapping(source = "customerRexy.customerId", target = "customerId")
    @Mapping(source = "customerRexy.rexyBasketLimit", target = "basketLimit")
    @Mapping(source = "customerRexy.rexyAvatar", target = "rexyAvatar")
    CustomerRexyBasketsDTO toCustomerRexyBasketsDTO(CustomerRexyBaskets customerRexyBaskets, CustomerRexy customerRexy);

    CustomerRexyBaskets toCustomerRexyBaskets(CustomerRexyBasketsDTO customerRexyBasketsDTO);
}
