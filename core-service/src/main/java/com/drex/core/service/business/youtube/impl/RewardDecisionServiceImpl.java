package com.drex.core.service.business.youtube.impl;

import com.drex.core.api.request.SocialEventRequest;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.model.youtube.FraudIndicatorThresholds;
import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
import com.drex.core.service.business.youtube.RewardDecisionService;
import com.drex.core.service.business.youtube.SystemConfigurationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 奖励决策服务实现
 */
@Slf4j
@Service
public class RewardDecisionServiceImpl implements RewardDecisionService {

    @Autowired
    private SystemConfigurationService systemConfigurationService;

    // 奖励代码缓存 (实际应该使用Redis)
    private final Map<String, RewardCodeInfo> rewardCodeCache = new HashMap<>();

    @Override
    public RewardEligibilityResult evaluateRewardEligibility(SocialEventRequest request,
                                                           VideoViewingSession session,
                                                           double trustScore,
                                                           Map<String, Double> fraudIndicators) {
        try {
            log.info("Evaluating reward eligibility for session: {}, trustScore: {}", 
                    request.getSessionId(), trustScore);

            // 1. 检查业务规则
            BusinessRuleCheckResult businessRuleResult = checkBusinessRules(request, session);
            
            // 2. 检查欺诈检测
            FraudCheckResult fraudCheckResult = checkFraudDetection(trustScore, fraudIndicators);
            
            // 3. 检查奖励限制
            RewardLimitCheckResult limitResult = checkRewardLimits(
                    request.getCustomerId(), request.getVideoId(), request.getRewardStage());

            // 4. 综合评估
            boolean eligible = businessRuleResult.isPassed() && 
                             fraudCheckResult.isPassed() && 
                             limitResult.isWithinLimits();

            String reason = eligible ? "Eligible for reward" : 
                    buildFailureReason(businessRuleResult, fraudCheckResult, limitResult);

            double confidenceScore = calculateConfidenceScore(businessRuleResult, fraudCheckResult, trustScore);

            RewardEligibilityResult result = new RewardEligibilityResult(eligible, reason, confidenceScore);
            result.setBusinessRuleResult(businessRuleResult);
            result.setFraudCheckResult(fraudCheckResult);

            Map<String, Object> evaluationDetails = new HashMap<>();
            evaluationDetails.put("trustScore", trustScore);
            evaluationDetails.put("rewardStage", request.getRewardStage());
            evaluationDetails.put("watchProgress", request.getWatchProgress());
            evaluationDetails.put("evaluationTime", System.currentTimeMillis());
            result.setEvaluationDetails(evaluationDetails);

            return result;

        } catch (Exception e) {
            log.error("Failed to evaluate reward eligibility", e);
            return new RewardEligibilityResult(false, "Evaluation error occurred", 0.0);
        }
    }

    @Override
    public RewardCodeGenerationResult generateRewardCode(String customerId, String sessionId, String rewardStage) {
        try {
            // 生成唯一的奖励代码
            String rewardCode = generateUniqueRewardCode(customerId, sessionId, rewardStage);
            long expirationTime = System.currentTimeMillis() + (30 * 60 * 1000); // 30分钟过期

            // 缓存奖励代码
            RewardCodeInfo codeInfo = new RewardCodeInfo(rewardCode, customerId, sessionId, 
                    rewardStage, expirationTime, false);
            rewardCodeCache.put(rewardCode, codeInfo);

            RewardCodeGenerationResult result = new RewardCodeGenerationResult(true, rewardCode, expirationTime);
            
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("customerId", customerId);
            metadata.put("sessionId", sessionId);
            metadata.put("rewardStage", rewardStage);
            metadata.put("generationTime", System.currentTimeMillis());
            result.setCodeMetadata(metadata);

            log.info("Generated reward code: {} for user: {}, session: {}", rewardCode, customerId, sessionId);
            return result;

        } catch (Exception e) {
            log.error("Failed to generate reward code", e);
            RewardCodeGenerationResult result = new RewardCodeGenerationResult(false, null, 0);
            result.setErrorMessage("Failed to generate reward code: " + e.getMessage());
            return result;
        }
    }

    @Override
    public RewardCodeValidationResult validateRewardCode(String rewardCode, String customerId, String sessionId) {
        try {
            RewardCodeInfo codeInfo = rewardCodeCache.get(rewardCode);
            
            if (codeInfo == null) {
                return new RewardCodeValidationResult(false, false, false);
            }

            boolean expired = System.currentTimeMillis() > codeInfo.expirationTime;
            boolean alreadyUsed = codeInfo.used;
            boolean valid = !expired && !alreadyUsed && 
                          customerId.equals(codeInfo.customerId) && 
                          sessionId.equals(codeInfo.sessionId);

            RewardCodeValidationResult result = new RewardCodeValidationResult(valid, expired, alreadyUsed);
            
            if (!valid) {
                if (expired) {
                    result.setErrorMessage("Reward code has expired");
                } else if (alreadyUsed) {
                    result.setErrorMessage("Reward code has already been used");
                } else {
                    result.setErrorMessage("Invalid reward code or user mismatch");
                }
            }

            Map<String, Object> details = new HashMap<>();
            details.put("rewardStage", codeInfo.rewardStage);
            details.put("expirationTime", codeInfo.expirationTime);
            details.put("validationTime", System.currentTimeMillis());
            result.setCodeDetails(details);

            return result;

        } catch (Exception e) {
            log.error("Failed to validate reward code: {}", rewardCode, e);
            RewardCodeValidationResult result = new RewardCodeValidationResult(false, false, false);
            result.setErrorMessage("Validation error occurred");
            return result;
        }
    }

    @Override
    public RewardAmountCalculationResult calculateRewardAmount(String rewardStage, 
                                                             double trustScore, 
                                                             double watchPercentage) {
        try {
            RewardConfiguration config = getRewardConfiguration(rewardStage);
            String baseAmount = config.getBaseAmount();
            
            // 计算奖励倍数（基于信任分数和观看百分比）
            double multiplier = calculateRewardMultiplier(trustScore, watchPercentage);
            
            // 计算奖励金额
            double baseValue = Double.parseDouble(baseAmount);
            double bonusValue = baseValue * (multiplier - 1.0);
            double totalValue = baseValue * multiplier;

            String bonusAmount = String.format("%.2f", bonusValue);
            String totalAmount = String.format("%.2f", totalValue);

            RewardAmountCalculationResult result = new RewardAmountCalculationResult(
                    baseAmount, bonusAmount, totalAmount);
            result.setMultiplier(multiplier);

            Map<String, String> breakdown = new HashMap<>();
            breakdown.put("baseReward", baseAmount);
            breakdown.put("trustScoreBonus", String.format("%.2f", bonusValue * 0.6));
            breakdown.put("completionBonus", String.format("%.2f", bonusValue * 0.4));
            result.setAmountBreakdown(breakdown);

            String formula = String.format("Total = Base(%.2f) × Multiplier(%.2f) = %.2f", 
                    baseValue, multiplier, totalValue);
            result.setCalculationFormula(formula);

            return result;

        } catch (Exception e) {
            log.error("Failed to calculate reward amount", e);
            return new RewardAmountCalculationResult("0", "0", "0");
        }
    }

    @Override
    public BusinessRuleCheckResult checkBusinessRules(SocialEventRequest request, VideoViewingSession session) {
        try {
            List<String> failedRules = new ArrayList<>();
            
            // 检查观看进度
            double requiredProgress = getRequiredWatchProgress(request.getRewardStage());
            if (request.getWatchProgress() < requiredProgress) {
                failedRules.add("INSUFFICIENT_WATCH_PROGRESS");
            }

            // 检查有效观看时长
            int minEffectiveSeconds = (int) (session.getVideoDurationSeconds() * requiredProgress);
            if (request.getEffectiveWatchSeconds() < minEffectiveSeconds) {
                failedRules.add("INSUFFICIENT_EFFECTIVE_WATCH_TIME");
            }

            // 检查会话状态
            if (!YouTubeAntiCheatConstant.SessionStatus.IN_PROGRESS.getCode().equals(session.getSessionStatus())) {
                failedRules.add("INVALID_SESSION_STATUS");
            }

            boolean passed = failedRules.isEmpty();
            BusinessRuleCheckResult result = new BusinessRuleCheckResult(
                    passed, request.getWatchProgress(), request.getEffectiveWatchSeconds());
            result.setFailedRules(failedRules);

            Map<String, Object> ruleDetails = new HashMap<>();
            ruleDetails.put("requiredProgress", requiredProgress);
            ruleDetails.put("actualProgress", request.getWatchProgress());
            ruleDetails.put("minEffectiveSeconds", minEffectiveSeconds);
            ruleDetails.put("actualEffectiveSeconds", request.getEffectiveWatchSeconds());
            result.setRuleDetails(ruleDetails);

            return result;

        } catch (Exception e) {
            log.error("Failed to check business rules", e);
            BusinessRuleCheckResult result = new BusinessRuleCheckResult(false, 0.0, 0);
            result.setFailedRules(Arrays.asList("RULE_CHECK_ERROR"));
            return result;
        }
    }

    @Override
    public RewardLimitCheckResult checkRewardLimits(String customerId, String videoId, String rewardStage) {
        try {
            // 简化实现：检查每日奖励限制
            int currentCount = getCurrentDailyRewardCount(customerId);
            int maxAllowed = getMaxDailyRewards(rewardStage);
            
            boolean withinLimits = currentCount < maxAllowed;
            long resetTime = getNextDayResetTime();

            return new RewardLimitCheckResult(withinLimits, "DAILY_LIMIT", currentCount, maxAllowed);

        } catch (Exception e) {
            log.error("Failed to check reward limits", e);
            return new RewardLimitCheckResult(false, "LIMIT_CHECK_ERROR", 0, 0);
        }
    }

    @Override
    public boolean recordRewardDecision(String customerId, String sessionId, boolean decision, String reason) {
        try {
            // 这里应该记录到数据库，当前简化实现
            log.info("Recorded reward decision - Customer: {}, Session: {}, Decision: {}, Reason: {}", 
                    customerId, sessionId, decision, reason);
            return true;
        } catch (Exception e) {
            log.error("Failed to record reward decision", e);
            return false;
        }
    }

    @Override
    public RewardConfiguration getRewardConfiguration(String rewardStage) {
        List<SystemConfigurationService.RewardStageConfiguration> configs = 
                systemConfigurationService.getRewardConfigurations();
        
        for (SystemConfigurationService.RewardStageConfiguration config : configs) {
            if (rewardStage.equals(config.getStageCode())) {
                RewardConfiguration rewardConfig = new RewardConfiguration(
                        config.getStageName(), config.getBaseRewardAmount(), 
                        config.getRequiredWatchPercentage(), config.getMinTrustScore());
                rewardConfig.setMaxDailyRewards(config.getMaxDailyRewards());
                rewardConfig.setRewardCodeExpirationMinutes(config.getRewardCodeExpirationMinutes());
                return rewardConfig;
            }
        }
        
        // 默认配置
        return new RewardConfiguration(rewardStage, "100", 0.5, 0.3);
    }

    // 辅助方法
    private FraudCheckResult checkFraudDetection(double trustScore, Map<String, Double> fraudIndicators) {
        SystemConfigurationService.TrustScoreThresholds thresholds = 
                systemConfigurationService.getTrustScoreThresholds();
        
        boolean passed = trustScore >= thresholds.getRewardEligibilityThreshold();
        String riskLevel = determineRiskLevel(trustScore, thresholds);
        
        FraudCheckResult result = new FraudCheckResult(passed, trustScore, riskLevel);
        
        List<String> triggeredIndicators = new ArrayList<>();
        for (Map.Entry<String, Double> entry : fraudIndicators.entrySet()) {
            if (entry.getValue() > 0.5) { // 阈值可配置
                triggeredIndicators.add(entry.getKey());
            }
        }
        result.setTriggeredIndicators(triggeredIndicators);
        result.setIndicatorScores(fraudIndicators);
        
        return result;
    }

    private String buildFailureReason(BusinessRuleCheckResult businessRule, 
                                    FraudCheckResult fraudCheck, 
                                    RewardLimitCheckResult limitCheck) {
        List<String> reasons = new ArrayList<>();
        
        if (!businessRule.isPassed()) {
            reasons.add("Business rule failed: " + String.join(", ", businessRule.getFailedRules()));
        }
        if (!fraudCheck.isPassed()) {
            reasons.add("Fraud check failed: trust score " + fraudCheck.getTrustScore());
        }
        if (!limitCheck.isWithinLimits()) {
            reasons.add("Reward limit exceeded: " + limitCheck.getCurrentCount() + "/" + limitCheck.getMaxAllowed());
        }
        
        return String.join("; ", reasons);
    }

    private double calculateConfidenceScore(BusinessRuleCheckResult businessRule, 
                                          FraudCheckResult fraudCheck, 
                                          double trustScore) {
        double businessScore = businessRule.isPassed() ? 1.0 : 0.0;
        double fraudScore = fraudCheck.isPassed() ? 1.0 : 0.0;
        double trustScoreNormalized = Math.min(1.0, trustScore);
        
        return (businessScore * 0.3 + fraudScore * 0.4 + trustScoreNormalized * 0.3);
    }

    private String generateUniqueRewardCode(String customerId, String sessionId, String rewardStage) {
        return String.format("RWD_%s_%s_%s_%d", 
                rewardStage, customerId.substring(0, Math.min(8, customerId.length())), 
                sessionId.substring(0, Math.min(8, sessionId.length())), 
                System.currentTimeMillis() % 100000);
    }

    private double calculateRewardMultiplier(double trustScore, double watchPercentage) {
        double trustMultiplier = 0.5 + (trustScore * 0.5); // 0.5-1.0
        double watchMultiplier = 0.8 + (watchPercentage * 0.2); // 0.8-1.0
        return Math.min(2.0, trustMultiplier * watchMultiplier);
    }

    private double getRequiredWatchProgress(String rewardStage) {
        switch (rewardStage) {
            case "STAGE_1": return 0.25;
            case "STAGE_2": return 0.50;
            case "STAGE_3": return 0.75;
            default: return 0.50;
        }
    }

    private String determineRiskLevel(double trustScore, SystemConfigurationService.TrustScoreThresholds thresholds) {
        if (trustScore >= thresholds.getLowRiskThreshold()) return "LOW";
        if (trustScore >= thresholds.getMediumRiskThreshold()) return "MEDIUM";
        if (trustScore >= thresholds.getHighRiskThreshold()) return "HIGH";
        return "CRITICAL";
    }

    private int getCurrentDailyRewardCount(String customerId) {
        // 简化实现，实际应该查询数据库
        return 0;
    }

    private int getMaxDailyRewards(String rewardStage) {
        return 10; // 简化实现
    }

    private long getNextDayResetTime() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    // 内部类
    private static class RewardCodeInfo {
        final String rewardCode;
        final String customerId;
        final String sessionId;
        final String rewardStage;
        final long expirationTime;
        boolean used;

        RewardCodeInfo(String rewardCode, String customerId, String sessionId, 
                      String rewardStage, long expirationTime, boolean used) {
            this.rewardCode = rewardCode;
            this.customerId = customerId;
            this.sessionId = sessionId;
            this.rewardStage = rewardStage;
            this.expirationTime = expirationTime;
            this.used = used;
        }
    }
}
