package com.drex.core.service.business.youtube.worker;

import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.business.rexy.Base62Encoding;
import com.drex.core.service.business.youtube.RealtimeRewardService;
import com.drex.core.service.config.YouTubeRewardProperties;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Random;

/**
 * 奖励计算Worker
 * 负责生成奖励代码和积分
 */
@Slf4j
@Component
public class RewardCalculationWorker implements IWorker<RewardCalculationWorker.RewardCalculationParam, RealtimeRewardService.RewardGenerationResult>,
        ICallback<RewardCalculationWorker.RewardCalculationParam, RealtimeRewardService.RewardGenerationResult> {

    @Autowired
    private MaizeRecordBuilder maizeRecordBuilder;

    @Autowired
    private Base62Encoding base62Encoding;

    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;

    private final Random random = new Random();

    @Override
    public RealtimeRewardService.RewardGenerationResult action(RewardCalculationParam param, 
                                                              Map<String, WorkerWrapper> allWrappers) {
        try {
            log.debug("Calculating reward for session: {}, progress: {}, riskScore: {}", 
                    param.getSessionId(), param.getProgress(), param.getRiskScore());

            // 1. 获取奖励规则配置
            YouTubeRewardProperties.ProgressConfig progressConfig = youTubeRewardProperties.getProgressConfig(param.getProgress());
            if (progressConfig == null) {
                return new RealtimeRewardService.RewardGenerationResult(false, "Progress config not found for progress: " + param.getProgress());
            }

            // 2. 检查风险分数要求（风险分数越高，越不应该给奖励）
            //TODO: 判断RiskScore是否小于配置值


            // 3. 生成奖励代码
            String rewardCode = generateRewardCode();
            
            // 4. 计算奖励积分
            Long rewardScore = calculateRewardScore(progressConfig, trustScore);
            
            // 5. 保存奖励记录
            MaizeRecord maizeRecord = new MaizeRecord();
            maizeRecord.setCustomerId(param.getCustomerId());
            maizeRecord.setSocialEvent("watch");
            maizeRecord.setSocialPlatform("youtube");
            maizeRecord.setSocialContentId(param.getVideoId());
            maizeRecord.setMaizeCode(rewardCode);
            maizeRecord.setMaizeLevel(progressConfig.getRewardLevel());
            maizeRecord.setMaizeScore(rewardScore);
            maizeRecord.setCreateTime(System.currentTimeMillis());
            
            // 扩展字段存储sessionId和progress
            maizeRecord.setId(param.getSessionId() + "_" + param.getProgress()); // 使用复合ID确保唯一性
            
            boolean saved = maizeRecordBuilder.save(maizeRecord);
            if (!saved) {
                return new RealtimeRewardService.RewardGenerationResult(false, "Failed to save reward record");
            }

            // 6. 构建返回结果
            RealtimeRewardService.RewardGenerationResult result = new RealtimeRewardService.RewardGenerationResult(true, "Reward generated successfully");
            result.setRewardCode(rewardCode);
            result.setRewardScore(rewardScore);
            result.setRewardLevel(progressConfig.getRewardLevel());
            result.setExpirationTime(System.currentTimeMillis() + 
                    progressConfig.getRewardCodeExpirationMinutes() * 60 * 1000L);

            log.info("Reward generated successfully for session: {}, progress: {}, code: {}, score: {}", 
                    param.getSessionId(), param.getProgress(), rewardCode, rewardScore);

            return result;

        } catch (Exception e) {
            log.error("Failed to generate reward for session: {}, progress: {}", param.getSessionId(), param.getProgress(), e);
            return new RealtimeRewardService.RewardGenerationResult(false, "Failed to generate reward: " + e.getMessage());
        }
    }

    @Override
    public void begin() {
        log.debug("RewardCalculationWorker begin");
    }

    @Override
    public void result(boolean success, RewardCalculationParam param, WorkResult<RealtimeRewardService.RewardGenerationResult> result) {
        if (success) {
            log.debug("RewardCalculationWorker completed successfully for session: {}", param.getSessionId());
        } else {
            log.error("RewardCalculationWorker failed for session: {}", param.getSessionId());
        }
    }

    /**
     * 生成奖励代码
     */
    private String generateRewardCode() {
        return base62Encoding.encode(System.currentTimeMillis()) + 
               base62Encoding.encode(random.nextLong() & Long.MAX_VALUE);
    }

    /**
     * 计算奖励积分
     */
    private Long calculateRewardScore(YouTubeRewardProperties.ProgressConfig progressConfig, double trustScore) {

//        // 基于信任分数调整奖励积分
//        double multiplier = Math.min(1.5, Math.max(0.5, trustScore * 1.5));
//        long adjustedScore = Math.round(scoreRange.getBaseScore() * multiplier);
//
//        // 确保在范围内
//        return Math.max(progressConfig.getMinWatchPercentage(),
//                Math.min(scoreRange.getMaxScore(), adjustedScore));
        return 0L;
    }

    /**
     * 奖励计算参数
     */
    public static class RewardCalculationParam {
        private final String sessionId;
        private final String customerId;
        private final String videoId;
        private final Integer progress;
        private final double riskScore;

        public RewardCalculationParam(String sessionId, String customerId, String videoId, Integer progress, double riskScore) {
            this.sessionId = sessionId;
            this.customerId = customerId;
            this.videoId = videoId;
            this.progress = progress;
            this.riskScore = riskScore;
        }

        public String getSessionId() { return sessionId; }
        public String getCustomerId() { return customerId; }
        public String getVideoId() { return videoId; }
        public Integer getProgress() { return progress; }
        public double getRiskScore() { return riskScore; }
    }
}
