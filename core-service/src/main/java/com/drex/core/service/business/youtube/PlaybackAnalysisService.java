package com.drex.core.service.business.youtube;

import com.drex.core.service.cache.model.SessionEvent;

import java.util.List;
import java.util.Map;

/**
 * 播放分析服务接口
 * 负责分析播放速率、跳转行为和视频完成百分比等播放相关指标
 */
public interface PlaybackAnalysisService {

    /**
     * 分析播放速率是否异常
     *
     * @param events 事件列表
     * @return 播放速率分析结果
     */
    PlaybackRateAnalysisResult analyzePlaybackRate(List<SessionEvent> events);

    /**
     * 分析跳转行为
     *
     * @param events 事件列表
     * @return 跳转行为分析结果
     */
    SeekingBehaviorAnalysisResult analyzeSeekingBehavior(List<SessionEvent> events);

    /**
     * 计算视频完成百分比
     *
     * @param events 事件列表
     * @param videoDurationSeconds 视频总时长
     * @return 完成百分比分析结果
     */
    CompletionPercentageAnalysisResult analyzeCompletionPercentage(List<SessionEvent> events, 
                                                                  int videoDurationSeconds);

    /**
     * 计算有效观看时长
     * 基于播放到暂停的时间长度，减去失焦到聚焦的时间长度
     *
     * @param events 事件列表
     * @return 有效观看时长（秒）
     */
    int calculateEffectiveWatchTime(List<SessionEvent> events);

    /**
     * 分析播放模式
     *
     * @param events 事件列表
     * @return 播放模式分析结果
     */
    PlaybackPatternAnalysisResult analyzePlaybackPattern(List<SessionEvent> events);

    /**
     * 检测异常播放行为
     *
     * @param events 事件列表
     * @return 异常行为检测结果
     */
    AbnormalPlaybackDetectionResult detectAbnormalPlayback(List<SessionEvent> events);

    /**
     * 计算播放相关的欺诈指标
     *
     * @param events 事件列表
     * @param videoDurationSeconds 视频总时长
     * @return 欺诈指标映射 (指标类型 -> 指标值)
     */
    Map<String, Double> calculatePlaybackFraudIndicators(List<SessionEvent> events, 
                                                         int videoDurationSeconds);

    /**
     * 播放速率分析结果
     */
    class PlaybackRateAnalysisResult {
        private double averagePlaybackRate;
        private double maxPlaybackRate;
        private double minPlaybackRate;
        private int rateChangeCount;
        private boolean hasAbnormalRate;
        private double abnormalRatePercentage;
        private List<PlaybackRateEvent> rateEvents;

        public PlaybackRateAnalysisResult(double averagePlaybackRate, double maxPlaybackRate, 
                                        double minPlaybackRate, int rateChangeCount) {
            this.averagePlaybackRate = averagePlaybackRate;
            this.maxPlaybackRate = maxPlaybackRate;
            this.minPlaybackRate = minPlaybackRate;
            this.rateChangeCount = rateChangeCount;
        }

        // Getters and setters
        public double getAveragePlaybackRate() { return averagePlaybackRate; }
        public double getMaxPlaybackRate() { return maxPlaybackRate; }
        public double getMinPlaybackRate() { return minPlaybackRate; }
        public int getRateChangeCount() { return rateChangeCount; }
        public boolean isHasAbnormalRate() { return hasAbnormalRate; }
        public void setHasAbnormalRate(boolean hasAbnormalRate) { this.hasAbnormalRate = hasAbnormalRate; }
        public double getAbnormalRatePercentage() { return abnormalRatePercentage; }
        public void setAbnormalRatePercentage(double abnormalRatePercentage) { 
            this.abnormalRatePercentage = abnormalRatePercentage; 
        }
        public List<PlaybackRateEvent> getRateEvents() { return rateEvents; }
        public void setRateEvents(List<PlaybackRateEvent> rateEvents) { this.rateEvents = rateEvents; }
    }

    /**
     * 跳转行为分析结果
     */
    class SeekingBehaviorAnalysisResult {
        private int totalSeekCount;
        private double averageSeekDistance;
        private int forwardSeekCount;
        private int backwardSeekCount;
        private boolean hasAbnormalSeeking;
        private double seekFrequency; // 每分钟跳转次数
        private List<SeekEvent> seekEvents;

        public SeekingBehaviorAnalysisResult(int totalSeekCount, double averageSeekDistance) {
            this.totalSeekCount = totalSeekCount;
            this.averageSeekDistance = averageSeekDistance;
        }

        // Getters and setters
        public int getTotalSeekCount() { return totalSeekCount; }
        public double getAverageSeekDistance() { return averageSeekDistance; }
        public int getForwardSeekCount() { return forwardSeekCount; }
        public void setForwardSeekCount(int forwardSeekCount) { this.forwardSeekCount = forwardSeekCount; }
        public int getBackwardSeekCount() { return backwardSeekCount; }
        public void setBackwardSeekCount(int backwardSeekCount) { this.backwardSeekCount = backwardSeekCount; }
        public boolean isHasAbnormalSeeking() { return hasAbnormalSeeking; }
        public void setHasAbnormalSeeking(boolean hasAbnormalSeeking) { 
            this.hasAbnormalSeeking = hasAbnormalSeeking; 
        }
        public double getSeekFrequency() { return seekFrequency; }
        public void setSeekFrequency(double seekFrequency) { this.seekFrequency = seekFrequency; }
        public List<SeekEvent> getSeekEvents() { return seekEvents; }
        public void setSeekEvents(List<SeekEvent> seekEvents) { this.seekEvents = seekEvents; }
    }

    /**
     * 完成百分比分析结果
     */
    class CompletionPercentageAnalysisResult {
        private double completionPercentage;
        private boolean isAbnormalCompletion;
        private int totalPlaySeconds;
        private int effectivePlaySeconds;
        private double playEfficiency; // 有效播放时间 / 总播放时间

        public CompletionPercentageAnalysisResult(double completionPercentage, int totalPlaySeconds, 
                                                int effectivePlaySeconds) {
            this.completionPercentage = completionPercentage;
            this.totalPlaySeconds = totalPlaySeconds;
            this.effectivePlaySeconds = effectivePlaySeconds;
            this.playEfficiency = effectivePlaySeconds > 0 ? 
                (double) effectivePlaySeconds / totalPlaySeconds : 0.0;
        }

        // Getters and setters
        public double getCompletionPercentage() { return completionPercentage; }
        public boolean isAbnormalCompletion() { return isAbnormalCompletion; }
        public void setAbnormalCompletion(boolean abnormalCompletion) { 
            isAbnormalCompletion = abnormalCompletion; 
        }
        public int getTotalPlaySeconds() { return totalPlaySeconds; }
        public int getEffectivePlaySeconds() { return effectivePlaySeconds; }
        public double getPlayEfficiency() { return playEfficiency; }
    }

    /**
     * 播放模式分析结果
     */
    class PlaybackPatternAnalysisResult {
        private int pauseCount;
        private double averagePauseDuration;
        private int playCount;
        private boolean hasUnusualPattern;
        private String patternDescription;
        private Map<String, Object> patternMetrics;

        public PlaybackPatternAnalysisResult(int pauseCount, double averagePauseDuration, int playCount) {
            this.pauseCount = pauseCount;
            this.averagePauseDuration = averagePauseDuration;
            this.playCount = playCount;
        }

        // Getters and setters
        public int getPauseCount() { return pauseCount; }
        public double getAveragePauseDuration() { return averagePauseDuration; }
        public int getPlayCount() { return playCount; }
        public boolean isHasUnusualPattern() { return hasUnusualPattern; }
        public void setHasUnusualPattern(boolean hasUnusualPattern) { 
            this.hasUnusualPattern = hasUnusualPattern; 
        }
        public String getPatternDescription() { return patternDescription; }
        public void setPatternDescription(String patternDescription) { 
            this.patternDescription = patternDescription; 
        }
        public Map<String, Object> getPatternMetrics() { return patternMetrics; }
        public void setPatternMetrics(Map<String, Object> patternMetrics) { 
            this.patternMetrics = patternMetrics; 
        }
    }

    /**
     * 异常播放行为检测结果
     */
    class AbnormalPlaybackDetectionResult {
        private boolean hasAbnormalBehavior;
        private List<String> abnormalBehaviorTypes;
        private double abnormalityScore;
        private Map<String, Object> detectionDetails;

        public AbnormalPlaybackDetectionResult(boolean hasAbnormalBehavior, double abnormalityScore) {
            this.hasAbnormalBehavior = hasAbnormalBehavior;
            this.abnormalityScore = abnormalityScore;
        }

        // Getters and setters
        public boolean isHasAbnormalBehavior() { return hasAbnormalBehavior; }
        public List<String> getAbnormalBehaviorTypes() { return abnormalBehaviorTypes; }
        public void setAbnormalBehaviorTypes(List<String> abnormalBehaviorTypes) { 
            this.abnormalBehaviorTypes = abnormalBehaviorTypes; 
        }
        public double getAbnormalityScore() { return abnormalityScore; }
        public Map<String, Object> getDetectionDetails() { return detectionDetails; }
        public void setDetectionDetails(Map<String, Object> detectionDetails) { 
            this.detectionDetails = detectionDetails; 
        }
    }

    /**
     * 播放速率事件
     */
    class PlaybackRateEvent {
        private long timestamp;
        private double rate;
        private double duration;

        public PlaybackRateEvent(long timestamp, double rate, double duration) {
            this.timestamp = timestamp;
            this.rate = rate;
            this.duration = duration;
        }

        public long getTimestamp() { return timestamp; }
        public double getRate() { return rate; }
        public double getDuration() { return duration; }
    }

    /**
     * 跳转事件
     */
    class SeekEvent {
        private long timestamp;
        private double fromTime;
        private double toTime;
        private double distance;

        public SeekEvent(long timestamp, double fromTime, double toTime) {
            this.timestamp = timestamp;
            this.fromTime = fromTime;
            this.toTime = toTime;
            this.distance = Math.abs(toTime - fromTime);
        }

        public long getTimestamp() { return timestamp; }
        public double getFromTime() { return fromTime; }
        public double getToTime() { return toTime; }
        public double getDistance() { return distance; }
    }
}
