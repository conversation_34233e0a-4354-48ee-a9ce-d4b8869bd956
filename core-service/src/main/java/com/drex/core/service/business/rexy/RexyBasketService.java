package com.drex.core.service.business.rexy;

import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.OperateMaizeKernelRequest;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.core.api.response.MaizeKernelDTO;
import com.drex.core.dal.tablestore.model.RexyClaimRecord;
import com.kikitrade.framework.common.model.Response;

public interface RexyBasketService {

    CustomerRexyBasketsDTO getCustomerRexyBaskets(String customerId, RexyConstant.RexyBasketsTypeEnum basketType) throws CoreException;

    MaizeKernelDTO collectMaizeKernel(OperateMaizeKernelRequest request) throws CoreException;

    MaizeKernelDTO claimMaizeKernel(OperateMaizeKernelRequest request) throws CoreException;

    Response<MaizeKernelDTO> claimMaizeKernel(RexyClaimRecord record);

}
