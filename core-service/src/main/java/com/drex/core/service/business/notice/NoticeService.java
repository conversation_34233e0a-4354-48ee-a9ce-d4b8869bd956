package com.drex.core.service.business.notice;

import com.drex.core.api.common.CoreException;
import com.drex.core.api.request.NoticeDTO;

import java.util.List;

public interface NoticeService {

    /**
     * 根据ID获取通知
     *
     * @param id 通知ID
     * @return 通知DTO
     * @throws CoreException 核心异常
     */
    NoticeDTO getById(String id) throws CoreException;

    /**
     * 保存通知
     *
     * @param noticeDTO 通知DTO
     * @return 是否保存成功
     * @throws CoreException 核心异常
     */
    Boolean saveNotice(NoticeDTO noticeDTO) throws CoreException;

    /**
     * 更新通知
     *
     * @param noticeDTO 通知DTO
     * @return 是否更新成功
     * @throws CoreException 核心异常
     */
    Boolean updateNotice(NoticeDTO noticeDTO) throws CoreException;

    /**
     * 删除通知
     *
     * @param id 通知ID
     * @return 是否删除成功
     * @throws CoreException 核心异常
     */
    Boolean deleteNotice(String id) throws CoreException;

    /**
     * 根据状态和发送目标获取通知列表
     *
     * @param status 通知状态
     * @return 通知DTO列表
     * @throws CoreException 核心异常
     */
    List<NoticeDTO> getByStatus(String status, Long beginTime) throws CoreException;

}
