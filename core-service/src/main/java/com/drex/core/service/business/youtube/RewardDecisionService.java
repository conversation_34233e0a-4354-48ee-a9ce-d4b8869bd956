package com.drex.core.service.business.youtube;

import com.drex.core.api.request.SocialEventRequest;
import com.drex.core.dal.tablestore.model.VideoViewingSession;

import java.util.List;
import java.util.Map;

/**
 * 奖励决策服务接口
 * 负责根据业务规则和欺诈检测结果决定是否授予奖励
 */
public interface RewardDecisionService {

    /**
     * 评估奖励资格
     *
     * @param request 社交事件请求
     * @param session 会话信息
     * @param trustScore 信任分数
     * @param fraudIndicators 欺诈指标
     * @return 奖励资格评估结果
     */
    RewardEligibilityResult evaluateRewardEligibility(SocialEventRequest request,
                                                     VideoViewingSession session,
                                                     double trustScore,
                                                     Map<String, Double> fraudIndicators);

    /**
     * 生成奖励代码
     *
     * @param customerId 用户ID
     * @param sessionId 会话ID
     * @param rewardStage 奖励阶段
     * @return 奖励代码生成结果
     */
    RewardCodeGenerationResult generateRewardCode(String customerId, String sessionId, String rewardStage);

    /**
     * 验证奖励代码
     *
     * @param rewardCode 奖励代码
     * @param customerId 用户ID
     * @param sessionId 会话ID
     * @return 奖励代码验证结果
     */
    RewardCodeValidationResult validateRewardCode(String rewardCode, String customerId, String sessionId);

    /**
     * 计算奖励金额
     *
     * @param rewardStage 奖励阶段
     * @param trustScore 信任分数
     * @param watchPercentage 观看百分比
     * @return 奖励金额计算结果
     */
    RewardAmountCalculationResult calculateRewardAmount(String rewardStage, 
                                                       double trustScore, 
                                                       double watchPercentage);

    /**
     * 检查业务规则
     *
     * @param request 社交事件请求
     * @param session 会话信息
     * @return 业务规则检查结果
     */
    BusinessRuleCheckResult checkBusinessRules(SocialEventRequest request, VideoViewingSession session);

    /**
     * 检查用户奖励限制
     *
     * @param customerId 用户ID
     * @param videoId 视频ID
     * @param rewardStage 奖励阶段
     * @return 奖励限制检查结果
     */
    RewardLimitCheckResult checkRewardLimits(String customerId, String videoId, String rewardStage);

    /**
     * 记录奖励决策
     *
     * @param customerId 用户ID
     * @param sessionId 会话ID
     * @param decision 决策结果
     * @param reason 决策原因
     * @return 是否记录成功
     */
    boolean recordRewardDecision(String customerId, String sessionId, boolean decision, String reason);

    /**
     * 获取奖励配置
     *
     * @param rewardStage 奖励阶段
     * @return 奖励配置
     */
    RewardConfiguration getRewardConfiguration(String rewardStage);

    /**
     * 奖励资格评估结果
     */
    class RewardEligibilityResult {
        private boolean eligible;
        private String reason;
        private double confidenceScore;
        private BusinessRuleCheckResult businessRuleResult;
        private FraudCheckResult fraudCheckResult;
        private Map<String, Object> evaluationDetails;

        public RewardEligibilityResult(boolean eligible, String reason, double confidenceScore) {
            this.eligible = eligible;
            this.reason = reason;
            this.confidenceScore = confidenceScore;
        }

        // Getters and setters
        public boolean isEligible() { return eligible; }
        public String getReason() { return reason; }
        public double getConfidenceScore() { return confidenceScore; }
        public BusinessRuleCheckResult getBusinessRuleResult() { return businessRuleResult; }
        public void setBusinessRuleResult(BusinessRuleCheckResult businessRuleResult) { 
            this.businessRuleResult = businessRuleResult; 
        }
        public FraudCheckResult getFraudCheckResult() { return fraudCheckResult; }
        public void setFraudCheckResult(FraudCheckResult fraudCheckResult) { 
            this.fraudCheckResult = fraudCheckResult; 
        }
        public Map<String, Object> getEvaluationDetails() { return evaluationDetails; }
        public void setEvaluationDetails(Map<String, Object> evaluationDetails) { 
            this.evaluationDetails = evaluationDetails; 
        }
    }

    /**
     * 奖励代码生成结果
     */
    class RewardCodeGenerationResult {
        private boolean success;
        private String rewardCode;
        private long expirationTime;
        private String errorMessage;
        private Map<String, Object> codeMetadata;

        public RewardCodeGenerationResult(boolean success, String rewardCode, long expirationTime) {
            this.success = success;
            this.rewardCode = rewardCode;
            this.expirationTime = expirationTime;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public String getRewardCode() { return rewardCode; }
        public long getExpirationTime() { return expirationTime; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public Map<String, Object> getCodeMetadata() { return codeMetadata; }
        public void setCodeMetadata(Map<String, Object> codeMetadata) { this.codeMetadata = codeMetadata; }
    }

    /**
     * 奖励代码验证结果
     */
    class RewardCodeValidationResult {
        private boolean valid;
        private boolean expired;
        private boolean alreadyUsed;
        private String errorMessage;
        private Map<String, Object> codeDetails;

        public RewardCodeValidationResult(boolean valid, boolean expired, boolean alreadyUsed) {
            this.valid = valid;
            this.expired = expired;
            this.alreadyUsed = alreadyUsed;
        }

        // Getters and setters
        public boolean isValid() { return valid; }
        public boolean isExpired() { return expired; }
        public boolean isAlreadyUsed() { return alreadyUsed; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public Map<String, Object> getCodeDetails() { return codeDetails; }
        public void setCodeDetails(Map<String, Object> codeDetails) { this.codeDetails = codeDetails; }
    }

    /**
     * 奖励金额计算结果
     */
    class RewardAmountCalculationResult {
        private String baseAmount;
        private String bonusAmount;
        private String totalAmount;
        private double multiplier;
        private Map<String, String> amountBreakdown;
        private String calculationFormula;

        public RewardAmountCalculationResult(String baseAmount, String bonusAmount, String totalAmount) {
            this.baseAmount = baseAmount;
            this.bonusAmount = bonusAmount;
            this.totalAmount = totalAmount;
        }

        // Getters and setters
        public String getBaseAmount() { return baseAmount; }
        public String getBonusAmount() { return bonusAmount; }
        public String getTotalAmount() { return totalAmount; }
        public double getMultiplier() { return multiplier; }
        public void setMultiplier(double multiplier) { this.multiplier = multiplier; }
        public Map<String, String> getAmountBreakdown() { return amountBreakdown; }
        public void setAmountBreakdown(Map<String, String> amountBreakdown) { 
            this.amountBreakdown = amountBreakdown; 
        }
        public String getCalculationFormula() { return calculationFormula; }
        public void setCalculationFormula(String calculationFormula) { 
            this.calculationFormula = calculationFormula; 
        }
    }

    /**
     * 业务规则检查结果
     */
    class BusinessRuleCheckResult {
        private boolean passed;
        private List<String> failedRules;
        private Map<String, Object> ruleDetails;
        private double watchPercentage;
        private int effectiveWatchSeconds;

        public BusinessRuleCheckResult(boolean passed, double watchPercentage, int effectiveWatchSeconds) {
            this.passed = passed;
            this.watchPercentage = watchPercentage;
            this.effectiveWatchSeconds = effectiveWatchSeconds;
        }

        // Getters and setters
        public boolean isPassed() { return passed; }
        public List<String> getFailedRules() { return failedRules; }
        public void setFailedRules(List<String> failedRules) { this.failedRules = failedRules; }
        public Map<String, Object> getRuleDetails() { return ruleDetails; }
        public void setRuleDetails(Map<String, Object> ruleDetails) { this.ruleDetails = ruleDetails; }
        public double getWatchPercentage() { return watchPercentage; }
        public int getEffectiveWatchSeconds() { return effectiveWatchSeconds; }
    }

    /**
     * 欺诈检查结果
     */
    class FraudCheckResult {
        private boolean passed;
        private double trustScore;
        private String riskLevel;
        private List<String> triggeredIndicators;
        private Map<String, Double> indicatorScores;

        public FraudCheckResult(boolean passed, double trustScore, String riskLevel) {
            this.passed = passed;
            this.trustScore = trustScore;
            this.riskLevel = riskLevel;
        }

        // Getters and setters
        public boolean isPassed() { return passed; }
        public double getTrustScore() { return trustScore; }
        public String getRiskLevel() { return riskLevel; }
        public List<String> getTriggeredIndicators() { return triggeredIndicators; }
        public void setTriggeredIndicators(List<String> triggeredIndicators) { 
            this.triggeredIndicators = triggeredIndicators; 
        }
        public Map<String, Double> getIndicatorScores() { return indicatorScores; }
        public void setIndicatorScores(Map<String, Double> indicatorScores) { 
            this.indicatorScores = indicatorScores; 
        }
    }

    /**
     * 奖励限制检查结果
     */
    class RewardLimitCheckResult {
        private boolean withinLimits;
        private String limitType;
        private int currentCount;
        private int maxAllowed;
        private long resetTime;

        public RewardLimitCheckResult(boolean withinLimits, String limitType, int currentCount, int maxAllowed) {
            this.withinLimits = withinLimits;
            this.limitType = limitType;
            this.currentCount = currentCount;
            this.maxAllowed = maxAllowed;
        }

        // Getters and setters
        public boolean isWithinLimits() { return withinLimits; }
        public String getLimitType() { return limitType; }
        public int getCurrentCount() { return currentCount; }
        public int getMaxAllowed() { return maxAllowed; }
        public long getResetTime() { return resetTime; }
        public void setResetTime(long resetTime) { this.resetTime = resetTime; }
    }

    /**
     * 奖励配置
     */
    class RewardConfiguration {
        private String rewardStage;
        private String baseAmount;
        private double requiredWatchPercentage;
        private double minTrustScore;
        private int maxDailyRewards;
        private long rewardCodeExpirationMinutes;
        private Map<String, Object> additionalRules;

        public RewardConfiguration(String rewardStage, String baseAmount, 
                                 double requiredWatchPercentage, double minTrustScore) {
            this.rewardStage = rewardStage;
            this.baseAmount = baseAmount;
            this.requiredWatchPercentage = requiredWatchPercentage;
            this.minTrustScore = minTrustScore;
        }

        // Getters and setters
        public String getRewardStage() { return rewardStage; }
        public String getBaseAmount() { return baseAmount; }
        public double getRequiredWatchPercentage() { return requiredWatchPercentage; }
        public double getMinTrustScore() { return minTrustScore; }
        public int getMaxDailyRewards() { return maxDailyRewards; }
        public void setMaxDailyRewards(int maxDailyRewards) { this.maxDailyRewards = maxDailyRewards; }
        public long getRewardCodeExpirationMinutes() { return rewardCodeExpirationMinutes; }
        public void setRewardCodeExpirationMinutes(long rewardCodeExpirationMinutes) { 
            this.rewardCodeExpirationMinutes = rewardCodeExpirationMinutes; 
        }
        public Map<String, Object> getAdditionalRules() { return additionalRules; }
        public void setAdditionalRules(Map<String, Object> additionalRules) { 
            this.additionalRules = additionalRules; 
        }
    }
}
