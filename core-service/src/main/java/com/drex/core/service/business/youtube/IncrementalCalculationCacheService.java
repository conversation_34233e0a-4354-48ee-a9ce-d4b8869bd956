package com.drex.core.service.business.youtube;

import java.util.Map;

/**
 * 增量计算缓存服务接口
 * 负责缓存欺诈指标计算的中间结果，支持增量更新
 */
public interface IncrementalCalculationCacheService {

    /**
     * 增量更新播放相关指标
     * 
     * @param sessionId 会话ID
     * @param playTime 新增播放时长（秒）
     * @param effectivePlayTime 新增有效播放时长（秒）
     * @param seekCount 新增跳转次数
     * @param playbackRateSum 播放速率累计值
     * @param playbackRateCount 播放速率计数
     */
    void updatePlaybackMetrics(String sessionId, int playTime, int effectivePlayTime, 
                              int seekCount, double playbackRateSum, int playbackRateCount);

    /**
     * 增量更新焦点相关指标
     * 
     * @param sessionId 会话ID
     * @param focusTime 新增焦点时长（毫秒）
     * @param blurTime 新增失焦时长（毫秒）
     * @param idleTime 新增空闲时长（毫秒）
     * @param interactionCount 新增交互次数
     */
    void updateFocusMetrics(String sessionId, long focusTime, long blurTime, 
                           long idleTime, int interactionCount);

    /**
     * 增量更新事件相关指标
     * 
     * @param sessionId 会话ID
     * @param eventCounts 各类型事件计数增量
     * @param duplicateEventCount 新增重复事件数
     * @param timestampAnomalyCount 新增时间戳异常数
     * @param sequenceErrorCount 新增序列错误数
     */
    void updateEventMetrics(String sessionId, Map<String, Integer> eventCounts, 
                           int duplicateEventCount, int timestampAnomalyCount, int sequenceErrorCount);

    /**
     * 获取播放相关指标
     * 
     * @param sessionId 会话ID
     * @return 播放指标
     */
    PlaybackMetrics getPlaybackMetrics(String sessionId);

    /**
     * 获取焦点相关指标
     * 
     * @param sessionId 会话ID
     * @return 焦点指标
     */
    FocusMetrics getFocusMetrics(String sessionId);

    /**
     * 获取事件相关指标
     * 
     * @param sessionId 会话ID
     * @return 事件指标
     */
    EventMetrics getEventMetrics(String sessionId);

    /**
     * 获取所有缓存的指标
     * 
     * @param sessionId 会话ID
     * @return 所有指标
     */
    AllMetrics getAllMetrics(String sessionId);

    /**
     * 清除会话的缓存指标
     * 
     * @param sessionId 会话ID
     */
    void clearMetrics(String sessionId);

    /**
     * 设置缓存过期时间
     * 
     * @param sessionId 会话ID
     * @param expireSeconds 过期时间（秒）
     */
    void setExpire(String sessionId, long expireSeconds);

    /**
     * 播放指标
     */
    class PlaybackMetrics {
        private int totalPlayTime;
        private int effectivePlayTime;
        private int totalSeekCount;
        private double averagePlaybackRate;
        private double playbackRateSum;
        private int playbackRateCount;

        // Getters and setters
        public int getTotalPlayTime() { return totalPlayTime; }
        public void setTotalPlayTime(int totalPlayTime) { this.totalPlayTime = totalPlayTime; }
        public int getEffectivePlayTime() { return effectivePlayTime; }
        public void setEffectivePlayTime(int effectivePlayTime) { this.effectivePlayTime = effectivePlayTime; }
        public int getTotalSeekCount() { return totalSeekCount; }
        public void setTotalSeekCount(int totalSeekCount) { this.totalSeekCount = totalSeekCount; }
        public double getAveragePlaybackRate() { return averagePlaybackRate; }
        public void setAveragePlaybackRate(double averagePlaybackRate) { 
            this.averagePlaybackRate = averagePlaybackRate; 
        }
        public double getPlaybackRateSum() { return playbackRateSum; }
        public void setPlaybackRateSum(double playbackRateSum) { this.playbackRateSum = playbackRateSum; }
        public int getPlaybackRateCount() { return playbackRateCount; }
        public void setPlaybackRateCount(int playbackRateCount) { this.playbackRateCount = playbackRateCount; }
    }

    /**
     * 焦点指标
     */
    class FocusMetrics {
        private long totalFocusTime;
        private long totalBlurTime;
        private long totalIdleTime;
        private int totalInteractionCount;
        private double focusPercentage;
        private double idlePercentage;

        // Getters and setters
        public long getTotalFocusTime() { return totalFocusTime; }
        public void setTotalFocusTime(long totalFocusTime) { this.totalFocusTime = totalFocusTime; }
        public long getTotalBlurTime() { return totalBlurTime; }
        public void setTotalBlurTime(long totalBlurTime) { this.totalBlurTime = totalBlurTime; }
        public long getTotalIdleTime() { return totalIdleTime; }
        public void setTotalIdleTime(long totalIdleTime) { this.totalIdleTime = totalIdleTime; }
        public int getTotalInteractionCount() { return totalInteractionCount; }
        public void setTotalInteractionCount(int totalInteractionCount) { 
            this.totalInteractionCount = totalInteractionCount; 
        }
        public double getFocusPercentage() { return focusPercentage; }
        public void setFocusPercentage(double focusPercentage) { this.focusPercentage = focusPercentage; }
        public double getIdlePercentage() { return idlePercentage; }
        public void setIdlePercentage(double idlePercentage) { this.idlePercentage = idlePercentage; }
    }

    /**
     * 事件指标
     */
    class EventMetrics {
        private Map<String, Integer> eventCounts;
        private int totalDuplicateEvents;
        private int totalTimestampAnomalies;
        private int totalSequenceErrors;
        private int totalEventCount;

        // Getters and setters
        public Map<String, Integer> getEventCounts() { return eventCounts; }
        public void setEventCounts(Map<String, Integer> eventCounts) { this.eventCounts = eventCounts; }
        public int getTotalDuplicateEvents() { return totalDuplicateEvents; }
        public void setTotalDuplicateEvents(int totalDuplicateEvents) { 
            this.totalDuplicateEvents = totalDuplicateEvents; 
        }
        public int getTotalTimestampAnomalies() { return totalTimestampAnomalies; }
        public void setTotalTimestampAnomalies(int totalTimestampAnomalies) { 
            this.totalTimestampAnomalies = totalTimestampAnomalies; 
        }
        public int getTotalSequenceErrors() { return totalSequenceErrors; }
        public void setTotalSequenceErrors(int totalSequenceErrors) { 
            this.totalSequenceErrors = totalSequenceErrors; 
        }
        public int getTotalEventCount() { return totalEventCount; }
        public void setTotalEventCount(int totalEventCount) { this.totalEventCount = totalEventCount; }
    }

    /**
     * 所有指标
     */
    class AllMetrics {
        private PlaybackMetrics playbackMetrics;
        private FocusMetrics focusMetrics;
        private EventMetrics eventMetrics;
        private long lastUpdateTime;

        public AllMetrics(PlaybackMetrics playbackMetrics, FocusMetrics focusMetrics, EventMetrics eventMetrics) {
            this.playbackMetrics = playbackMetrics;
            this.focusMetrics = focusMetrics;
            this.eventMetrics = eventMetrics;
            this.lastUpdateTime = System.currentTimeMillis();
        }

        // Getters and setters
        public PlaybackMetrics getPlaybackMetrics() { return playbackMetrics; }
        public FocusMetrics getFocusMetrics() { return focusMetrics; }
        public EventMetrics getEventMetrics() { return eventMetrics; }
        public long getLastUpdateTime() { return lastUpdateTime; }
    }
}
