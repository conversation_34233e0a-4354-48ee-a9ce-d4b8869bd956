package com.drex.core.service.business.rexy.impl;

import com.drex.asset.api.model.constant.AssetBusinessType;
import com.drex.asset.api.model.constant.AssetType;
import com.drex.asset.api.model.request.AssetTransferInRequest;
import com.drex.asset.api.model.response.AssetOperateResponse;
import com.drex.asset.api.service.RemoteAssetService;
import com.drex.core.api.common.BusinessMonitorConstant;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.OperateMaizeKernelRequest;
import com.drex.core.api.request.RexyConfigDTO;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.core.api.response.MaizeKernelDTO;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.CustomerRexyBasketsBuilder;
import com.drex.core.dal.tablestore.builder.RexyBasketRecordBuilder;
import com.drex.core.dal.tablestore.model.CustomerRexy;
import com.drex.core.dal.tablestore.model.CustomerRexyBaskets;
import com.drex.core.dal.tablestore.model.RexyBasketRecord;
import com.drex.core.dal.tablestore.model.RexyClaimRecord;
import com.drex.core.model.BasketPoint;
import com.drex.core.model.RexyBusinessCode;
import com.drex.core.service.business.rexy.CustomerRexyService;
import com.drex.core.service.business.rexy.RexyBasketService;
import com.drex.core.service.business.rexy.RexyClaimRecordService;
import com.drex.core.service.business.rexy.RexyConfigService;
import com.drex.core.service.chain.PointGrant;
import com.drex.core.service.mapperstruct.RexyMapperStruct;
import com.drex.endpoint.api.RemoteTransactionsService;
import com.drex.endpoint.api.response.TransactionInfo;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import com.kikitrade.framework.redis.lock.RedisDistributedLock;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RexyBasketServiceImpl implements RexyBasketService {

    @Resource
    private CustomerRexyService customerRexyService;

    @Resource
    private RexyMapperStruct rexyMapperStruct;

    @Resource
    private CustomerRexyBasketsBuilder customerRexyBasketsBuilder;

    @Resource
    private RexyBasketRecordBuilder rexyBasketRecordBuilder;

    @Resource
    private IdUtils idUtils;

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @DubboReference
    private RemoteAssetService remoteAssetService;
    @Resource
    private RexyConfigService rexyConfigService;
    @DubboReference
    private RemoteTransactionsService remoteTransactionsService;
    @Resource
    private RedisDistributedLock redisDistributedLock;
    @Resource
    private RexyClaimRecordService rexyClaimRecordService;
    @Resource
    private PointGrant pointGrant;
    @Resource
    private KiKiMonitor kiKiMonitor;

    @Override
    public CustomerRexyBasketsDTO getCustomerRexyBaskets(String customerId, RexyConstant.RexyBasketsTypeEnum basketType) throws CoreException {
        List<CustomerRexy> customerRexyList = customerRexyService.getByCustomerAndStatus(customerId, RexyConstant.CustomerRexyStatus.ACTIVE);
        CustomerRexy customerRexy = customerRexyList.get(0);
        CustomerRexyBaskets rexyBaskets = customerRexyBasketsBuilder.getByCustomerId(customerId, basketType.name());
        //如果还没有篮子，初始化篮子
        if(rexyBaskets == null){
            RexyConfigDTO rexyConfig = rexyConfigService.getRexyConfigById(customerRexy.getRexyId());
            rexyBaskets = new CustomerRexyBaskets();
            rexyBaskets.setCustomerId(customerId);
            rexyBaskets.setBasketType(basketType.name());
            rexyBaskets.setReceived(BigDecimal.ZERO);
            rexyBaskets.setLastClaimTime(System.currentTimeMillis());
            rexyBaskets.setBasketLimit(BigDecimal.valueOf(rexyConfig.getLimit()));
            customerRexyBasketsBuilder.insert(rexyBaskets);
        }else{
            if(basketType == RexyConstant.RexyBasketsTypeEnum.normal){
                RexyConfigDTO rexyConfig = rexyConfigService.getRexyConfigById(customerRexy.getRexyId());
                customerRexy.setRexyBasketLimit(rexyConfig.getLimit());
                customerRexy.setRexyRate(rexyConfig.getRate());
                BigDecimal autoGenCount = calculateRexyAutoGenMaizeKernel(customerId, rexyBaskets.getLastClaimTime(), customerRexy);
                rexyBaskets.setReceived(BigDecimal.valueOf(Math.min(rexyConfig.getLimit(), rexyBaskets.getReceived().add(autoGenCount).intValue())));
            }
        }
        return rexyMapperStruct.toCustomerRexyBasketsDTO(rexyBaskets, customerRexy);
    }

    /**
     * 计算恐龙自动生成的玉米粒数量
     *
     * @param customerId 用户ID
     * @param lastClaimTime 上一次领取时间
     * @return autoGenCount
     */
    private BigDecimal calculateRexyAutoGenMaizeKernel(String customerId, Long lastClaimTime, CustomerRexy customerRexy) {
        try {
            // 最后一次领取时间到现在的时间差(分钟数) * 生产速率(每分钟生成多少个)
            long minutes = TimeUnit.MILLISECONDS.toMinutes(System.currentTimeMillis() - lastClaimTime);
            BigDecimal autoGenCount = BigDecimal.valueOf(minutes).multiply(BigDecimal.valueOf(customerRexy.getRexyRate()/60.0));
            log.info("calculateRexyAutoGenMaizeKernel customerId: {}, minutes: {}, autoGenCount: {}", customerId, minutes, autoGenCount);
            return autoGenCount;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }


    @Override
    public MaizeKernelDTO collectMaizeKernel(OperateMaizeKernelRequest request) throws CoreException {
        CustomerRexyBasketsDTO customerRexyBaskets = getCustomerRexyBaskets(request.getCustomerId(), request.getBasketType());
        log.info("collectMaizeKernel latest rexyBaskets:{}", customerRexyBaskets);
        if(customerRexyBaskets == null){
            throw new CoreException(CoreResponseCode.DATA_NOT_FOUND);
        }

        // 1.校验: businessId是否有效
        if (request.getBusinessId() == null || request.getBusinessId().isEmpty()) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }
        if (checkBusinessIdValid(request.getBusinessCode(),request.getBusinessId())) {
            throw new CoreException(CoreResponseCode.INVALID_BUSINESS_ID);
        }

        if(request.getBasketType() == RexyConstant.RexyBasketsTypeEnum.normal){
            // 2.校验: 篮子中的玉米粒是否已满
            if (customerRexyBaskets.getReceived().compareTo(customerRexyBaskets.getBasketLimit()) >= 0){
                log.info("customerRexyBaskets is full, already received:{}, basketLimit:{}",
                        customerRexyBaskets.getReceived(), customerRexyBaskets.getBasketLimit());
                throw new CoreException(CoreResponseCode.REXY_BASKET_FULL);
            }
            if (customerRexyBaskets.getReceived().add(request.getAmount())
                    .compareTo(customerRexyBaskets.getBasketLimit()) > 0){
                log.info("customerRexyBaskets is full, already received:{}, collect amount:{}, basketLimit:{}",
                        customerRexyBaskets.getReceived(), request.getAmount(), customerRexyBaskets.getBasketLimit());
                throw new CoreException(CoreResponseCode.REXY_BASKET_FULL);
            }
        }

        CustomerRexyBaskets rexyBaskets = customerRexyBasketsBuilder.getByCustomerId(request.getCustomerId(), request.getBasketType().name());
        rexyBaskets.setReceived(rexyBaskets.getReceived().add(request.getAmount()));
        rexyBaskets.setModified(System.currentTimeMillis());

        // 3.写入变更记录(amount = 指定数量)
        RexyBasketRecord rexyBasketRecord = createRexyBasketRecord(request);
        rexyBasketRecord.setStatus(RexyConstant.CommonStatus.SUCCESS.getCode());
        rexyBasketRecord.setAvailable(rexyBaskets.getReceived().intValue());

        Boolean collectRecord = rexyBasketRecordBuilder.insert(rexyBasketRecord);
        log.info("collectMaizeKernel rexyBasketRecord {}, collectRecord:{}", rexyBasketRecord, collectRecord);
        if (Boolean.FALSE.equals(collectRecord)) {
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        // 4.增加玉米粒
        Boolean rexyBasketUpdate = customerRexyBasketsBuilder.update(rexyBaskets);
        if (Boolean.FALSE.equals(rexyBasketUpdate)) {
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        // 返回最新剩余的玉米粒数量
        return MaizeKernelDTO.builder().received(rexyBaskets.getReceived()).build();
    }

    /**
     * 校验businessId是否有效
     * @param businessCode
     * @param businessId
     * @return
     */
    private boolean checkBusinessIdValid(Integer businessCode,String businessId) {
        // todo: 校验businessId是否有效

        return false;
    }

    @Override
    public MaizeKernelDTO claimMaizeKernel(OperateMaizeKernelRequest request) throws CoreException {
        // 2.校验: 篮子中的玉米粒是否为空
        CustomerRexyBasketsDTO customerRexyBaskets = getCustomerRexyBaskets(request.getCustomerId(), request.getBasketType());
        log.info("claimMaizeKernel latest rexyBaskets:{}", customerRexyBaskets);
        if(customerRexyBaskets == null){
            throw new CoreException(CoreResponseCode.DATA_NOT_FOUND);
        }
        if (customerRexyBaskets.getReceived().compareTo(request.getAmount()) < 0){
            log.error("customerRexyBaskets is empty, already received:{}", customerRexyBaskets.getReceived());
            kiKiMonitor.monitor(BusinessMonitorConstant.CLAIM_MAIZE_KERNEL, new String[]{"code", "basket_empty"});
            throw new CoreException(CoreResponseCode.REXY_BASKET_EMPTY);
        }

        // 3.写入变更记录(amount = 篮子中应有的全部数量)
        customerRexyBaskets.setReceived(customerRexyBaskets.getReceived().subtract(request.getAmount()));
        customerRexyBaskets.setLastClaimTime(System.currentTimeMillis());
        CustomerRexyBaskets rexyBaskets = rexyMapperStruct.toCustomerRexyBaskets(customerRexyBaskets);
        rexyBaskets.setModified(System.currentTimeMillis());

        // 4.减少玉米粒
        RexyBasketRecord rexyBasketRecord = createRexyBasketRecord(request);
        rexyBasketRecord.setStatus(RexyConstant.CommonStatus.SUCCESS.getCode());
        rexyBasketRecord.setAvailable(customerRexyBaskets.getReceived().intValue());

        Boolean claimRecord = rexyBasketRecordBuilder.insert(rexyBasketRecord);
        log.info("claimMaizeKernel rexyBasketRecord {}, claimRecord:{}", rexyBasketRecord, claimRecord);
        if (Boolean.FALSE.equals(claimRecord)) {
            RexyBasketRecord record = rexyBasketRecordBuilder.getById(rexyBasketRecord.getId());
            if(record != null && record.getStatus().equals(RexyConstant.CommonStatus.SUCCESS.getCode())){
                return MaizeKernelDTO.builder().received(request.getAmount()).build();
            }
            kiKiMonitor.monitor(BusinessMonitorConstant.CLAIM_MAIZE_KERNEL, new String[]{"code", "basket_operate_fail"});
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        Boolean rexyBasketUpdate = customerRexyBasketsBuilder.update(rexyBaskets);
        if (Boolean.FALSE.equals(rexyBasketUpdate)) {
            kiKiMonitor.monitor(BusinessMonitorConstant.CLAIM_MAIZE_KERNEL, new String[]{"code", "basket_operate_fail"});
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        // 返回领取的玉米粒数量
        return MaizeKernelDTO.builder().received(request.getAmount()).build();
    }

    @Override
    public Response<MaizeKernelDTO> claimMaizeKernel(RexyClaimRecord record) {
        String lockKey = null;
        boolean lock = false;

        try{
            //查询交易hash是否存在
            Response<TransactionInfo> transactionInfoResponse = remoteTransactionsService.getTransactionInfo(record.getTransferHashCode());
            if(transactionInfoResponse.isSuccess()){
                String data = transactionInfoResponse.getData().getData();
                //解析callData，执行扣减篮子
                BasketPoint.ClaimPointsModel claimPointsModel = pointGrant.decodeClaimOperationNoPrefix(data);
                log.info("decodeClaimOperation: {}", claimPointsModel);
                lockKey = "lock:claim:" + claimPointsModel.getBusinessId();
                lock = redisDistributedLock.tryLock(lockKey, Duration.ofSeconds(5));
                if(!lock){
                    return Response.error(CoreResponseCode.TOO_MANY_REQUEST.getCode(), "listener or http is doing");
                }
                //扣减篮子
                OperateMaizeKernelRequest operateMaizeKernelRequest = OperateMaizeKernelRequest.builder()
                        .customerId(record.getCustomerId())
                        .operateType(RexyConstant.OperateTypeEnum.claim)
                        .businessCode(RexyBusinessCode.CLAIM_MAIZE.getCode())
                        .amount(record.getPoint())
                        .basketType(RexyConstant.RexyBasketsTypeEnum.valueOf(record.getBasketType()))
                        .businessId(record.getRecordId())
                        .transactionHash(record.getTransferHashCode())
                        .build();
                MaizeKernelDTO maizeKernelDTO = claimMaizeKernel(operateMaizeKernelRequest);

                //转入积分
                AssetTransferInRequest transferInRequest = new AssetTransferInRequest();
                transferInRequest.setAmount(record.getPoint());
                transferInRequest.setCustomerId(record.getCustomerId());
                transferInRequest.setBusinessId(record.getRecordId());
                transferInRequest.setAssetType(AssetType.POINT);
                if(RexyConstant.RexyBasketsTypeEnum.invite.name().equals(record.getBasketType())){
                    transferInRequest.setBusinessType(AssetBusinessType.INVITE_REWARD);
                } else {
                    transferInRequest.setBusinessType(AssetBusinessType.ACTIVITY_TASK);
                }
                transferInRequest.setDescription("claim maize kernel");
                Response<AssetOperateResponse> response = remoteAssetService.transferIn(transferInRequest);
                if(!response.isSuccess()){
                    kiKiMonitor.monitor(BusinessMonitorConstant.CLAIM_MAIZE_KERNEL, new String[]{"code", "transfer_in_fail"});
                    return Response.error(CoreResponseCode.DATA_OPERATE_FAIL.getCode(), "transfer in fail");
                }
                Boolean claim = rexyClaimRecordService.confirmClaim(record);
                log.info("claimMaizeKernel success: {},{}", maizeKernelDTO, claim);
                return Response.success(maizeKernelDTO);
            }
            kiKiMonitor.monitor(BusinessMonitorConstant.CLAIM_MAIZE_KERNEL, new String[]{"code", "transaction_not_found"});
            return Response.error(CoreResponseCode.DATA_NOT_FOUND.getCode(), "transaction not found");
        } catch (CoreException e) {
            kiKiMonitor.monitor(BusinessMonitorConstant.CLAIM_MAIZE_KERNEL, new String[]{"code", e.getCode().name().toLowerCase()});
            return Response.error(e.getCode().getCode(), e.getMessage());
        }finally {
            if(lockKey != null && lock){
                redisDistributedLock.unlock(lockKey);
            }
        }
    }

    public RexyBasketRecord createRexyBasketRecord(OperateMaizeKernelRequest request) {
        RexyBasketRecord rexyBasketRecord = new RexyBasketRecord();
        rexyBasketRecord.setId(request.getBusinessId() + request.getBusinessCode());
        rexyBasketRecord.setCustomerId(request.getCustomerId());
        rexyBasketRecord.setBasketType(request.getBasketType().name());
        rexyBasketRecord.setBusinessId(request.getBusinessId());
        rexyBasketRecord.setBusinessCode(request.getBusinessCode());
        rexyBasketRecord.setOperateType(request.getOperateType().name());
        rexyBasketRecord.setAmount(request.getAmount().intValue());
        rexyBasketRecord.setCreated(System.currentTimeMillis());
        return rexyBasketRecord;
    }
}
