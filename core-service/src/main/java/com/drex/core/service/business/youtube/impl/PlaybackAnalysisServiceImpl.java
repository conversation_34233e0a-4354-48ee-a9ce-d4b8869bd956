package com.drex.core.service.business.youtube.impl;

import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
import com.drex.core.model.youtube.FraudIndicatorThresholds;
import com.drex.core.service.business.youtube.PlaybackAnalysisService;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 播放分析服务实现
 * 基于提供的欺诈指标阈值设计实现
 */
@Slf4j
@Service
public class PlaybackAnalysisServiceImpl implements PlaybackAnalysisService {

    // 注意：欺诈指标阈值常量已移至FraudIndicatorThresholds类中统一管理

    @Override
    public PlaybackRateAnalysisResult analyzePlaybackRate(List<SessionEvent> events) {
        try {
            List<PlaybackRateEvent> rateEvents = extractPlaybackRateEvents(events);
            
            if (rateEvents.isEmpty()) {
                return new PlaybackRateAnalysisResult(1.0, 1.0, 1.0, 0);
            }

            double totalDuration = rateEvents.stream().mapToDouble(PlaybackRateEvent::getDuration).sum();
            double weightedRateSum = rateEvents.stream()
                    .mapToDouble(event -> event.getRate() * event.getDuration())
                    .sum();
            
            double averagePlaybackRate = totalDuration > 0 ? weightedRateSum / totalDuration : 1.0;
            double maxPlaybackRate = rateEvents.stream().mapToDouble(PlaybackRateEvent::getRate).max().orElse(1.0);
            double minPlaybackRate = rateEvents.stream().mapToDouble(PlaybackRateEvent::getRate).min().orElse(1.0);
            int rateChangeCount = rateEvents.size();

            PlaybackRateAnalysisResult result = new PlaybackRateAnalysisResult(
                    averagePlaybackRate, maxPlaybackRate, minPlaybackRate, rateChangeCount);
            
            // 检测异常播放速率
            boolean hasAbnormalRate = Math.abs(averagePlaybackRate - 1.0) > 0.2 ||
                    maxPlaybackRate > FraudIndicatorThresholds.PLAYBACK_RATE_MAX_ALLOWED;
            result.setHasAbnormalRate(hasAbnormalRate);
            
            // 计算异常速率百分比
            double abnormalDuration = rateEvents.stream()
                    .filter(event -> Math.abs(event.getRate() - 1.0) > 0.2)
                    .mapToDouble(PlaybackRateEvent::getDuration)
                    .sum();
            result.setAbnormalRatePercentage(totalDuration > 0 ? abnormalDuration / totalDuration : 0.0);
            result.setRateEvents(rateEvents);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze playback rate", e);
            return new PlaybackRateAnalysisResult(1.0, 1.0, 1.0, 0);
        }
    }

    @Override
    public SeekingBehaviorAnalysisResult analyzeSeekingBehavior(List<SessionEvent> events) {
        try {
            List<SeekEvent> seekEvents = extractSeekEvents(events);
            
            if (seekEvents.isEmpty()) {
                return new SeekingBehaviorAnalysisResult(0, 0.0);
            }

            int totalSeekCount = seekEvents.size();
            double averageSeekDistance = seekEvents.stream()
                    .mapToDouble(SeekEvent::getDistance)
                    .average()
                    .orElse(0.0);

            int forwardSeekCount = (int) seekEvents.stream()
                    .filter(event -> event.getToTime() > event.getFromTime())
                    .count();
            int backwardSeekCount = totalSeekCount - forwardSeekCount;

            SeekingBehaviorAnalysisResult result = new SeekingBehaviorAnalysisResult(totalSeekCount, averageSeekDistance);
            result.setForwardSeekCount(forwardSeekCount);
            result.setBackwardSeekCount(backwardSeekCount);

            // 计算跳转频率（每分钟）
            long sessionDuration = getSessionDurationMinutes(events);
            double seekFrequency = sessionDuration > 0 ? (double) totalSeekCount / sessionDuration : 0.0;
            result.setSeekFrequency(seekFrequency);

            // 检测异常跳转
            boolean hasAbnormalSeeking = seekFrequency > FraudIndicatorThresholds.ABNORMAL_SEEK_THRESHOLD;
            result.setHasAbnormalSeeking(hasAbnormalSeeking);
            result.setSeekEvents(seekEvents);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze seeking behavior", e);
            return new SeekingBehaviorAnalysisResult(0, 0.0);
        }
    }

    @Override
    public CompletionPercentageAnalysisResult analyzeCompletionPercentage(List<SessionEvent> events, 
                                                                         int videoDurationSeconds) {
        try {
            int totalPlaySeconds = calculateTotalPlayTime(events);
            int effectivePlaySeconds = calculateEffectiveWatchTime(events);
            
            double completionPercentage = videoDurationSeconds > 0 ? 
                    (double) totalPlaySeconds / videoDurationSeconds : 0.0;

            CompletionPercentageAnalysisResult result = new CompletionPercentageAnalysisResult(
                    completionPercentage, totalPlaySeconds, effectivePlaySeconds);

            // 检测异常完成百分比
            boolean isAbnormalCompletion = completionPercentage > FraudIndicatorThresholds.COMPLETION_PERCENTAGE_MAX_ALLOWED ||
                    (completionPercentage > FraudIndicatorThresholds.COMPLETION_PERCENTAGE_EXPECTED &&
                     effectivePlaySeconds < totalPlaySeconds * 0.5); // 实际观看时间远小于报告时间
            
            result.setAbnormalCompletion(isAbnormalCompletion);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze completion percentage", e);
            return new CompletionPercentageAnalysisResult(0.0, 0, 0);
        }
    }

    @Override
    public int calculateEffectiveWatchTime(List<SessionEvent> events) {
        try {
            // 计算有效观看时长 = 播放时间 - 失焦时间
            int totalPlayTime = 0;
            int totalBlurTime = 0;
            
            // 按时间排序事件
            List<SessionEvent> sortedEvents = events.stream()
                    .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
                    .toList();

            Long lastPlayTime = null;
            Long lastFocusTime = null;
            boolean isPlaying = false;
            boolean isFocused = true;

            for (SessionEvent event : sortedEvents) {
                String eventType = event.getEventType();
                Long timestamp = event.getClientTimestamp();

                switch (eventType) {
                    case "PLAY":
                        if (!isPlaying) {
                            lastPlayTime = timestamp;
                            isPlaying = true;
                        }
                        break;
                    case "PAUSE":
                        if (isPlaying && lastPlayTime != null) {
                            totalPlayTime += (int) ((timestamp - lastPlayTime) / 1000);
                            isPlaying = false;
                        }
                        break;
                    case "BLUR":
                        if (isFocused) {
                            lastFocusTime = timestamp;
                            isFocused = false;
                        }
                        break;
                    case "FOCUS":
                        if (!isFocused && lastFocusTime != null) {
                            totalBlurTime += (int) ((timestamp - lastFocusTime) / 1000);
                            isFocused = true;
                        }
                        break;
                }
            }

            // 处理未结束的状态
            if (isPlaying && lastPlayTime != null && !sortedEvents.isEmpty()) {
                long lastEventTime = sortedEvents.get(sortedEvents.size() - 1).getClientTimestamp();
                totalPlayTime += (int) ((lastEventTime - lastPlayTime) / 1000);
            }

            if (!isFocused && lastFocusTime != null && !sortedEvents.isEmpty()) {
                long lastEventTime = sortedEvents.get(sortedEvents.size() - 1).getClientTimestamp();
                totalBlurTime += (int) ((lastEventTime - lastFocusTime) / 1000);
            }

            return Math.max(0, totalPlayTime - totalBlurTime);

        } catch (Exception e) {
            log.error("Failed to calculate effective watch time", e);
            return 0;
        }
    }

    @Override
    public PlaybackPatternAnalysisResult analyzePlaybackPattern(List<SessionEvent> events) {
        try {
            int pauseCount = (int) events.stream()
                    .filter(event -> "PAUSE".equals(event.getEventType()))
                    .count();
            
            int playCount = (int) events.stream()
                    .filter(event -> "PLAY".equals(event.getEventType()))
                    .count();

            // 计算平均暂停时长
            double averagePauseDuration = calculateAveragePauseDuration(events);

            PlaybackPatternAnalysisResult result = new PlaybackPatternAnalysisResult(
                    pauseCount, averagePauseDuration, playCount);

            // 检测异常模式
            long sessionDurationMinutes = getSessionDurationMinutes(events);
            double pauseFrequency = sessionDurationMinutes > 0 ? (double) pauseCount / sessionDurationMinutes : 0.0;
            
            boolean hasUnusualPattern = pauseFrequency > 10 || // 超过10次暂停/分钟
                    (pauseCount > 0 && averagePauseDuration < 1.0) || // 暂停时间过短
                    (playCount > 0 && pauseCount == 0 && sessionDurationMinutes > 5); // 长时间无暂停

            result.setHasUnusualPattern(hasUnusualPattern);
            result.setPatternDescription(generatePatternDescription(pauseCount, playCount, averagePauseDuration));

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("pauseFrequency", pauseFrequency);
            metrics.put("playPauseRatio", playCount > 0 ? (double) pauseCount / playCount : 0.0);
            result.setPatternMetrics(metrics);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze playback pattern", e);
            return new PlaybackPatternAnalysisResult(0, 0.0, 0);
        }
    }

    @Override
    public AbnormalPlaybackDetectionResult detectAbnormalPlayback(List<SessionEvent> events) {
        try {
            List<String> abnormalBehaviorTypes = new ArrayList<>();
            double abnormalityScore = 0.0;

            // 检测重复事件
            double repeatedEventsScore = detectRepeatedEvents(events);
            if (repeatedEventsScore > 0) {
                abnormalBehaviorTypes.add("REPEATED_EVENTS");
                abnormalityScore = Math.max(abnormalityScore, repeatedEventsScore);
            }

            // 检测异常播放速率
            PlaybackRateAnalysisResult rateResult = analyzePlaybackRate(events);
            if (rateResult.isHasAbnormalRate()) {
                abnormalBehaviorTypes.add("EXCESSIVE_PLAYBACK_SPEED");
                double rateScore = calculatePlaybackRateScore(rateResult.getAveragePlaybackRate());
                abnormalityScore = Math.max(abnormalityScore, rateScore);
            }

            // 检测异常跳转
            SeekingBehaviorAnalysisResult seekResult = analyzeSeekingBehavior(events);
            if (seekResult.isHasAbnormalSeeking()) {
                abnormalBehaviorTypes.add("ABNORMAL_SEEK");
                double seekScore = calculateSeekScore(seekResult.getSeekFrequency());
                abnormalityScore = Math.max(abnormalityScore, seekScore);
            }

            boolean hasAbnormalBehavior = !abnormalBehaviorTypes.isEmpty();
            AbnormalPlaybackDetectionResult result = new AbnormalPlaybackDetectionResult(
                    hasAbnormalBehavior, abnormalityScore);
            result.setAbnormalBehaviorTypes(abnormalBehaviorTypes);

            Map<String, Object> details = new HashMap<>();
            details.put("repeatedEventsScore", repeatedEventsScore);
            details.put("playbackRateScore", rateResult.isHasAbnormalRate() ? 
                    calculatePlaybackRateScore(rateResult.getAveragePlaybackRate()) : 0.0);
            details.put("seekScore", seekResult.isHasAbnormalSeeking() ? 
                    calculateSeekScore(seekResult.getSeekFrequency()) : 0.0);
            result.setDetectionDetails(details);

            return result;

        } catch (Exception e) {
            log.error("Failed to detect abnormal playback", e);
            return new AbnormalPlaybackDetectionResult(false, 0.0);
        }
    }

    @Override
    public Map<String, Double> calculatePlaybackFraudIndicators(List<SessionEvent> events, int videoDurationSeconds) {
        Map<String, Double> indicators = new HashMap<>();

        try {
            // 2.1 重复事件 (REPEATED_EVENTS)
            double repeatedEventsScore = detectRepeatedEvents(events);
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.DUPLICATE_EVENT.getCode(), repeatedEventsScore);

            // 2.2 完成百分比异常 (ABNORMAL_COMPLETION_PERCENTAGE)
            double completionAnomalyScore = calculateCompletionAnomalyScore(events, videoDurationSeconds);
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.COMPLETION_PERCENTAGE_ANOMALY.getCode(), 
                    completionAnomalyScore);

            // 2.8 异常播放速率 (EXCESSIVE_PLAYBACK_SPEED)
            PlaybackRateAnalysisResult rateResult = analyzePlaybackRate(events);
            double playbackRateScore = calculatePlaybackRateScore(rateResult.getAveragePlaybackRate());
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.EXCESSIVE_PLAYBACK_SPEED.getCode(), 
                    playbackRateScore);

            // 2.9 异常跳转 (ABNORMAL_SEEK)
            SeekingBehaviorAnalysisResult seekResult = analyzeSeekingBehavior(events);
            double seekScore = calculateSeekScore(seekResult.getSeekFrequency());
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.ABNORMAL_SEEKING.getCode(), seekScore);

            // 播放模式异常
            PlaybackPatternAnalysisResult patternResult = analyzePlaybackPattern(events);
            double patternScore = patternResult.isHasUnusualPattern() ? 0.8 : 0.0;
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.PLAYBACK_PATTERN_ANOMALY.getCode(), 
                    patternScore);

        } catch (Exception e) {
            log.error("Failed to calculate playback fraud indicators", e);
            // 返回高风险指标
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.EXCESSIVE_PLAYBACK_SPEED.getCode(), 1.0);
        }

        return indicators;
    }

    /**
     * 检测重复事件
     * 使用FraudIndicatorThresholds中的阈值和计算公式
     */
    private double detectRepeatedEvents(List<SessionEvent> events) {
        Map<String, Integer> eventCounts = new HashMap<>();
        long sessionDurationMinutes = Math.max(1, getSessionDurationMinutes(events));

        for (SessionEvent event : events) {
            String eventType = event.getEventType();
            if ("PLAY".equals(eventType) || "PAUSE".equals(eventType)) {
                eventCounts.put(eventType, eventCounts.getOrDefault(eventType, 0) + 1);
            }
        }

        double maxScore = 0.0;
        for (Map.Entry<String, Integer> entry : eventCounts.entrySet()) {
            double frequency = (double) entry.getValue() / sessionDurationMinutes;
            double score = FraudIndicatorThresholds.calculateRepeatedEventsScore(frequency);
            maxScore = Math.max(maxScore, score);
        }

        return maxScore;
    }

    /**
     * 计算完成百分比异常分数
     * 使用FraudIndicatorThresholds中的计算公式
     */
    private double calculateCompletionAnomalyScore(List<SessionEvent> events, int videoDurationSeconds) {
        int watchedDuration = calculateTotalPlayTime(events);
        int realWatchTime = calculateEffectiveWatchTime(events);

        if (videoDurationSeconds <= 0) return 0.0;

        double completionPercentage = (double) watchedDuration / videoDurationSeconds;

        return FraudIndicatorThresholds.calculateCompletionPercentageAnomalyScore(
                completionPercentage, realWatchTime, watchedDuration);
    }

    /**
     * 计算播放速率分数
     * 使用FraudIndicatorThresholds中的计算公式
     */
    private double calculatePlaybackRateScore(double averagePlaybackRate) {
        return FraudIndicatorThresholds.calculateExcessivePlaybackSpeedScore(averagePlaybackRate);
    }

    /**
     * 计算跳转分数
     * 使用FraudIndicatorThresholds中的计算公式
     */
    private double calculateSeekScore(double seekFrequency) {
        return FraudIndicatorThresholds.calculateAbnormalSeekScore(seekFrequency);
    }

    /**
     * 提取播放速率事件
     */
    private List<PlaybackRateEvent> extractPlaybackRateEvents(List<SessionEvent> events) {
        List<PlaybackRateEvent> rateEvents = new ArrayList<>();
        
        for (int i = 0; i < events.size(); i++) {
            SessionEvent event = events.get(i);
            if ("PLAYBACK_RATE_CHANGE".equals(event.getEventType()) && event.getEventData() != null) {
                Object rateObj = event.getEventData().get("playbackRate");
                if (rateObj instanceof Number) {
                    double rate = ((Number) rateObj).doubleValue();
                    long timestamp = event.getClientTimestamp();
                    
                    // 计算持续时间（到下一个速率变化事件或会话结束）
                    double duration = 0.0;
                    for (int j = i + 1; j < events.size(); j++) {
                        if ("PLAYBACK_RATE_CHANGE".equals(events.get(j).getEventType())) {
                            duration = (events.get(j).getClientTimestamp() - timestamp) / 1000.0;
                            break;
                        }
                    }
                    if (duration == 0.0 && i < events.size() - 1) {
                        duration = (events.get(events.size() - 1).getClientTimestamp() - timestamp) / 1000.0;
                    }
                    
                    rateEvents.add(new PlaybackRateEvent(timestamp, rate, duration));
                }
            }
        }
        
        return rateEvents;
    }

    /**
     * 提取跳转事件
     */
    private List<SeekEvent> extractSeekEvents(List<SessionEvent> events) {
        List<SeekEvent> seekEvents = new ArrayList<>();
        
        for (SessionEvent event : events) {
            if ("SEEKING".equals(event.getEventType()) && event.getEventData() != null) {
                Object fromObj = event.getEventData().get("from");
                Object toObj = event.getEventData().get("to");
                
                if (fromObj instanceof Number && toObj instanceof Number) {
                    double fromTime = ((Number) fromObj).doubleValue();
                    double toTime = ((Number) toObj).doubleValue();
                    seekEvents.add(new SeekEvent(event.getClientTimestamp(), fromTime, toTime));
                }
            }
        }
        
        return seekEvents;
    }

    /**
     * 计算总播放时间
     */
    private int calculateTotalPlayTime(List<SessionEvent> events) {
        // 简化实现：基于TIMEUPDATE事件的最大时间
        return events.stream()
                .filter(event -> "TIMEUPDATE".equals(event.getEventType()))
                .filter(event -> event.getEventData() != null && event.getEventData().get("currentTime") != null)
                .mapToInt(event -> ((Number) event.getEventData().get("currentTime")).intValue())
                .max()
                .orElse(0);
    }

    /**
     * 获取会话持续时间（分钟）
     */
    private long getSessionDurationMinutes(List<SessionEvent> events) {
        if (events.isEmpty()) return 1;
        
        long startTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).min().orElse(0);
        long endTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).max().orElse(0);
        
        return Math.max(1, (endTime - startTime) / (60 * 1000)); // 转换为分钟
    }

    /**
     * 计算平均暂停时长
     */
    private double calculateAveragePauseDuration(List<SessionEvent> events) {
        List<SessionEvent> sortedEvents = events.stream()
                .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
                .collect(Collectors.toList());

        List<Double> pauseDurations = new ArrayList<>();
        Long pauseStartTime = null;

        for (SessionEvent event : sortedEvents) {
            if ("PAUSE".equals(event.getEventType())) {
                pauseStartTime = event.getClientTimestamp();
            } else if ("PLAY".equals(event.getEventType()) && pauseStartTime != null) {
                double duration = (event.getClientTimestamp() - pauseStartTime) / 1000.0;
                pauseDurations.add(duration);
                pauseStartTime = null;
            }
        }

        return pauseDurations.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
    }

    /**
     * 生成播放模式描述
     */
    private String generatePatternDescription(int pauseCount, int playCount, double averagePauseDuration) {
        if (pauseCount == 0) {
            return "Continuous playback without pauses";
        } else if (pauseCount > playCount * 2) {
            return "Excessive pause behavior";
        } else if (averagePauseDuration < 1.0) {
            return "Very short pause durations";
        } else {
            return "Normal playback pattern";
        }
    }
}
