package com.drex.core.service.business.rexy;

import com.drex.core.api.request.RexyConfigDTO;

import java.util.List;

public interface RexyConfigService {

    /**
     * Get the rexy config
     * @return
     */
    List<RexyConfigDTO> getRexys();

    RexyConfigDTO getDefaultRexyByLevel(String level);

    Boolean saveRexyConfig(RexyConfigDTO rexyConfigDTO);

    Boolean delRexyConfig(String rexyId);

    RexyConfigDTO getRexyConfigById(String id);
}
