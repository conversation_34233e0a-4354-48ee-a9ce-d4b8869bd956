package com.drex.core.service.business.youtube;

import com.drex.core.api.websocket.YouTubeWebSocketMessage;
import com.drex.core.service.cache.model.SessionEvent;

import java.util.List;

/**
 * 实时奖励服务接口
 * 负责实时计算进度和奖励发放
 */
public interface RealtimeRewardService {

    /**
     * 处理实时事件并计算奖励
     *
     * @param sessionId 会话ID
     * @param customerId 用户ID
     * @param videoId 视频ID
     * @param events 事件列表
     * @param videoDurationSeconds 视频总时长
     * @return 处理结果
     */
    RealtimeProcessingResult processRealtimeEvents(String sessionId, String customerId, String videoId,
                                                  List<SessionEvent> events, int videoDurationSeconds);

    /**
     * 计算当前进度
     *
     * @param effectiveWatchSeconds 有效观看时长
     * @param videoDurationSeconds 视频总时长
     * @return 进度等级（1、2、3）
     */
    Integer calculateProgress(int effectiveWatchSeconds, int videoDurationSeconds);

    /**
     * 检查是否可以获得奖励
     *
     * @param customerId 用户ID
     * @param sessionId 会话ID
     * @param videoId 视频ID
     * @param progress 进度等级
     * @return 是否可以获得奖励
     */
    boolean canReceiveReward(String customerId, String sessionId, String videoId, Integer progress);

    /**
     * 生成奖励
     *
     * @param customerId 用户ID
     * @param sessionId 会话ID
     * @param videoId 视频ID
     * @param progress 进度等级
     * @param trustScore 信任分数
     * @return 奖励生成结果
     */
    RewardGenerationResult generateReward(String customerId, String sessionId, String videoId, 
                                        Integer progress, double trustScore);

    /**
     * 获取YouTube奖励规则配置
     *
     * @param videoId 视频ID
     * @return 奖励规则配置
     */
    com.drex.core.api.rules.youtube.YouTubeRewardRules getYouTubeRewardRules(String videoId);

    /**
     * 实时处理结果
     */
    class RealtimeProcessingResult {
        private boolean success;
        private String message;
        private YouTubeWebSocketMessage.ProgressInfo progressInfo;
        private YouTubeWebSocketMessage.RewardInfo rewardInfo;
        private double trustScore;
        private boolean hasNewReward;

        public RealtimeProcessingResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public YouTubeWebSocketMessage.ProgressInfo getProgressInfo() { return progressInfo; }
        public void setProgressInfo(YouTubeWebSocketMessage.ProgressInfo progressInfo) { 
            this.progressInfo = progressInfo; 
        }
        public YouTubeWebSocketMessage.RewardInfo getRewardInfo() { return rewardInfo; }
        public void setRewardInfo(YouTubeWebSocketMessage.RewardInfo rewardInfo) { 
            this.rewardInfo = rewardInfo; 
        }
        public double getTrustScore() { return trustScore; }
        public void setTrustScore(double trustScore) { this.trustScore = trustScore; }
        public boolean isHasNewReward() { return hasNewReward; }
        public void setHasNewReward(boolean hasNewReward) { this.hasNewReward = hasNewReward; }
    }

    /**
     * 奖励生成结果
     */
    class RewardGenerationResult {
        private boolean success;
        private String message;
        private String rewardCode;
        private Long rewardScore;
        private String rewardLevel;
        private Long expirationTime;

        public RewardGenerationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getRewardCode() { return rewardCode; }
        public void setRewardCode(String rewardCode) { this.rewardCode = rewardCode; }
        public Long getRewardScore() { return rewardScore; }
        public void setRewardScore(Long rewardScore) { this.rewardScore = rewardScore; }
        public String getRewardLevel() { return rewardLevel; }
        public void setRewardLevel(String rewardLevel) { this.rewardLevel = rewardLevel; }
        public Long getExpirationTime() { return expirationTime; }
        public void setExpirationTime(Long expirationTime) { this.expirationTime = expirationTime; }
    }
}
