package com.drex.core.service.business.rexy;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

public class Base62Encoding {
    private static final String BASE62_CHARS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final Map<Character, Integer> CHAR_TO_INT_MAP = new HashMap<>();

    static {
        for (int i = 0; i < BASE62_CHARS.length(); i++) {
            CHAR_TO_INT_MAP.put(BASE62_CHARS.charAt(i), i);
        }
    }

    public static String encode(String numStr) {
        BigInteger num = new BigInteger(numStr);
        StringBuilder sb = new StringBuilder();
        while (num.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divAndRemainder = num.divideAndRemainder(BigInteger.valueOf(62));
            sb.append(BASE62_CHARS.charAt(divAndRemainder[1].intValue()));
            num = divAndRemainder[0];
        }
        return sb.reverse().toString();
    }

    public static String decode(String str) {
        BigInteger result = BigInteger.ZERO;
        for (int i = 0; i < str.length(); i++) {
            result = result.multiply(BigInteger.valueOf(62));
            result = result.add(BigInteger.valueOf(CHAR_TO_INT_MAP.get(str.charAt(i))));
        }
        return result.toString();
    }

    public static void main(String[] args) {
        String numStr = "123456789012345678901234567890";
        String encoded = encode(numStr);
        System.out.println("Encoded: " + encoded);

        String decoded = decode(encoded);
        System.out.println("Decoded: " + decoded);
    }
}
