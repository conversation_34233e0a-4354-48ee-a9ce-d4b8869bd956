package com.drex.core.service.chain.sign;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson2.JSON;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.kms.model.v20160120.AsymmetricSignRequest;
import com.aliyuncs.kms.model.v20160120.AsymmetricSignResponse;
import com.aliyuncs.kms.model.v20160120.GetPublicKeyRequest;
import com.aliyuncs.kms.model.v20160120.GetPublicKeyResponse;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.DERBitString;
import org.bouncycastle.asn1.DLSequence;
import org.bouncycastle.util.BigIntegers;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.web3j.crypto.ECDSASignature;
import org.web3j.crypto.Hash;
import org.web3j.crypto.Keys;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.Sign;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthEstimateGas;
import org.web3j.protocol.core.methods.response.EthSendTransaction;
import org.web3j.utils.Numeric;

import java.io.ByteArrayInputStream;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.Base64;
import java.util.Objects;

@Component
@Primary
@Slf4j
public class TransferSigner implements ISigner {

    @Value("${kiki.transfer.kms.key-id:alias/drex-dev}")
    private String keyId;

    @Value("${signer.transfer.kms-version-id:c38abd5e-0c80-4730-ba8f-3ba4d17dea53}")
    private String keyVersion;

    @Getter
    @Value("${signer.transfer.kms-public-key:}")
    private String publicKey;

    private final DefaultAcsClient acsClient;

    public TransferSigner(DefaultAcsClient acsClient) {
        this.acsClient = acsClient;
    }

    @SneakyThrows
    @PostConstruct
    public void init() {
        if (StringUtils.isBlank(publicKey)) {
            GetPublicKeyRequest getPublicKeyRequest = new GetPublicKeyRequest();
            getPublicKeyRequest.setKeyId(keyId);
            getPublicKeyRequest.setKeyVersionId(keyVersion);
            GetPublicKeyResponse acsResponse = acsClient.getAcsResponse(getPublicKeyRequest);
            String publicKey = acsResponse.getPublicKey()
                    .replace("-----BEGIN PUBLIC KEY-----\n", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replace("\n", "");
            byte[] decode = Base64.getDecoder().decode(publicKey);
            this.publicKey = getEthereumAddress(decode);
            log.info("TransferSigner public key: {}", this.publicKey);
        }
    }

    @SneakyThrows
    @Override
    public String signPrefixedMessage(byte[] message) {
        byte[] messageHash = Sign.getEthereumMessageHash(message);
        return signMessage(messageHash);
    }


    @SneakyThrows
    @Override
    public String signMessage(byte[] message) {
        Sign.SignatureData signatureData = signMessageInner(message);
        byte[] bytes = ArrayUtil.addAll(signatureData.getR(), signatureData.getS(), signatureData.getV());
        return Numeric.toHexString(bytes);
    }


    @SneakyThrows
    private Sign.SignatureData signMessageInner(byte[] message) {
        AsymmetricSignRequest signRequest = new AsymmetricSignRequest();
        signRequest.setKeyId(keyId);
        signRequest.setAlgorithm("ECDSA_SHA_256");
        signRequest.setDigest(Base64.getEncoder().encodeToString(message));
        signRequest.setKeyVersionId(keyVersion);
        AsymmetricSignResponse acsResponse = acsClient.getAcsResponse(signRequest);
        String value = acsResponse.getValue();
        byte[] signature = Base64.getDecoder().decode(value);
        return convertSigToSignatureData(message, signature);
    }

    /**
     * 交易签名
     *
     * @param rawTransaction 交易原数据
     * @return 签名
     */
    public byte[] signTransaction(RawTransaction rawTransaction) {
        byte[] encodedTransaction = TransactionEncoder.encode(rawTransaction);
        Sign.SignatureData signatureData = this.signMessageInner(Hash.sha3(encodedTransaction));
        return TransactionEncoder.encode(rawTransaction, signatureData);
    }

    @SneakyThrows
    public String sendTransaction(Integer chainId, Web3j web3j, String to, String calldata) {
        return sendTransaction(chainId, web3j, to, calldata, BigInteger.ZERO);
    }


    @SneakyThrows
    @Override
    public String sendTransaction(Integer chainId, Web3j web3j, String to, String calldata, BigInteger value) {
        BigInteger nonce = web3j.ethGetTransactionCount(publicKey, DefaultBlockParameter.valueOf("latest"))
                .send().getTransactionCount();
        BigInteger gasPrice = web3j.ethGasPrice().send().getGasPrice();
        EthEstimateGas ethEstimateGas = web3j.ethEstimateGas(Transaction.createEthCallTransaction(publicKey, to, calldata, value)).send();
        if (ethEstimateGas.hasError()) {
            throw new RuntimeException("Error estimating gas: " + JSON.toJSONString(ethEstimateGas.getError()));
        }
        BigInteger gasLimit = ethEstimateGas.getAmountUsed();
        BigInteger maxPriorityFeePerGas = web3j.ethMaxPriorityFeePerGas().send().getMaxPriorityFeePerGas()
                .multiply(BigInteger.valueOf(12)).divide(BigInteger.valueOf(10)); //多给一点点
        RawTransaction etherTransaction = RawTransaction.createTransaction(
                chainId,
                nonce,
                gasLimit,
                to,
                value,
                calldata,
                maxPriorityFeePerGas,
                gasPrice.multiply(BigInteger.valueOf(2)).add(maxPriorityFeePerGas)
        );
        byte[] signedTransaction = signTransaction(etherTransaction);
        EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(Numeric.toHexString(signedTransaction)).send();
        if (ethSendTransaction.hasError()) {
            throw new RuntimeException("Error sending transaction: " + ethSendTransaction.getError().getMessage());
        } else {
            String transactionHash = ethSendTransaction.getTransactionHash();
            log.info("[KMS] send transaction hash is {}", transactionHash);
            return transactionHash;
        }
    }

    @SuppressWarnings(value = "all")
    private Sign.SignatureData convertSigToSignatureData(byte[] ethereumMessageHash, byte[] signature) {
        ASN1Sequence seq = ASN1Sequence.getInstance(signature);
        BigInteger r = ASN1Integer.getInstance(seq.getObjectAt(0)).getValue();
        BigInteger s = ASN1Integer.getInstance(seq.getObjectAt(1)).getValue();
        BigInteger secp256k1N = new BigInteger("fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", 16);
        BigInteger secp256k1halfN = secp256k1N.divide(BigInteger.valueOf(2));
        BigInteger adjustedS = s.compareTo(secp256k1halfN) > 0 ? secp256k1N.subtract(s) : s;
        byte[] rBytes = BigIntegers.asUnsignedByteArray(32, r);
        byte[] sBytes = BigIntegers.asUnsignedByteArray(32, adjustedS);
        String expectedEthAddr;
        expectedEthAddr = Objects.requireNonNullElseGet(publicKey, () -> publicKey);
        int v = 27;
        String pubKey = recoverAddress(ethereumMessageHash, rBytes, sBytes, (byte) v);
        if (!pubKey.equalsIgnoreCase(expectedEthAddr)) {
            v = 28;
            pubKey = recoverAddress(ethereumMessageHash, rBytes, sBytes, (byte) v);
            if (!pubKey.equalsIgnoreCase(expectedEthAddr)) {
                throw new RuntimeException("Could not recover public key from signature");
            }
        }
        byte[] vBytes = Numeric.toBytesPadded(BigInteger.valueOf(v), 1);
        return new Sign.SignatureData(vBytes, rBytes, sBytes);
    }


    public String recoverAddress(byte[] digest, byte[] r, byte[] s, byte v) {
        Sign.SignatureData signatureData = new Sign.SignatureData(v, r, s);
        int header = 0;
        for (byte b : signatureData.getV()) {
            header = (header << 8) + (b & 0xFF);
        }
        int recId = header - 27;
        BigInteger key = Sign.recoverFromSignature(
                recId,
                new ECDSASignature(
                        new BigInteger(1, signatureData.getR()), new BigInteger(1, signatureData.getS())),
                digest);
        if (key == null) {
            throw new RuntimeException("Could not recover public key from signature");
        }
        return ("0x" + Keys.getAddress(key)).trim();
    }


    private String getEthereumAddress(byte[] publicKey) {
        byte[] pubKeyBuffer = decodePublicKey(publicKey);
        pubKeyBuffer = removePrefix(pubKeyBuffer);
        byte[] addressBytes = Hash.sha3(pubKeyBuffer);
        return "0x" + Hex.toHexString(addressBytes).substring(24);
    }

    @SneakyThrows
    @SuppressWarnings(value = "all")
    private static byte[] decodePublicKey(byte[] publicKey) {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(publicKey);
        ASN1InputStream asn1InputStream = new ASN1InputStream(inputStream);
        ASN1Primitive asn1Primitive = asn1InputStream.readObject();
        if (asn1Primitive instanceof DLSequence sequence) {
            ASN1Primitive bitStringObj = sequence.getObjectAt(1).toASN1Primitive();
            if (bitStringObj instanceof DERBitString bitString) {
                return bitString.getOctets();
            }
        }
        throw new RuntimeException("Could not decode public key");
    }

    private byte[] removePrefix(byte[] pubKeyBuffer) {
        return Arrays.copyOfRange(pubKeyBuffer, 1, pubKeyBuffer.length);
    }
}
