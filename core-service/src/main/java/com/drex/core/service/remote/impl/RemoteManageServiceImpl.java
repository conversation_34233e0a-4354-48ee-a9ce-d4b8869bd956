package com.drex.core.service.remote.impl;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.exception.NacosException;
import com.drex.core.api.RemoteManageService;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.request.NoticeDTO;
import com.drex.core.api.request.OperateConfig;
import com.drex.core.api.response.ManageResponse;
import com.drex.core.service.business.notice.NoticeService;
import com.drex.core.service.business.rexy.impl.PropertiesConfigService;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class RemoteManageServiceImpl implements RemoteManageService {

    private static final String DREX_CORE = "drex-core";
    private static final String INNER_TEST_USERS = "inner_test_users";

    @Resource
    private PropertiesConfigService propertiesConfigService;

    @Resource
    private NoticeService noticeService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public Response<List<OperateConfig>> getOperateConfigs(List<String> keys) {
        log.info("getOperateConfigs keys: {}", keys);
        List<OperateConfig> list = new ArrayList<>();
        try {
            String config = propertiesConfigService.getConfigService().getConfig(DREX_CORE, DREX_CORE, 1000);
            if (StringUtils.isBlank(config)) {
                return Response.success(new ArrayList<>());
            }
            List<OperateConfig> collect = keys.stream().map(s -> {
                OperateConfig operateConfig = new OperateConfig();
                operateConfig.setParamterKey(s);
                operateConfig.setParamterValue(getConfigValue(config, s));
                return operateConfig;
            }).collect(Collectors.toList());
            return Response.success(collect);
        } catch (NacosException e) {
            log.error("getOperateConfigs error:{}", keys, e);
            return Response.success(list);
        }
    }

    public String getConfigValue(String configContent, String key) {
        if (configContent == null || key == null) {
            return null;
        }
        try {
            Properties properties = new Properties();
            String filteredContent = configContent.replaceAll("(?m)^#.*\\n?", "");
            properties.load(new StringReader(filteredContent));
            return properties.getProperty(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public Response<ManageResponse> refreshOperateConfig(OperateConfig operateConfig) {
        log.info("Refresh operate config: {}", operateConfig);
        ManageResponse response = new ManageResponse();
        response.setSuccess(false);
        try {
            // 1. 获取当前完整配置
            String originalConfig = propertiesConfigService.getConfigService().getConfig(DREX_CORE, DREX_CORE, 1000);

            // 2. 构建正则表达式（匹配原key的行）
            Pattern pattern = Pattern.compile(
                    "^(" + Pattern.quote(operateConfig.getParamterKey()) + "\\s*[=:]\\s*)(.*?)(\\s*(?:#.*)?)$",
                    Pattern.MULTILINE
            );

            // 3. 更新指定key的值
            Matcher matcher = pattern.matcher(originalConfig);
            if (!matcher.find()) {
                throw new NacosException(NacosException.CLIENT_INVALID_PARAM, "未找到配置项: " + operateConfig.getParamterKey());
            }
            String updatedConfig = matcher.replaceAll("$1" + operateConfig.getParamterValue() + "$3");

            // 4. 发布更新后的完整配置
            boolean result = propertiesConfigService.getConfigService().publishConfig(
                    DREX_CORE,
                    DREX_CORE,
                    updatedConfig,
                    ConfigType.PROPERTIES.getType()
            );
            response.setSuccess(result);
            return Response.success(response);
        } catch (NacosException e) {
            log.error("syncOperateConfig error:{}", operateConfig, e);
            response.setSuccess(false);
            return Response.success(response);
        }
    }

    @Override
    public Response<Boolean> saveNotice(NoticeDTO noticeDTO) {
        log.info("saveNotice noticeDTO: {}", noticeDTO);
        try {
            Boolean result = noticeService.saveNotice(noticeDTO);
            return Response.success(result);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<Boolean> addInnerTestUsers(List<String> customerIds) {
        // 将 customerIds 保存在 redis缓存中，供后续使用
        log.info("addInnerTestUsers customerIds: {}", customerIds);
        try {
            if (customerIds == null || customerIds.isEmpty()) {
                return Response.error(CoreResponseCode.INVALID_PARAMETER.getCode(), "customerIds cannot be empty");
            }
            // 将用户ID添加到 Redis Set 中
            for (String customerId : customerIds) {
                if (StringUtils.isNotBlank(customerId)) {
                    redisTemplate.opsForSet().add(INNER_TEST_USERS, customerId);
                }
            }
            // 返回成功响应
            return Response.success(true);
        } catch (Exception e) {
            log.error("Failed to add inner test users to Redis", e);
            return Response.error(CoreResponseCode.DATA_OPERATE_FAIL.getCode(), "Failed to add inner test users: " + e.getMessage());
        }
    }

    @Override
    public Response<List<NoticeDTO>> getNotices(String customerId, Long beginTime) {
        log.info("getNotices customerId: {}, beginTime {}", customerId, beginTime);
        if (beginTime == null || beginTime == 0) {
            beginTime = System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000; // 避免一次性查出历史所有数据
        }
        try {
            List<NoticeDTO> noticeDTOList = noticeService.getByStatus("Completed", beginTime);
            log.info("getNotices noticeDTOList: {}", noticeDTOList);
            if (!noticeDTOList.isEmpty()) {
                // 如果 sendTo 是 2，那么要检测 customerId 是否在白名单中
                noticeDTOList = noticeDTOList.stream().filter(noticeDTO -> {
                    if (noticeDTO.getSendTo() == 2) {
                        // 检测 customerId 是否在内测用户白名单中
                        Boolean isMember = redisTemplate.opsForSet().isMember(INNER_TEST_USERS, customerId);
                        return Boolean.TRUE.equals(isMember);
                    }
                    return true;
                }).collect(Collectors.toList());
            }
            return Response.success(noticeDTOList);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

}
