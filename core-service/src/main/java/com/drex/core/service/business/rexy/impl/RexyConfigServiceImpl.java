package com.drex.core.service.business.rexy.impl;

import com.drex.core.dal.tablestore.builder.RexyConfigBuilder;
import com.drex.core.dal.tablestore.model.RexyConfig;
import com.drex.core.api.request.RexyConfigDTO;
import com.drex.core.service.business.rexy.RexyConfigService;
import com.drex.core.service.mapperstruct.RexyMapperStruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RexyConfigServiceImpl implements RexyConfigService {

    @Resource
    private RexyConfigBuilder rexyConfigBuilder;
    @Resource
    private RexyMapperStruct rexyMapperStruct;

    @Override
    public List<RexyConfigDTO> getRexys() {
        return rexyMapperStruct.toRexyConfigDTOList(rexyConfigBuilder.listAll());
    }

    @Override
    public RexyConfigDTO getDefaultRexyByLevel(String level) {
        RexyConfig defaultRexy = rexyConfigBuilder.getDefaultByLevel(level);
        return rexyMapperStruct.toRexyConfigDTO(defaultRexy);
    }

    public Boolean saveRexyConfig(RexyConfigDTO rexyConfigDTO) {
        return rexyConfigBuilder.insert(rexyMapperStruct.toRexyConfig(rexyConfigDTO));
    }

    @Override
    public Boolean delRexyConfig(String rexyId) {
        return rexyConfigBuilder.deleteById(rexyId);
    }

    @Override
    public RexyConfigDTO getRexyConfigById(String id) {
        return rexyMapperStruct.toRexyConfigDTO(rexyConfigBuilder.getById(id));
    }
}
