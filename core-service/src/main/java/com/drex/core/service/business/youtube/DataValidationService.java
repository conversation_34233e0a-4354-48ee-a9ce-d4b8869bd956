package com.drex.core.service.business.youtube;

import com.drex.core.api.request.RexyReportRequest;
import com.drex.core.service.cache.model.SessionEvent;

import java.util.List;
import java.util.Map;

/**
 * 数据验证服务接口
 * 负责验证输入数据的格式、签名和时间戳等基础验证
 */
public interface DataValidationService {

    /**
     * 验证事件上报请求的基础数据
     *
     * @param request 事件上报请求
     * @return 验证结果
     */
    ValidationResult validateReportRequest(RexyReportRequest request);

    /**
     * 验证并解密事件数据
     *
     * @param encryptedData 加密的事件数据
     * @param signature 数据签名
     * @param secretKey 密钥
     * @return 解密后的事件列表
     */
    List<SessionEvent> validateAndDecryptEvents(String encryptedData, String signature, String secretKey);

    /**
     * 验证事件数据的时间戳
     *
     * @param events 事件列表
     * @param serverTimestamp 服务器时间戳
     * @return 时间戳验证结果
     */
    TimestampValidationResult validateTimestamps(List<SessionEvent> events, Long serverTimestamp);

    /**
     * 验证事件序列的合理性
     *
     * @param events 事件列表
     * @return 事件序列验证结果
     */
    EventSequenceValidationResult validateEventSequence(List<SessionEvent> events);

    /**
     * 检测重复事件
     *
     * @param events 事件列表
     * @return 重复事件检测结果
     */
    DuplicateEventDetectionResult detectDuplicateEvents(List<SessionEvent> events);

    /**
     * 验证事件数据格式
     *
     * @param event 单个事件
     * @return 格式验证结果
     */
    FormatValidationResult validateEventFormat(SessionEvent event);

    /**
     * 计算数据验证相关的欺诈指标
     *
     * @param events 事件列表
     * @param validationResults 各项验证结果
     * @return 欺诈指标映射 (指标类型 -> 指标值)
     */
    Map<String, Double> calculateFraudIndicators(List<SessionEvent> events, 
                                                 ValidationResult validationResults);

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private String errorCode;
        private Map<String, Object> details;

        // 构造函数、getter、setter
        public ValidationResult(boolean valid, String errorMessage, String errorCode) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.errorCode = errorCode;
        }

        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
        public String getErrorCode() { return errorCode; }
        public Map<String, Object> getDetails() { return details; }
        public void setDetails(Map<String, Object> details) { this.details = details; }
    }

    /**
     * 时间戳验证结果
     */
    class TimestampValidationResult {
        private boolean valid;
        private double maxTimeDifference;
        private int anomalousEventCount;
        private List<String> anomalousEventIds;

        public TimestampValidationResult(boolean valid, double maxTimeDifference, 
                                       int anomalousEventCount, List<String> anomalousEventIds) {
            this.valid = valid;
            this.maxTimeDifference = maxTimeDifference;
            this.anomalousEventCount = anomalousEventCount;
            this.anomalousEventIds = anomalousEventIds;
        }

        public boolean isValid() { return valid; }
        public double getMaxTimeDifference() { return maxTimeDifference; }
        public int getAnomalousEventCount() { return anomalousEventCount; }
        public List<String> getAnomalousEventIds() { return anomalousEventIds; }
    }

    /**
     * 事件序列验证结果
     */
    class EventSequenceValidationResult {
        private boolean valid;
        private int sequenceErrors;
        private List<String> invalidTransitions;
        private Map<String, Object> sequenceAnalysis;

        public EventSequenceValidationResult(boolean valid, int sequenceErrors, 
                                           List<String> invalidTransitions) {
            this.valid = valid;
            this.sequenceErrors = sequenceErrors;
            this.invalidTransitions = invalidTransitions;
        }

        public boolean isValid() { return valid; }
        public int getSequenceErrors() { return sequenceErrors; }
        public List<String> getInvalidTransitions() { return invalidTransitions; }
        public Map<String, Object> getSequenceAnalysis() { return sequenceAnalysis; }
        public void setSequenceAnalysis(Map<String, Object> sequenceAnalysis) { 
            this.sequenceAnalysis = sequenceAnalysis; 
        }
    }

    /**
     * 重复事件检测结果
     */
    class DuplicateEventDetectionResult {
        private boolean hasDuplicates;
        private int duplicateCount;
        private List<String> duplicateEventIds;
        private Map<String, Integer> duplicateGroups;

        public DuplicateEventDetectionResult(boolean hasDuplicates, int duplicateCount, 
                                           List<String> duplicateEventIds) {
            this.hasDuplicates = hasDuplicates;
            this.duplicateCount = duplicateCount;
            this.duplicateEventIds = duplicateEventIds;
        }

        public boolean isHasDuplicates() { return hasDuplicates; }
        public int getDuplicateCount() { return duplicateCount; }
        public List<String> getDuplicateEventIds() { return duplicateEventIds; }
        public Map<String, Integer> getDuplicateGroups() { return duplicateGroups; }
        public void setDuplicateGroups(Map<String, Integer> duplicateGroups) { 
            this.duplicateGroups = duplicateGroups; 
        }
    }

    /**
     * 格式验证结果
     */
    class FormatValidationResult {
        private boolean valid;
        private List<String> formatErrors;
        private Map<String, Object> validationDetails;

        public FormatValidationResult(boolean valid, List<String> formatErrors) {
            this.valid = valid;
            this.formatErrors = formatErrors;
        }

        public boolean isValid() { return valid; }
        public List<String> getFormatErrors() { return formatErrors; }
        public Map<String, Object> getValidationDetails() { return validationDetails; }
        public void setValidationDetails(Map<String, Object> validationDetails) { 
            this.validationDetails = validationDetails; 
        }
    }
}
