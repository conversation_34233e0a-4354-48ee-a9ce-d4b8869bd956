package com.drex.core.service.business.youtube.impl;

import com.drex.core.service.CoreProperties;
import com.drex.core.service.business.youtube.SystemConfigurationService;
import com.drex.core.service.business.youtube.TrustScoreCalculationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 信任分数计算服务实现
 * 
 * 注意：这是基础框架实现，具体的欺诈分计算公式将根据后续提供的算法进行调整
 */
@Slf4j
@Service
public class TrustScoreCalculationServiceImpl implements TrustScoreCalculationService {

    @Autowired
    private SystemConfigurationService systemConfigurationService;

    @Autowired
    private CoreProperties coreProperties;

    @Override
    public TrustScoreResult calculateTrustScore(Map<String, Double> fraudIndicators, 
                                               Map<String, Double> weights) {
        try {
            log.debug("Calculating trust score with {} indicators", fraudIndicators.size());

            // TODO: 这里将根据后续提供的具体公式进行实现
            // 当前使用基础的加权平均算法作为框架
            
            double rawScore = calculateRawScore(fraudIndicators, weights);
            double normalizedScore = normalizeScore(rawScore);
            double finalTrustScore = applyScoreAdjustments(normalizedScore, fraudIndicators);
            
            RiskLevel riskLevel = calculateRiskLevel(finalTrustScore);
            double confidenceScore = calculateConfidenceScore(fraudIndicators, 1.0);

            TrustScoreResult result = new TrustScoreResult(finalTrustScore, rawScore, riskLevel);
            result.setNormalizedScore(normalizedScore);
            result.setConfidenceScore(confidenceScore);
            result.setContributionByIndicator(calculateContributions(fraudIndicators, weights));
            result.setCalculationDetails(buildCalculationDetails(fraudIndicators, weights, rawScore));

            log.debug("Trust score calculated: final={}, raw={}, risk={}", 
                     finalTrustScore, rawScore, riskLevel.getCode());

            return result;

        } catch (Exception e) {
            log.error("Failed to calculate trust score", e);
            // 返回默认的低信任分数
            return new TrustScoreResult(0.0, 0.0, RiskLevel.CRITICAL);
        }
    }

    @Override
    public CategorizedTrustScoreResult calculateCategorizedTrustScore(
            Map<String, Double> dataValidationIndicators,
            Map<String, Double> playbackAnalysisIndicators,
            Map<String, Double> focusActivityIndicators,
            Map<String, Double> environmentAnalysisIndicators) {
        
        try {
            Map<String, Double> categoryWeights = getCategoryWeights();

            // 计算各分类的分数
            double dataValidationScore = calculateCategoryScore(dataValidationIndicators);
            double playbackAnalysisScore = calculateCategoryScore(playbackAnalysisIndicators);
            double focusActivityScore = calculateCategoryScore(focusActivityIndicators);
            double environmentAnalysisScore = calculateCategoryScore(environmentAnalysisIndicators);

            // 计算综合分数
            double overallScore = 
                    dataValidationScore * categoryWeights.getOrDefault("dataValidation", 0.1) +
                    playbackAnalysisScore * categoryWeights.getOrDefault("playbackAnalysis", 0.3) +
                    focusActivityScore * categoryWeights.getOrDefault("focusActivity", 0.2) +
                    environmentAnalysisScore * categoryWeights.getOrDefault("environmentAnalysis", 0.4);

            CategorizedTrustScoreResult result = new CategorizedTrustScoreResult(
                    overallScore, dataValidationScore, playbackAnalysisScore, 
                    focusActivityScore, environmentAnalysisScore);
            
            result.setCategoryWeights(categoryWeights);
            result.setOverallRiskLevel(calculateRiskLevel(overallScore));

            return result;

        } catch (Exception e) {
            log.error("Failed to calculate categorized trust score", e);
            return new CategorizedTrustScoreResult(0.0, 0.0, 0.0, 0.0, 0.0);
        }
    }

    @Override
    public Map<String, Double> getDefaultWeights() {
        try {
            // 优先从系统配置获取，如果没有则使用默认值
            Map<String, Double> configWeights = systemConfigurationService.getFraudDetectionWeights();
            if (configWeights != null && !configWeights.isEmpty()) {
                return configWeights;
            }
        } catch (Exception e) {
            log.warn("Failed to get weights from configuration, using defaults", e);
        }

        // 返回基于欺诈指标阈值设计的默认权重配置
        Map<String, Double> defaultWeights = new HashMap<>();

        // 数据验证类指标权重 (总权重约0.1)
        defaultWeights.put("TIMESTAMP_ANOMALY", 0.05);
        defaultWeights.put("EVENT_ORDER_ANOMALY", 0.03);
        defaultWeights.put("DUPLICATE_EVENT", 0.02);

        // 播放分析类指标权重 (总权重约0.3)
        defaultWeights.put("EXCESSIVE_PLAYBACK_SPEED", 0.12);
        defaultWeights.put("ABNORMAL_SEEK", 0.08);
        defaultWeights.put("ABNORMAL_COMPLETION_PERCENTAGE", 0.10);

        // 焦点活动类指标权重 (总权重约0.2)
        defaultWeights.put("LOW_FOCUS_DURATION", 0.12);
        defaultWeights.put("LONG_IDLE_DURATION", 0.08);

        // 环境分析类指标权重 (总权重约0.4)
        defaultWeights.put("ENVIRONMENT_INCONSISTENCY", 0.15);
        defaultWeights.put("FINGERPRINT_DUPLICATION", 0.10);
        defaultWeights.put("MALICIOUS_IP", 0.15);

        return defaultWeights;
    }

    @Override
    public Map<String, Double> getCategoryWeights() {
        Map<String, Double> categoryWeights = new HashMap<>();
        categoryWeights.put("dataValidation", coreProperties.getDataValidationWeight());
        categoryWeights.put("playbackAnalysis", coreProperties.getPlaybackAnalysisWeight());
        categoryWeights.put("focusActivity", coreProperties.getFocusActivityWeight());
        categoryWeights.put("environmentAnalysis", coreProperties.getEnvironmentAnalysisWeight());
        return categoryWeights;
    }

    @Override
    public boolean updateWeights(Map<String, Double> weights) {
        try {
            WeightValidationResult validation = validateWeights(weights);
            if (!validation.isValid()) {
                log.warn("Invalid weights provided: {}", validation.getValidationErrors());
                return false;
            }

            return systemConfigurationService.updateFraudDetectionWeights(weights);
        } catch (Exception e) {
            log.error("Failed to update weights", e);
            return false;
        }
    }

    @Override
    public WeightValidationResult validateWeights(Map<String, Double> weights) {
        List<String> errors = new ArrayList<>();
        double totalWeight = 0.0;

        for (Map.Entry<String, Double> entry : weights.entrySet()) {
            Double weight = entry.getValue();
            if (weight == null || weight < 0.0 || weight > 1.0) {
                errors.add("Invalid weight for " + entry.getKey() + ": " + weight);
            } else {
                totalWeight += weight;
            }
        }

        // 检查总权重是否合理（允许一定的误差）
        if (Math.abs(totalWeight - 1.0) > 0.01) {
            errors.add("Total weight should be close to 1.0, but got: " + totalWeight);
        }

        WeightValidationResult result = new WeightValidationResult(errors.isEmpty(), totalWeight);
        if (!errors.isEmpty()) {
            result.setValidationErrors(errors);
        }

        return result;
    }

    @Override
    public RiskLevel calculateRiskLevel(double trustScore) {
        return RiskLevel.fromScore(trustScore);
    }

    @Override
    public TrustScoreThresholds getThresholds() {
        try {
            SystemConfigurationService.TrustScoreThresholds configThresholds = 
                    systemConfigurationService.getTrustScoreThresholds();
            if (configThresholds != null) {
                return new TrustScoreThresholds(
                        configThresholds.getLowRiskThreshold(),
                        configThresholds.getMediumRiskThreshold(),
                        configThresholds.getHighRiskThreshold(),
                        configThresholds.getRewardEligibilityThreshold(),
                        configThresholds.getAutoRejectThreshold()
                );
            }
        } catch (Exception e) {
            log.warn("Failed to get thresholds from configuration, using defaults", e);
        }

        // 返回默认阈值
        return new TrustScoreThresholds(0.3, 0.6, 0.8, 
                coreProperties.getTrustScoreThreshold(), coreProperties.getHighRiskThreshold());
    }

    @Override
    public TrustScoreTrendAnalysis analyzeTrustScoreTrend(String customerId, List<Double> recentScores) {
        if (recentScores == null || recentScores.size() < 2) {
            return new TrustScoreTrendAnalysis("STABLE", 0.0, 
                    recentScores != null && !recentScores.isEmpty() ? recentScores.get(0) : 0.0);
        }

        // 计算趋势斜率
        double trendSlope = calculateTrendSlope(recentScores);
        String trendDirection = determineTrendDirection(trendSlope);
        double averageScore = recentScores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = calculateVariance(recentScores, averageScore);

        TrustScoreTrendAnalysis analysis = new TrustScoreTrendAnalysis(trendDirection, trendSlope, averageScore);
        analysis.setScoreVariance(variance);
        analysis.setVolatile(variance > 0.1); // 方差大于0.1认为是波动的

        return analysis;
    }

    @Override
    public double calculateConfidenceScore(Map<String, Double> fraudIndicators, double dataQuality) {
        // 基于指标数量和数据质量计算置信度
        int indicatorCount = fraudIndicators.size();
        double indicatorConfidence = Math.min(1.0, indicatorCount / 10.0); // 假设10个指标为满分
        
        return (indicatorConfidence * 0.7 + dataQuality * 0.3);
    }

    /**
     * 计算原始分数
     * 基于提供的欺诈指标阈值设计实现
     * 公式：TrustScore = Σ(IndicatorValue × Weight)
     */
    private double calculateRawScore(Map<String, Double> fraudIndicators, Map<String, Double> weights) {
        double weightedScore = 0.0;
        double totalWeight = 0.0;

        for (Map.Entry<String, Double> entry : fraudIndicators.entrySet()) {
            String indicator = entry.getKey();
            Double indicatorValue = entry.getValue();
            Double weight = weights.get(indicator);

            if (indicatorValue != null && weight != null) {
                // 应用具体的欺诈指标计算
                double processedIndicatorValue = processIndicatorValue(indicator, indicatorValue);
                weightedScore += processedIndicatorValue * weight;
                totalWeight += weight;
            }
        }

        // 返回加权平均分数
        return totalWeight > 0 ? weightedScore / totalWeight : 0.0;
    }

    /**
     * 处理特定指标值，应用阈值逻辑
     */
    private double processIndicatorValue(String indicatorType, double rawValue) {
        switch (indicatorType) {
            case "REPEATED_EVENTS":
                // 已在PlaybackAnalysisService中按公式计算
                return rawValue;

            case "ABNORMAL_COMPLETION_PERCENTAGE":
                // 已在PlaybackAnalysisService中按公式计算
                return rawValue;

            case "LOW_FOCUS_DURATION":
                // 已在FocusAndActivityService中按公式计算
                return rawValue;

            case "LONG_IDLE_DURATION":
                // 已在FocusAndActivityService中按公式计算
                return rawValue;

            case "ENVIRONMENT_INCONSISTENCY":
                // 已在EnvironmentAnalysisService中按公式计算
                return rawValue;

            case "TIMESTAMP_ANOMALY":
                // 已在DataValidationService中按公式计算
                return rawValue;

            case "EVENT_ORDER_ANOMALY":
                // 直接触发：若检测到任何顺序异常，IndicatorValue = 1.0
                return rawValue;

            case "EXCESSIVE_PLAYBACK_SPEED":
                // 已在PlaybackAnalysisService中按公式计算
                return rawValue;

            case "ABNORMAL_SEEK":
                // 已在PlaybackAnalysisService中按公式计算
                return rawValue;

            case "FINGERPRINT_DUPLICATION":
                // 已在EnvironmentAnalysisService中按公式计算
                return rawValue;

            case "MALICIOUS_IP":
                // 直接触发：IP在ipReputations表标记为恶意，IndicatorValue = 1.0
                return rawValue;

            default:
                return rawValue;
        }
    }

    /**
     * 标准化分数到0-1范围
     */
    private double normalizeScore(double rawScore) {
        return Math.max(0.0, Math.min(1.0, rawScore));
    }

    /**
     * 应用分数调整
     * TODO: 这里可以根据具体需求添加额外的调整逻辑
     */
    private double applyScoreAdjustments(double normalizedScore, Map<String, Double> fraudIndicators) {
        // 当前直接返回标准化分数，后续可以添加调整逻辑
        return normalizedScore;
    }

    /**
     * 计算各指标的贡献度
     */
    private Map<String, Double> calculateContributions(Map<String, Double> fraudIndicators, 
                                                      Map<String, Double> weights) {
        Map<String, Double> contributions = new HashMap<>();
        double totalContribution = 0.0;

        for (Map.Entry<String, Double> entry : fraudIndicators.entrySet()) {
            String indicator = entry.getKey();
            Double indicatorValue = entry.getValue();
            Double weight = weights.get(indicator);

            if (indicatorValue != null && weight != null) {
                double contribution = indicatorValue * weight;
                contributions.put(indicator, contribution);
                totalContribution += contribution;
            }
        }
        final double contributionSum = totalContribution;
        // 标准化贡献度
        if (totalContribution > 0) {
            contributions.replaceAll((k, v) -> v / contributionSum);
        }

        return contributions;
    }

    /**
     * 构建计算详情
     */
    private Map<String, Object> buildCalculationDetails(Map<String, Double> fraudIndicators, 
                                                       Map<String, Double> weights, 
                                                       double rawScore) {
        Map<String, Object> details = new HashMap<>();
        details.put("indicatorCount", fraudIndicators.size());
        details.put("weightCount", weights.size());
        details.put("rawScore", rawScore);
        details.put("calculationMethod", "weighted_average");
        details.put("timestamp", System.currentTimeMillis());
        return details;
    }

    /**
     * 计算分类分数
     */
    private double calculateCategoryScore(Map<String, Double> indicators) {
        if (indicators == null || indicators.isEmpty()) {
            return 1.0; // 没有指标时返回最高分
        }

        return indicators.values().stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(1.0);
    }

    /**
     * 计算趋势斜率
     */
    private double calculateTrendSlope(List<Double> scores) {
        int n = scores.size();
        double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;

        for (int i = 0; i < n; i++) {
            sumX += i;
            sumY += scores.get(i);
            sumXY += i * scores.get(i);
            sumX2 += i * i;
        }

        return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    }

    /**
     * 确定趋势方向
     */
    private String determineTrendDirection(double slope) {
        if (slope > 0.01) return "IMPROVING";
        if (slope < -0.01) return "DECLINING";
        return "STABLE";
    }

    /**
     * 计算方差
     */
    private double calculateVariance(List<Double> scores, double mean) {
        return scores.stream()
                .mapToDouble(score -> Math.pow(score - mean, 2))
                .average()
                .orElse(0.0);
    }
}
