package com.drex.core.service.business.rexy;

import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.core.dal.tablestore.model.RexyClaimRecord;
import com.drex.customer.api.response.CustomerDTO;

import java.math.BigDecimal;
import java.util.List;

public interface RexyClaimRecordService {

    /**
     * 领取记录
     *
     * @param customerId
     * @param basketCode
     * @param eoaAddress
     * @return
     */
    RexyClaimRecord tryClaim(String customerId, String eoaAddress, String basketCode, CustomerRexyBasketsDTO customerRexyBaskets);

    RexyClaimRecord getByRecordId(String recordId);

    Boolean confirmClaim(RexyClaimRecord record);

    Boolean updateHashCode(RexyClaimRecord record);

    List<RexyClaimRecord> getByStatus(String status);
}
