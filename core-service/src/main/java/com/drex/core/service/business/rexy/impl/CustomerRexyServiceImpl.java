package com.drex.core.service.business.rexy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.RexyConfigDTO;
import com.drex.core.dal.tablestore.builder.CustomerRexyBasketsBuilder;
import com.drex.core.dal.tablestore.builder.CustomerRexyBuilder;
import com.drex.core.dal.tablestore.builder.RexyConfigBuilder;
import com.drex.core.dal.tablestore.model.CustomerRexy;
import com.drex.core.dal.tablestore.model.CustomerRexyBaskets;
import com.drex.core.dal.tablestore.model.RexyConfig;
import com.drex.core.service.business.rexy.CustomerRexyService;
import com.drex.core.service.business.rexy.RexyConfigService;
import com.drex.core.service.mapperstruct.RexyMapperStruct;
import com.drex.customer.api.RemoteCustomerService;
import com.drex.customer.api.response.CustomerDTO;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerRexyServiceImpl implements CustomerRexyService {

    @Resource
    private RexyConfigService rexyConfigService;
    @Resource
    private RexyMapperStruct rexyMapperStruct;
    @Resource
    private CustomerRexyBuilder customerRexyBuilder;
    @Resource
    private RexyConfigBuilder rexyConfigBuilder;
    @Resource
    private CustomerRexyBasketsBuilder customerRexyBasketsBuilder;
    @Resource
    private RemoteCustomerService remoteCustomerService;


    @Override
    public void initCustomerRexy(CustomerDTO customerDTO) {
        // 初始化用户拥有的恐龙
        RexyConfigDTO rexyConfig = rexyConfigService.getDefaultRexyByLevel(customerDTO.getKycLevel());
        CustomerRexy customerRexy = rexyMapperStruct.toCustomerRexy(customerDTO, rexyConfig);
        customerRexy.setStatus(RexyConstant.CustomerRexyStatus.ACTIVE.name());
        customerRexyBuilder.insert(customerRexy);
    }

    @Override
    public List<CustomerRexy> getByCustomerAndStatus(String customerId, RexyConstant.CustomerRexyStatus status) {
        List<CustomerRexy> rexyList = customerRexyBuilder.getByCustomerAndStatus(customerId, status.name());
        if(CollectionUtil.isEmpty(rexyList)){
            Response<CustomerDTO> response = remoteCustomerService.getById(customerId);
            RexyConfigDTO rexyConfig = rexyConfigService.getDefaultRexyByLevel(response.getData().getKycLevel());
            CustomerRexy customerRexy = rexyMapperStruct.toCustomerRexy(response.getData(), rexyConfig);
            customerRexy.setStatus(RexyConstant.CustomerRexyStatus.ACTIVE.name());
            customerRexyBuilder.insert(customerRexy);
        }
        return rexyList;
    }

    @Override
    public List<CustomerRexy> getByCustomer(String customerId) {
        return customerRexyBuilder.getByCustomer(customerId);
    }

    /**
     * 切换用户拥有的恐龙
     * @param customerDTO
     * @param userKycLevel
     * @return
     */
    @Override
    public Boolean updateCustomerRexyByLevel(CustomerDTO customerDTO, String userKycLevel) {
        log.info("updateCustomerRexyByLevel customerId: {}, kycLevel: {}", customerDTO, userKycLevel);
        //准备切换的恐龙
        RexyConfigDTO defaultRexyConfig = rexyConfigService.getDefaultRexyByLevel(userKycLevel);
        if (defaultRexyConfig == null) {
            log.error("updateCustomerRexyByLevel rexyConfig is null");
            return false;
        }
        //用户目前有效的恐龙
        RexyConfigDTO hasDefaultRexyConfig = rexyConfigService.getDefaultRexyByLevel(customerDTO.getKycLevel());
        CustomerRexy hasRexy = customerRexyBuilder.getByCustomerAndRexyId(customerDTO.getCustomerId(), hasDefaultRexyConfig.getId());

        customerRexyBuilder.delete(hasRexy);

        //插入新的恐龙
        Response<CustomerDTO> response = remoteCustomerService.getById(customerDTO.getCustomerId());
        CustomerRexy customerRexy = rexyMapperStruct.toCustomerRexy(response.getData(), defaultRexyConfig);
        customerRexy.setStatus(RexyConstant.CustomerRexyStatus.ACTIVE.name());
        customerRexyBuilder.insert(customerRexy);

        // 更新用户恐龙篮子的限制
        updateBasket(customerDTO.getCustomerId(), RexyConstant.RexyBasketsTypeEnum.normal.name(), customerRexy.getRexyBasketLimit());
        return true;
    }

    /**
     * 更新特定类型的篮子
     *
     * @param customerId  用户ID
     * @param basketType  篮子类型
     * @param basketLimit 篮子限制
     */
    private void updateBasket(String customerId, String basketType, Integer basketLimit) {
        CustomerRexyBaskets basket = customerRexyBasketsBuilder.getByCustomerId(customerId, basketType);
        if (basket != null) {
            BigDecimal newLimit = BigDecimal.valueOf(basketLimit);
            basket.setBasketLimit(newLimit);

            // 如果received超过了basketLimit，则更新为basketLimit
            if (basket.getReceived().compareTo(newLimit) > 0) {
                basket.setReceived(newLimit);
            }
            customerRexyBasketsBuilder.update(basket);
        }
    }
}
