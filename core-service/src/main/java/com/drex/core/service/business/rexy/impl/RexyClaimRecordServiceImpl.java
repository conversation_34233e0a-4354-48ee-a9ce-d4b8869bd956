package com.drex.core.service.business.rexy.impl;

import com.drex.asset.api.model.constant.AssetBusinessType;
import com.drex.asset.api.model.constant.AssetType;
import com.drex.asset.api.model.request.AssetTransferInRequest;
import com.drex.asset.api.model.response.AssetOperateResponse;
import com.drex.core.api.common.BusinessMonitorConstant;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.OperateMaizeKernelRequest;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.core.api.response.MaizeKernelDTO;
import com.drex.core.dal.tablestore.builder.RexyClaimRecordBuilder;
import com.drex.core.dal.tablestore.model.RexyClaimRecord;
import com.drex.core.model.BasketPoint;
import com.drex.core.model.RexyBusinessCode;
import com.drex.core.service.business.rexy.RexyClaimRecordService;
import com.drex.endpoint.api.response.TransactionInfo;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;

@Slf4j
@Service
public class RexyClaimRecordServiceImpl implements RexyClaimRecordService {

    @Resource
    private RexyClaimRecordBuilder rexyClaimRecordBuilder;

    /**
     * 领取记录
     *
     * @param customerId
     * @param basketType
     * @param customerRexyBaskets
     * @return
     */
    @Override
    public RexyClaimRecord tryClaim(String customerId, String eoaAddress, String basketType, CustomerRexyBasketsDTO customerRexyBaskets) {
        RexyClaimRecord rexyClaimRecord = new RexyClaimRecord();
        rexyClaimRecord.setCustomerId(customerId);
        rexyClaimRecord.setBasketType(basketType);
        rexyClaimRecord.setAddress(eoaAddress);
        rexyClaimRecord.setPoint(customerRexyBaskets.getReceived());
        rexyClaimRecord.setStatus(RexyConstant.CommonStatus.PROCESSING.getCode());
        rexyClaimRecordBuilder.insert(rexyClaimRecord);
        return rexyClaimRecord;
    }

    @Override
    public RexyClaimRecord getByRecordId(String recordId) {
        return rexyClaimRecordBuilder.getByRecordId(recordId);
    }

    @Override
    public Boolean confirmClaim(RexyClaimRecord record) {
        record.setStatus(RexyConstant.CommonStatus.SUCCESS.getCode());
        return rexyClaimRecordBuilder.update(record);
    }

    @Override
    public Boolean updateHashCode(RexyClaimRecord record) {
        return rexyClaimRecordBuilder.updateHashCode(record);
    }

    public BigDecimal getByCustomerId(String customerId, RexyConstant.CommonStatus commonStatus){
        return rexyClaimRecordBuilder.getByCustomerIdAndStatus(customerId, commonStatus.getCode());
    }

    @Override
    public List<RexyClaimRecord> getByStatus(String status) {
        return rexyClaimRecordBuilder.getByStatus(status);
    }
}
