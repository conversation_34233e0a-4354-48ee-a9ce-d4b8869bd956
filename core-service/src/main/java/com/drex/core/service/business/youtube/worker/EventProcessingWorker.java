package com.drex.core.service.business.youtube.worker;

import com.drex.core.service.business.youtube.PlaybackAnalysisService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 事件处理Worker
 * 负责计算有效观看时长和进度
 */
@Slf4j
@Component
public class EventProcessingWorker implements IWorker<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult>,
        ICallback<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult> {

    @Autowired
    private PlaybackAnalysisService playbackAnalysisService;

    @Override
    public EventProcessingResult action(EventProcessingParam param, Map<String, WorkerWrapper> allWrappers) {
        try {
            log.debug("Processing events for session: {}, events count: {}", param.getSessionId(), param.getEvents().size());
            
            // 计算有效观看时长
            int effectiveWatchSeconds = playbackAnalysisService.calculateEffectiveWatchTime(param.getEvents());
            
            // 计算进度
            Integer currentProgress = calculateProgress(effectiveWatchSeconds, param.getVideoDurationSeconds());
            
            log.debug("Event processing completed for session: {}, effectiveWatchSeconds: {}, progress: {}", 
                    param.getSessionId(), effectiveWatchSeconds, currentProgress);
            
            return new EventProcessingResult(effectiveWatchSeconds, currentProgress);
            
        } catch (Exception e) {
            log.error("Failed to process events for session: {}", param.getSessionId(), e);
            throw new RuntimeException("Event processing failed", e);
        }
    }

    @Override
    public void begin() {
        log.debug("EventProcessingWorker begin");
    }

    @Override
    public void result(boolean success, EventProcessingParam param, WorkResult<EventProcessingResult> result) {
        if (success) {
            log.debug("EventProcessingWorker completed successfully for session: {}", param.getSessionId());
        } else {
            log.error("EventProcessingWorker failed for session: {}", param.getSessionId());
        }
    }

    /**
     * 计算进度
     */
    private Integer calculateProgress(int effectiveWatchSeconds, int videoDurationSeconds) {
        if (videoDurationSeconds <= 0) {
            return null;
        }

        double watchPercentage = (double) effectiveWatchSeconds / videoDurationSeconds;
        
        // 根据有效播放时长占视频总时长的百分比计算progress进度
        if (watchPercentage >= 0.2 && watchPercentage <= 0.4) {
            return 1;
        } else if (watchPercentage >= 0.5 && watchPercentage <= 0.7) {
            return 2;
        } else if (watchPercentage >= 0.8 && watchPercentage <= 1.2) {
            return 3;
        }
        
        return null;
    }

    /**
     * 事件处理参数
     */
    public static class EventProcessingParam {
        private final String sessionId;
        private final List<SessionEvent> events;
        private final int videoDurationSeconds;

        public EventProcessingParam(String sessionId, List<SessionEvent> events, int videoDurationSeconds) {
            this.sessionId = sessionId;
            this.events = events;
            this.videoDurationSeconds = videoDurationSeconds;
        }

        public String getSessionId() { return sessionId; }
        public List<SessionEvent> getEvents() { return events; }
        public int getVideoDurationSeconds() { return videoDurationSeconds; }
    }

    /**
     * 事件处理结果
     */
    public static class EventProcessingResult {
        private final int effectiveWatchSeconds;
        private final Integer currentProgress;

        public EventProcessingResult(int effectiveWatchSeconds, Integer currentProgress) {
            this.effectiveWatchSeconds = effectiveWatchSeconds;
            this.currentProgress = currentProgress;
        }

        public int getEffectiveWatchSeconds() { return effectiveWatchSeconds; }
        public Integer getCurrentProgress() { return currentProgress; }
    }
}
