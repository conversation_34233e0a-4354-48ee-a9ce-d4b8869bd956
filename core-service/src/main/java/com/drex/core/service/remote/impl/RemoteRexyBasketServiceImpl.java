package com.drex.core.service.remote.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.asset.api.model.constant.AssetBusinessType;
import com.drex.asset.api.model.constant.AssetType;
import com.drex.asset.api.model.request.AssetTransferInRequest;
import com.drex.asset.api.model.response.AssetOperateResponse;
import com.drex.asset.api.service.RemoteAssetService;
import com.drex.core.api.RemoteRexyBasketService;
import com.drex.core.api.common.BusinessMonitorConstant;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.ClaimMaizeRequest;
import com.drex.core.api.request.OperateMaizeKernelRequest;
import com.drex.core.api.request.RexyBasketsRequest;
import com.drex.core.api.response.CustomerRexyBasketsDTO;
import com.drex.core.api.response.MaizeKernelDTO;
import com.drex.core.api.response.WalletOperationDTO;
import com.drex.core.dal.tablestore.model.RexyClaimRecord;
import com.drex.core.model.BasketPoint;
import com.drex.core.model.RexyBusinessCode;
import com.drex.core.service.business.rexy.RexyBasketService;
import com.drex.core.service.business.rexy.RexyClaimRecordService;
import com.drex.core.service.chain.PointGrant;
import com.drex.endpoint.api.RemoteTransactionsService;
import com.drex.endpoint.api.response.TransactionInfo;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import com.kikitrade.framework.redis.lock.RedisDistributedLock;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Duration;
import java.util.Objects;


@Slf4j
@DubboService
public class RemoteRexyBasketServiceImpl implements RemoteRexyBasketService {

    @Resource
    private RexyBasketService rexyBasketService;
    @Resource
    private RexyClaimRecordService rexyClaimRecordService;
    @Resource
    private PointGrant pointGrant;
    @Resource
    private KiKiMonitor kiKiMonitor;
    @DubboReference
    private RemoteAssetService remoteAssetService;

    @Override
    public Response<CustomerRexyBasketsDTO> getCustomerRexyBaskets(RexyBasketsRequest request) {
        log.info("getCustomerRexyBaskets request: {}", request);
        try {
            CustomerRexyBasketsDTO rexyBaskets = rexyBasketService.getCustomerRexyBaskets(request.getCustomerId(), request.getBasketType());
            return Response.success(rexyBaskets);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<MaizeKernelDTO> collectMaizeKernel(OperateMaizeKernelRequest request) {
        log.info("collectMaizeKernel request: {}", request);
        try {
            if (request == null || request.getCustomerId() == null || request.getBasketType() == null
                    || request.getOperateType() != RexyConstant.OperateTypeEnum.collect){
                throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
            }
            log.info("generateMaize_request:{}", request);
            kiKiMonitor.monitor(BusinessMonitorConstant.COLLECT_MAIZE_KERNEL,
                    new String[]{"code", "request"});

            MaizeKernelDTO maizeKernelDTO = rexyBasketService.collectMaizeKernel(request);
            log.info("generateMaize_success:{}", maizeKernelDTO);
            return Response.success(maizeKernelDTO);
        } catch (CoreException e) {
            kiKiMonitor.monitor(BusinessMonitorConstant.COLLECT_MAIZE_KERNEL,
                    new String[]{"code", e.getCode().name().toLowerCase()});
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<MaizeKernelDTO> claimMaizeKernel(ClaimMaizeRequest request) {
        log.info("claimMaizeKernel request: {}", request);
        kiKiMonitor.monitor(BusinessMonitorConstant.CLAIM_MAIZE_KERNEL, new String[]{"code", "request"});
        log.info("{}::{}::{}", RexyConstant.STATISTICS_ORIGINAL_DATA, RexyConstant.RiskMonitorType.CLAIM_POINT, JSON.toJSONString(request));

        BasketPoint.ClaimPointsModel claimPointsModel = pointGrant.decodeClaimOperation(request.getCallData());
        RexyClaimRecord record = rexyClaimRecordService.getByRecordId(claimPointsModel.getBusinessId());
        if(record == null){
            kiKiMonitor.monitor(BusinessMonitorConstant.CLAIM_MAIZE_KERNEL, new String[]{"code", "record_not_found"});
            return Response.error(CoreResponseCode.DATA_NOT_FOUND.getCode(), "record not found");
        }
        if(record.getStatus().equals(RexyConstant.CommonStatus.SUCCESS.getCode())){
            return Response.success(MaizeKernelDTO.builder().received(record.getPoint()).build());
        }
        record.setTransferHashCode(request.getTransactionHash());
        record.setPoint(BigDecimal.valueOf(claimPointsModel.getPoints().longValue()));
        record.setStatus(RexyConstant.CommonStatus.TRYING.name());
        record.setCreate(System.currentTimeMillis());
        rexyClaimRecordService.updateHashCode(record);
        return rexyBasketService.claimMaizeKernel(record);
    }

    /**
     * 构建领取请求
     *
     * @param customerId
     * @param basketCode
     * @return
     */
    @Override
    public Response<WalletOperationDTO> buildClaimRequests(String customerId, String eoaAddress, String basketCode) {
        log.info("buildClaimRequests request: customerId={}, eoaAddress={} ,basketCode={}", customerId, eoaAddress, basketCode);
        //查询用户篮子里面还有多少积分待领取
        try {
            CustomerRexyBasketsDTO customerRexyBaskets = rexyBasketService.getCustomerRexyBaskets(customerId, RexyConstant.RexyBasketsTypeEnum.valueOf(basketCode));
            if(customerRexyBaskets == null || customerRexyBaskets.getReceived() == null || customerRexyBaskets.getReceived().compareTo(BigDecimal.ZERO) <= 0){
                return Response.error(CoreResponseCode.REXY_BASKET_EMPTY.getCode(), CoreResponseCode.REXY_BASKET_EMPTY.getKey());
            }
            //生成本次的领取请求
            RexyClaimRecord rexyClaimRecord = rexyClaimRecordService.tryClaim(customerId, eoaAddress, basketCode, customerRexyBaskets);
            WalletOperationDTO walletOperationDTO = pointGrant.buildClaimOperation(rexyClaimRecord.getAddress(), rexyClaimRecord.getRecordId(), new BigInteger(rexyClaimRecord.getPoint().toString()));
            return Response.success(walletOperationDTO);
        } catch (CoreException e) {
            log.error("buildClaimRequests error", e);
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }
}
