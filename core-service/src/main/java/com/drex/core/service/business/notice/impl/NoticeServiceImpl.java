package com.drex.core.service.business.notice.impl;

import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.request.NoticeDTO;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.NoticeBuilder;
import com.drex.core.dal.tablestore.model.Notice;
import com.drex.core.service.business.notice.NoticeService;
import com.drex.core.service.mapperstruct.NoticeMapperStruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class NoticeServiceImpl implements NoticeService {

    @Resource
    private NoticeBuilder noticeBuilder;

    @Resource
    private NoticeMapperStruct noticeMapperStruct;

    @Resource
    private IdUtils idUtils;

    @Override
    public NoticeDTO getById(String id) throws CoreException {
        Notice notice = noticeBuilder.getById(id);
        if (notice == null) {
            throw new CoreException(CoreResponseCode.DATA_NOT_FOUND);
        }
        return noticeMapperStruct.toNoticeDTO(notice);
    }

    @Override
    public Boolean saveNotice(NoticeDTO noticeDTO) throws CoreException {
        if (noticeDTO == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }
        if (noticeDTO.getId() == null) {
            noticeDTO.setId(idUtils.nextId());
        }
        Notice notice = noticeMapperStruct.toNotice(noticeDTO);
        Boolean result = noticeBuilder.insert(notice);
        if (Boolean.FALSE.equals(result)) {
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        return true;
    }

    @Override
    public Boolean updateNotice(NoticeDTO noticeDTO) throws CoreException {
        if (noticeDTO == null || noticeDTO.getId() == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }
        
        // 先检查是否存在
        Notice existingNotice = noticeBuilder.getById(noticeDTO.getId());
        if (existingNotice == null) {
            throw new CoreException(CoreResponseCode.DATA_NOT_FOUND);
        }
        
        Notice notice = noticeMapperStruct.toNotice(noticeDTO);
        Boolean result = noticeBuilder.update(notice);
        if (Boolean.FALSE.equals(result)) {
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        return true;
    }

    @Override
    public Boolean deleteNotice(String id) throws CoreException {
        if (id == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }
        
        // 先检查是否存在
        Notice existingNotice = noticeBuilder.getById(id);
        if (existingNotice == null) {
            throw new CoreException(CoreResponseCode.DATA_NOT_FOUND);
        }
        
        Boolean result = noticeBuilder.delete(id);
        if (Boolean.FALSE.equals(result)) {
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        return true;
    }

    @Override
    public List<NoticeDTO> getByStatus(String status, Long beginTime) throws CoreException {
        if (status == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }
        
        List<Notice> noticeList = noticeBuilder.getByStatus(status, beginTime);
        return noticeMapperStruct.toNoticeDTOList(noticeList);
    }

}
