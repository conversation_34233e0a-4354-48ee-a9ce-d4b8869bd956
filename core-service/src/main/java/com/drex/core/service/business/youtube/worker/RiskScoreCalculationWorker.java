package com.drex.core.service.business.youtube.worker;

import com.drex.core.service.business.youtube.TrustScoreCalculationService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.ICallback;
import com.drex.core.service.util.async.worker.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 风险分数计算Worker
 * 负责计算欺诈风险分数（分数越高风险越高）
 */
@Slf4j
@Component
public class RiskScoreCalculationWorker implements IWorker<RiskScoreCalculationWorker.RiskScoreParam, Double>,
        ICallback<RiskScoreCalculationWorker.RiskScoreParam, Double> {

    @Autowired
    private TrustScoreCalculationService trustScoreCalculationService;

    @Override
    public Double action(RiskScoreParam param, Map<String, WorkerWrapper> allWrappers) {
        try {
            log.debug("Calculating risk score for session: {}, events count: {}", param.getSessionId(), param.getEvents().size());
            
            // 计算信任分数（注意：这里实际计算的是信任分数，我们需要转换为风险分数）
            double trustScore = trustScoreCalculationService.calculateTrustScore(param.getEvents(), param.getVideoDurationSeconds());
            
            // 将信任分数转换为风险分数：风险分数 = 1 - 信任分数
            // 信任分数越高，风险分数越低；信任分数越低，风险分数越高
            double riskScore = 1.0 - trustScore;
            
            // 确保风险分数在0-1范围内
            riskScore = Math.max(0.0, Math.min(1.0, riskScore));
            
            log.debug("Risk score calculation completed for session: {}, trustScore: {}, riskScore: {}", 
                    param.getSessionId(), trustScore, riskScore);
            
            return riskScore;
            
        } catch (Exception e) {
            log.error("Failed to calculate risk score for session: {}", param.getSessionId(), e);
            // 返回中等风险分数作为默认值
            return 0.5;
        }
    }

    @Override
    public void begin() {
        log.debug("RiskScoreCalculationWorker begin");
    }

    @Override
    public void result(boolean success, RiskScoreParam param, WorkResult<Double> result) {
        if (success) {
            log.debug("RiskScoreCalculationWorker completed successfully for session: {}, riskScore: {}", 
                    param.getSessionId(), result);
        } else {
            log.error("RiskScoreCalculationWorker failed for session: {}", param.getSessionId());
        }
    }

    /**
     * 风险分数计算参数
     */
    public static class RiskScoreParam {
        private final String sessionId;
        private final List<SessionEvent> events;
        private final int videoDurationSeconds;

        public RiskScoreParam(String sessionId, List<SessionEvent> events, int videoDurationSeconds) {
            this.sessionId = sessionId;
            this.events = events;
            this.videoDurationSeconds = videoDurationSeconds;
        }

        public String getSessionId() { return sessionId; }
        public List<SessionEvent> getEvents() { return events; }
        public int getVideoDurationSeconds() { return videoDurationSeconds; }
    }
}
