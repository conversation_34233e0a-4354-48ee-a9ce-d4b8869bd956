package com.drex.core.service.business.youtube.impl;

import com.drex.core.dal.tablestore.builder.VideoViewingSessionBuilder;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.service.business.youtube.*;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 异步事件处理服务实现
 */
@Slf4j
@Service
public class AsyncEventProcessingServiceImpl implements AsyncEventProcessingService {

    @Autowired
    private DataValidationService dataValidationService;

    @Autowired
    private PlaybackAnalysisService playbackAnalysisService;

    @Autowired
    private FocusAndActivityService focusAndActivityService;

    @Autowired
    private EnvironmentAnalysisService environmentAnalysisService;

    @Autowired
    private RiskScoreCalculationService riskScoreCalculationService;

    @Autowired
    private IncrementalCalculationCacheService incrementalCacheService;

    @Autowired
    private VideoViewingSessionBuilder videoViewingSessionBuilder;

    // 处理状态缓存
    private final Map<String, Double> processingProgress = new ConcurrentHashMap<>();
    private final Map<String, Boolean> processingComplete = new ConcurrentHashMap<>();

    @Override
    @Async("asyncEventProcessingExecutor")
    public void processEventsAsync(String sessionId, List<SessionEvent> events, int videoDurationSeconds) {
        try {
            log.info("Starting async processing for session: {}, events: {}", sessionId, events.size());
            
            processingProgress.put(sessionId, 0.0);
            processingComplete.put(sessionId, false);

            // 1. 增量计算并缓存指标 (30%)
            calculateAndCacheFraudIndicators(sessionId, events, videoDurationSeconds);
            processingProgress.put(sessionId, 0.3);

            // 2. 计算风控分数 (70%)
            IncrementalCalculationCacheService.AllMetrics allMetrics = 
                    incrementalCacheService.getAllMetrics(sessionId);
            
            if (allMetrics != null) {
                Map<String, Double> fraudIndicators = calculateFraudIndicatorsFromCache(
                        allMetrics, videoDurationSeconds);
                
                Map<String, Double> weights = riskScoreCalculationService.getDefaultWeights();
                RiskScoreCalculationService.RiskScoreResult riskScoreResult = 
                        riskScoreCalculationService.calculateRiskScore(fraudIndicators, weights);
                
                processingProgress.put(sessionId, 0.7);

                // 3. 更新会话状态 (100%)
                updateSessionStatusAsync(sessionId, riskScoreResult.getFinalRiskScore(), fraudIndicators);
                processingProgress.put(sessionId, 1.0);
            }

            processingComplete.put(sessionId, true);
            log.info("Completed async processing for session: {}", sessionId);

        } catch (Exception e) {
            log.error("Failed to process events async for session: {}", sessionId, e);
            processingComplete.put(sessionId, true);
            processingProgress.put(sessionId, 1.0);
        }
    }

    @Override
    public void calculateAndCacheFraudIndicators(String sessionId, List<SessionEvent> events, int videoDurationSeconds) {
        try {
            // 计算播放相关指标
            calculateAndCachePlaybackMetrics(sessionId, events, videoDurationSeconds);
            
            // 计算焦点相关指标
            calculateAndCacheFocusMetrics(sessionId, events);
            
            // 计算事件相关指标
            calculateAndCacheEventMetrics(sessionId, events);

            // 设置缓存过期时间（24小时）
            incrementalCacheService.setExpire(sessionId, 24 * 60 * 60);

        } catch (Exception e) {
            log.error("Failed to calculate and cache fraud indicators for session: {}", sessionId, e);
        }
    }

    @Override
    @Async("asyncEventProcessingExecutor")
    public void updateSessionStatusAsync(String sessionId, double riskScore, Map<String, Double> fraudIndicators) {
        try {
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            if (session != null) {
                session.setFinalRiskScore(riskScore);
                session.setModified(System.currentTimeMillis());
                videoViewingSessionBuilder.update(session);
                
                log.debug("Updated session {} with risk score: {}", sessionId, riskScore);
            }
        } catch (Exception e) {
            log.error("Failed to update session status async for session: {}", sessionId, e);
        }
    }

    @Override
    public boolean isProcessingComplete(String sessionId) {
        return processingComplete.getOrDefault(sessionId, false);
    }

    @Override
    public double getProcessingProgress(String sessionId) {
        return processingProgress.getOrDefault(sessionId, 0.0);
    }

    /**
     * 计算并缓存播放相关指标
     */
    private void calculateAndCachePlaybackMetrics(String sessionId, List<SessionEvent> events, int videoDurationSeconds) {
        try {
            // 计算播放时长
            int totalPlayTime = calculateTotalPlayTime(events);
            int effectivePlayTime = playbackAnalysisService.calculateEffectiveWatchTime(events);
            
            // 计算跳转次数
            PlaybackAnalysisService.SeekingBehaviorAnalysisResult seekResult = 
                    playbackAnalysisService.analyzeSeekingBehavior(events);
            
            // 计算播放速率
            PlaybackAnalysisService.PlaybackRateAnalysisResult rateResult = 
                    playbackAnalysisService.analyzePlaybackRate(events);
            
            // 更新缓存
            incrementalCacheService.updatePlaybackMetrics(
                    sessionId, 
                    totalPlayTime, 
                    effectivePlayTime,
                    seekResult.getTotalSeekCount(),
                    rateResult.getAveragePlaybackRate() * rateResult.getRateChangeCount(),
                    rateResult.getRateChangeCount()
            );
            
        } catch (Exception e) {
            log.error("Failed to calculate playback metrics for session: {}", sessionId, e);
        }
    }

    /**
     * 计算并缓存焦点相关指标
     */
    private void calculateAndCacheFocusMetrics(String sessionId, List<SessionEvent> events) {
        try {
            long sessionDuration = getSessionDuration(events);
            
            FocusAndActivityService.FocusAnalysisResult focusResult = 
                    focusAndActivityService.analyzeFocusTime(events, sessionDuration);
            
            FocusAndActivityService.IdleTimeAnalysisResult idleResult = 
                    focusAndActivityService.analyzeIdleTime(events);
            
            FocusAndActivityService.InteractionBehaviorAnalysisResult interactionResult = 
                    focusAndActivityService.analyzeInteractionBehavior(events);
            
            // 更新缓存
            incrementalCacheService.updateFocusMetrics(
                    sessionId,
                    focusResult.getTotalFocusTime(),
                    focusResult.getTotalBlurTime(),
                    idleResult.getTotalIdleTime(),
                    interactionResult.getTotalInteractions()
            );
            
        } catch (Exception e) {
            log.error("Failed to calculate focus metrics for session: {}", sessionId, e);
        }
    }

    /**
     * 计算并缓存事件相关指标
     */
    private void calculateAndCacheEventMetrics(String sessionId, List<SessionEvent> events) {
        try {
            // 统计事件类型
            Map<String, Integer> eventCounts = new HashMap<>();
            for (SessionEvent event : events) {
                eventCounts.put(event.getEventType(), 
                        eventCounts.getOrDefault(event.getEventType(), 0) + 1);
            }
            
            // 计算重复事件
            DataValidationService.DuplicateEventDetectionResult duplicateResult = 
                    dataValidationService.detectDuplicateEvents(events);
            
            // 计算时间戳异常
            DataValidationService.TimestampValidationResult timestampResult = 
                    dataValidationService.validateTimestamps(events, System.currentTimeMillis());
            
            // 计算序列错误
            DataValidationService.EventSequenceValidationResult sequenceResult = 
                    dataValidationService.validateEventSequence(events);
            
            // 更新缓存
            incrementalCacheService.updateEventMetrics(
                    sessionId,
                    eventCounts,
                    duplicateResult.getDuplicateCount(),
                    timestampResult.getAnomalousEventCount(),
                    sequenceResult.getSequenceErrors()
            );
            
        } catch (Exception e) {
            log.error("Failed to calculate event metrics for session: {}", sessionId, e);
        }
    }

    /**
     * 从缓存中计算欺诈指标
     */
    private Map<String, Double> calculateFraudIndicatorsFromCache(
            IncrementalCalculationCacheService.AllMetrics allMetrics, int videoDurationSeconds) {
        
        Map<String, Double> indicators = new HashMap<>();
        
        try {
            IncrementalCalculationCacheService.PlaybackMetrics playback = allMetrics.getPlaybackMetrics();
            IncrementalCalculationCacheService.FocusMetrics focus = allMetrics.getFocusMetrics();
            IncrementalCalculationCacheService.EventMetrics events = allMetrics.getEventMetrics();
            
            // 基于缓存数据计算各项指标
            // 这里使用FraudIndicatorThresholds中的计算公式
            
            // 播放相关指标
            if (playback != null) {
                double completionPercentage = videoDurationSeconds > 0 ? 
                        (double) playback.getTotalPlayTime() / videoDurationSeconds : 0.0;
                indicators.put("ABNORMAL_COMPLETION_PERCENTAGE", 
                        calculateCompletionAnomalyScore(completionPercentage, 
                                playback.getEffectivePlayTime(), playback.getTotalPlayTime()));
                
                indicators.put("EXCESSIVE_PLAYBACK_SPEED", 
                        calculatePlaybackRateScore(playback.getAveragePlaybackRate()));
                
                // 计算跳转频率（假设会话时长为总播放时长）
                double seekFrequency = playback.getTotalPlayTime() > 0 ? 
                        (double) playback.getTotalSeekCount() / (playback.getTotalPlayTime() / 60.0) : 0.0;
                indicators.put("ABNORMAL_SEEK", calculateSeekScore(seekFrequency));
            }
            
            // 焦点相关指标
            if (focus != null) {
                indicators.put("LOW_FOCUS_DURATION", calculateLowFocusScore(focus.getFocusPercentage()));
                indicators.put("LONG_IDLE_DURATION", calculateLongIdleScore(focus.getIdlePercentage()));
            }
            
            // 事件相关指标
            if (events != null) {
                double duplicateRate = events.getTotalEventCount() > 0 ? 
                        (double) events.getTotalDuplicateEvents() / events.getTotalEventCount() : 0.0;
                indicators.put("DUPLICATE_EVENT", duplicateRate);
                
                double timestampAnomalyRate = events.getTotalEventCount() > 0 ? 
                        (double) events.getTotalTimestampAnomalies() / events.getTotalEventCount() : 0.0;
                indicators.put("TIMESTAMP_ANOMALY", timestampAnomalyRate);
                
                indicators.put("EVENT_ORDER_ANOMALY", events.getTotalSequenceErrors() > 0 ? 1.0 : 0.0);
            }
            
        } catch (Exception e) {
            log.error("Failed to calculate fraud indicators from cache", e);
        }
        
        return indicators;
    }

    // 辅助方法
    private int calculateTotalPlayTime(List<SessionEvent> events) {
        return events.stream()
                .filter(event -> "TIMEUPDATE".equals(event.getEventType()))
                .filter(event -> event.getEventData() != null && event.getEventData().get("currentTime") != null)
                .mapToInt(event -> ((Number) event.getEventData().get("currentTime")).intValue())
                .max()
                .orElse(0);
    }

    private long getSessionDuration(List<SessionEvent> events) {
        if (events.isEmpty()) return 0;
        long startTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).min().orElse(0);
        long endTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).max().orElse(0);
        return endTime - startTime;
    }

    // 简化的指标计算方法（实际应该使用FraudIndicatorThresholds中的方法）
    private double calculateCompletionAnomalyScore(double completionPercentage, int effectiveTime, int totalTime) {
        if (effectiveTime < totalTime * 0.5) return 1.0;
        return completionPercentage > 0.9 ? Math.min((completionPercentage - 0.9) / 0.6, 1.0) : 0.0;
    }

    private double calculatePlaybackRateScore(double rate) {
        double deviation = Math.abs(rate - 1.0);
        return deviation > 0.2 ? Math.min((deviation - 0.2) / 0.3, 1.0) : 0.0;
    }

    private double calculateSeekScore(double frequency) {
        return frequency > 5 ? Math.min((frequency - 5) / 5, 1.0) : 0.0;
    }

    private double calculateLowFocusScore(double percentage) {
        return percentage < 0.7 ? Math.min((0.7 - percentage) / 0.4, 1.0) : 0.0;
    }

    private double calculateLongIdleScore(double percentage) {
        return percentage > 0.2 ? Math.min((percentage - 0.2) / 0.3, 1.0) : 0.0;
    }
}
