package com.drex.core.service.business.youtube.impl;

import com.drex.core.api.request.VideoSessionInitRequest;
import com.drex.core.api.response.VideoSessionInitResponse;
import com.drex.core.dal.tablestore.builder.VideoViewingSessionBuilder;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
import com.drex.core.service.CoreProperties;
import com.drex.core.service.business.youtube.*;
import com.drex.core.service.cache.SessionEventCacheService;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 会话处理服务实现
 */
@Slf4j
@Service
public class SessionProcessingServiceImpl implements SessionProcessingService {

    @Autowired
    private VideoViewingSessionBuilder videoViewingSessionBuilder;

    @Autowired
    private SessionEventCacheService sessionEventCacheService;

    @Autowired
    private DataValidationService dataValidationService;

    @Autowired
    private PlaybackAnalysisService playbackAnalysisService;

    @Autowired
    private FocusAndActivityService focusAndActivityService;

    @Autowired
    private EnvironmentAnalysisService environmentAnalysisService;

    @Autowired
    private RiskScoreCalculationService riskScoreCalculationService;

    @Autowired
    private AsyncEventProcessingService asyncEventProcessingService;

    @Autowired
    private IncrementalCalculationCacheService incrementalCacheService;

    @Autowired
    private SystemConfigurationService systemConfigurationService;

    @Autowired
    private CoreProperties coreProperties;

    @Override
    public VideoSessionInitResponse initializeSession(VideoSessionInitRequest request) {
        try {
            log.info("Initializing video session for user: {}, video: {}", 
                    request.getCustomerId(), request.getVideoId());

            // 1. 检查用户黑名单
            BlacklistCheckResult userBlacklistResult = checkUserBlacklist(request.getCustomerId());
            if (userBlacklistResult.isBlacklisted()) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("User is blacklisted: " + userBlacklistResult.getReason())
                        .build();
            }

            // 2. 检查IP黑名单
            BlacklistCheckResult ipBlacklistResult = checkIpBlacklist(request.getClientIp());
            if (ipBlacklistResult.isBlacklisted()) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("IP is blacklisted: " + ipBlacklistResult.getReason())
                        .build();
            }

            // 3. 检查是否为奖励视频
            if (!isRewardVideo(request.getVideoId())) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("Video is not eligible for rewards")
                        .build();
            }

            // 4. 检查是否已获得奖励
            if (hasUserReceivedReward(request.getCustomerId(), request.getVideoId())) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("User has already received rewards for this video")
                        .build();
            }

            // 5. 创建会话
            VideoViewingSession session = createNewSession(request);
            boolean sessionCreated = videoViewingSessionBuilder.insert(session);
            
            if (!sessionCreated) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("Failed to create session")
                        .build();
            }

            // 6. 生成会话密钥
            String sessionKey = generateSessionKey(session.getSessionId(), request.getCustomerId());

            // 7. 构建响应
            return buildSuccessResponse(session, sessionKey);

        } catch (Exception e) {
            log.error("Failed to initialize session for user: {}, video: {}", 
                     request.getCustomerId(), request.getVideoId(), e);
            return VideoSessionInitResponse.builder()
                    .success(false)
                    .failureReason("Internal error occurred")
                    .build();
        }
    }

    @Override
    public SessionUpdateResult updateSession(String sessionId, List<SessionEvent> events) {
        try {
            log.debug("Updating session: {} with {} events", sessionId, events.size());

            // 1. 验证会话存在
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            if (session == null) {
                return new SessionUpdateResult(false, "Session not found", 0);
            }

            // 2. 存储事件到缓存
            boolean eventsSaved = sessionEventCacheService.addEvents(sessionId, events);
            if (!eventsSaved) {
                return new SessionUpdateResult(false, "Failed to save events", 0);
            }

            // 3. 异步处理事件（不阻塞响应）
            asyncEventProcessingService.processEventsAsync(sessionId, events, session.getVideoDurationSeconds());

            // 4. 返回基本确认信息（不包含分析结果）
            SessionUpdateResult result = new SessionUpdateResult(true, null, events.size());
            result.setAnalysisResults(Map.of(
                    "processingStatus", "ASYNC_PROCESSING",
                    "estimatedCompletionTime", System.currentTimeMillis() + 5000 // 预估5秒完成
            ));

            return result;

        } catch (Exception e) {
            log.error("Failed to update session: {}", sessionId, e);
            return new SessionUpdateResult(false, "Internal error occurred", 0);
        }
    }

    @Override
    public SessionCompletionResult completeSession(String sessionId) {
        try {
            log.info("Completing session: {}", sessionId);

            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            if (session == null) {
                return new SessionCompletionResult(false, null);
            }

            // 获取所有事件进行最终分析
            List<SessionEvent> allEvents = sessionEventCacheService.getEvents(sessionId);
            ComprehensiveAnalysisResult finalAnalysis = coordinateAnalysis(sessionId, allEvents);

            // 计算最终观看时长
            int effectiveWatchSeconds = playbackAnalysisService.calculateEffectiveWatchTime(allEvents);

            // 更新会话状态
            session.setSessionStatus(YouTubeAntiCheatConstant.SessionStatus.COMPLETED_PENDING_VALIDATION.getCode());
            session.setFinalRiskScore(finalAnalysis.getOverallRiskScore());
            session.setCalculatedEngagedSeconds(effectiveWatchSeconds);
            session.setServerEndTime(System.currentTimeMillis());

            videoViewingSessionBuilder.update(session);

            SessionCompletionResult result = new SessionCompletionResult(true, session);
            result.setFinalRiskScore(finalAnalysis.getOverallRiskScore());
            result.setEffectiveWatchSeconds(effectiveWatchSeconds);
            result.setFraudIndicators(finalAnalysis.getAllFraudIndicators());

            return result;

        } catch (Exception e) {
            log.error("Failed to complete session: {}", sessionId, e);
            return new SessionCompletionResult(false, null);
        }
    }

    @Override
    public SessionStatus getSessionStatus(String sessionId) {
        try {
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            if (session == null) {
                return null;
            }

            SessionStatus status = new SessionStatus(sessionId, session.getSessionStatus(), session.getStartTime());
            status.setLastUpdateTime(session.getModified());
            status.setCurrentRiskScore(session.getFinalRiskScore() != null ? session.getFinalRiskScore() : 0.0);
            status.setActive(!YouTubeAntiCheatConstant.SessionStatus.COMPLETED_PENDING_VALIDATION.getCode()
                    .equals(session.getSessionStatus()));

            // 获取事件统计
            Long eventCount = sessionEventCacheService.getEventCount(sessionId);
            status.setEventCount(eventCount != null ? eventCount.intValue() : 0);

            return status;

        } catch (Exception e) {
            log.error("Failed to get session status: {}", sessionId, e);
            return null;
        }
    }

    @Override
    public boolean validateSession(String sessionId, String customerId) {
        try {
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            return session != null && customerId.equals(session.getCustomerId());
        } catch (Exception e) {
            log.error("Failed to validate session: {}", sessionId, e);
            return false;
        }
    }

    @Override
    public BlacklistCheckResult checkUserBlacklist(String customerId) {
        // TODO: 实现用户黑名单检查逻辑
        // 这里应该查询用户黑名单缓存或数据库
        return new BlacklistCheckResult(false, null);
    }

    @Override
    public BlacklistCheckResult checkIpBlacklist(String ipAddress) {
        // TODO: 实现IP黑名单检查逻辑
        // 这里应该查询IP黑名单缓存或数据库
        return new BlacklistCheckResult(false, null);
    }

    @Override
    public boolean isRewardVideo(String videoId) {
        // TODO: 实现奖励视频检查逻辑
        // 这里应该查询奖励视频配置
        return true; // 临时返回true
    }

    @Override
    public boolean hasUserReceivedReward(String customerId, String videoId) {
        try {
            return videoViewingSessionBuilder.hasRewardGranted(customerId, videoId);
        } catch (Exception e) {
            log.error("Failed to check user reward status: {}, {}", customerId, videoId, e);
            return false;
        }
    }

    @Override
    public String generateSessionKey(String sessionId, String customerId) {
        // TODO: 实现密钥生成逻辑
        // 这里应该使用加密算法生成安全的密钥
        return "temp_key_" + sessionId + "_" + System.currentTimeMillis();
    }

    @Override
    public int calculateNextReportInterval(int eventCount) {
        // 实现上报间隔计算公式：10+(n/99×10)
        int additionalInterval = (eventCount / YouTubeAntiCheatConstant.INTERVAL_CALCULATION_DIVISOR) 
                * YouTubeAntiCheatConstant.MAX_ADDITIONAL_INTERVAL;
        return YouTubeAntiCheatConstant.BASE_REPORT_INTERVAL + additionalInterval;
    }

    @Override
    public ComprehensiveAnalysisResult coordinateAnalysis(String sessionId, List<SessionEvent> events) {
        try {
            // 优先从缓存读取预计算的指标
            IncrementalCalculationCacheService.AllMetrics cachedMetrics =
                    incrementalCacheService.getAllMetrics(sessionId);

            Map<String, Double> allFraudIndicators = new HashMap<>();

            if (cachedMetrics != null) {
                // 从缓存中计算欺诈指标
                allFraudIndicators = calculateFraudIndicatorsFromCache(cachedMetrics);
                log.debug("Using cached metrics for session: {}", sessionId);
            } else {
                // 回退到实时计算（兼容性）
                log.warn("No cached metrics found for session: {}, falling back to real-time calculation", sessionId);

                // 数据验证指标
                Map<String, Double> dataValidationIndicators = dataValidationService.calculateFraudIndicators(
                        events, null);
                allFraudIndicators.putAll(dataValidationIndicators);

                // 播放分析指标
                Map<String, Double> playbackIndicators = playbackAnalysisService.calculatePlaybackFraudIndicators(
                        events, 300); // 假设视频时长300秒
                allFraudIndicators.putAll(playbackIndicators);

                // 焦点活动指标
                Map<String, Double> focusActivityIndicators = focusAndActivityService.calculateFocusActivityFraudIndicators(
                        events, System.currentTimeMillis() - events.get(0).getServerTimestamp());
                allFraudIndicators.putAll(focusActivityIndicators);

                // 环境分析指标
                VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
                if (session != null) {
                    Map<String, Double> environmentIndicators = environmentAnalysisService.calculateEnvironmentFraudIndicators(
                            session.getCustomerId(), null, session.getClientIp(), null);
                    allFraudIndicators.putAll(environmentIndicators);
                }
            }

            // 计算综合风控分数
            Map<String, Double> weights = riskScoreCalculationService.getDefaultWeights();
            RiskScoreCalculationService.RiskScoreResult riskScoreResult =
                    riskScoreCalculationService.calculateRiskScore(allFraudIndicators, weights);

            ComprehensiveAnalysisResult result = new ComprehensiveAnalysisResult(
                    allFraudIndicators, riskScoreResult.getFinalRiskScore());
            result.setRiskLevel(riskScoreResult.getRiskLevel().getCode());

            return result;

        } catch (Exception e) {
            log.error("Failed to coordinate analysis for session: {}", sessionId, e);
            // 返回默认高风险结果
            return new ComprehensiveAnalysisResult(new HashMap<>(), 1.0);
        }
    }

    /**
     * 创建新会话
     */
    private VideoViewingSession createNewSession(VideoSessionInitRequest request) {
        VideoViewingSession session = new VideoViewingSession();
        session.setCustomerId(request.getCustomerId());
        session.setVideoId(request.getVideoId());
        session.setVideoUrl(request.getVideoUrl());
        session.setVideoDurationSeconds(request.getVideoDurationSeconds());
        session.setStartTime(request.getClientTimestamp());
        session.setServerStartTime(System.currentTimeMillis());
        session.setSessionStatus(YouTubeAntiCheatConstant.SessionStatus.IN_PROGRESS.getCode());
        session.setRewardGranted(false);
        return session;
    }

    /**
     * 构建成功响应
     */
    private VideoSessionInitResponse buildSuccessResponse(VideoViewingSession session, String sessionKey) {
        return VideoSessionInitResponse.builder()
                .success(true)
                .sessionId(session.getSessionId())
                .reportKey(sessionKey)
                .sessionExpireTime(System.currentTimeMillis() + 
                        coreProperties.getSessionTimeoutMinutes() * 60 * 1000L)
                .rewardStages(buildRewardStages())
                .reportConfig(buildReportConfig())
                .build();
    }

    /**
     * 构建奖励阶段信息
     */
    private List<VideoSessionInitResponse.RewardStageInfo> buildRewardStages() {
        List<VideoSessionInitResponse.RewardStageInfo> stages = new ArrayList<>();
        
        for (YouTubeAntiCheatConstant.RewardStage stage : YouTubeAntiCheatConstant.RewardStage.values()) {
            VideoSessionInitResponse.RewardStageInfo stageInfo = VideoSessionInitResponse.RewardStageInfo.builder()
                    .stageName(stage.getDescription())
                    .stageCode(stage.getCode())
                    .requiredWatchPercentage(stage.getWatchPercentage())
                    .rewardAmount("100") // TODO: 从配置获取
                    .rewardType("CORN")
                    .claimed(false)
                    .build();
            stages.add(stageInfo);
        }
        
        return stages;
    }

    /**
     * 构建上报配置
     */
    private VideoSessionInitResponse.ReportConfig buildReportConfig() {
        return VideoSessionInitResponse.ReportConfig.builder()
                .baseReportInterval(YouTubeAntiCheatConstant.BASE_REPORT_INTERVAL)
                .maxReportInterval(YouTubeAntiCheatConstant.BASE_REPORT_INTERVAL + 
                        YouTubeAntiCheatConstant.MAX_ADDITIONAL_INTERVAL)
                .intervalCalculationParam(YouTubeAntiCheatConstant.INTERVAL_CALCULATION_DIVISOR)
                .maxEventsPerReport(YouTubeAntiCheatConstant.MAX_REPORT_EVENTS_PER_REQUEST)
                .encryptionEnabled(true)
                .signatureEnabled(true)
                .requiredEventTypes(Arrays.asList("PLAY", "PAUSE", "TIMEUPDATE", "FOCUS", "BLUR",
                        "PLAYING", "PAUSED", "FOCUS_LOST", "FOCUS_GAINED"))
                .optionalEventTypes(Arrays.asList("SEEKING", "SEEK", "VOLUME_CHANGE", "FULLSCREEN_CHANGE", "USER_STATE"))
                .build();
    }

    /**
     * 根据分析结果更新会话
     */
    private void updateSessionWithAnalysisResult(VideoViewingSession session,
                                               ComprehensiveAnalysisResult analysisResult) {
        session.setFinalRiskScore(analysisResult.getOverallRiskScore());
        session.setModified(System.currentTimeMillis());
    }

    /**
     * 从缓存中计算欺诈指标
     */
    private Map<String, Double> calculateFraudIndicatorsFromCache(
            IncrementalCalculationCacheService.AllMetrics allMetrics) {

        Map<String, Double> indicators = new HashMap<>();

        try {
            IncrementalCalculationCacheService.PlaybackMetrics playback = allMetrics.getPlaybackMetrics();
            IncrementalCalculationCacheService.FocusMetrics focus = allMetrics.getFocusMetrics();
            IncrementalCalculationCacheService.EventMetrics events = allMetrics.getEventMetrics();

            // 基于缓存数据计算各项指标
            if (playback != null) {
                indicators.put("EXCESSIVE_PLAYBACK_SPEED",
                        Math.abs(playback.getAveragePlaybackRate() - 1.0) > 0.2 ? 0.8 : 0.0);
                indicators.put("ABNORMAL_SEEK",
                        playback.getTotalSeekCount() > 10 ? 0.6 : 0.0);
            }

            if (focus != null) {
                indicators.put("LOW_FOCUS_DURATION",
                        focus.getFocusPercentage() < 0.7 ? 0.7 : 0.0);
                indicators.put("LONG_IDLE_DURATION",
                        focus.getIdlePercentage() > 0.2 ? 0.5 : 0.0);
            }

            if (events != null) {
                double duplicateRate = events.getTotalEventCount() > 0 ?
                        (double) events.getTotalDuplicateEvents() / events.getTotalEventCount() : 0.0;
                indicators.put("DUPLICATE_EVENT", duplicateRate);

                indicators.put("EVENT_ORDER_ANOMALY", events.getTotalSequenceErrors() > 0 ? 1.0 : 0.0);
            }

        } catch (Exception e) {
            log.error("Failed to calculate fraud indicators from cache", e);
        }

        return indicators;
    }
}
