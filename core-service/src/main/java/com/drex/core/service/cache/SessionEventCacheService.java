package com.drex.core.service.cache;

import com.drex.core.service.cache.model.SessionEvent;

import java.util.List;

/**
 * 会话事件缓存服务接口
 * 用于管理Redis中的会话事件数据
 */
public interface SessionEventCacheService {

    /**
     * 添加会话事件
     *
     * @param sessionId 会话ID
     * @param event 事件对象
     * @return 是否成功
     */
    Boolean addEvent(String sessionId, SessionEvent event);

    /**
     * 批量添加会话事件
     *
     * @param sessionId 会话ID
     * @param events 事件列表
     * @return 是否成功
     */
    Boolean addEvents(String sessionId, List<SessionEvent> events);

    /**
     * 获取会话的所有事件
     *
     * @param sessionId 会话ID
     * @return 事件列表
     */
    List<SessionEvent> getEvents(String sessionId);

    /**
     * 获取会话的指定类型事件
     *
     * @param sessionId 会话ID
     * @param eventType 事件类型
     * @return 事件列表
     */
    List<SessionEvent> getEventsByType(String sessionId, String eventType);

    /**
     * 获取会话事件数量
     *
     * @param sessionId 会话ID
     * @return 事件数量
     */
    Long getEventCount(String sessionId);

    /**
     * 删除会话的所有事件
     *
     * @param sessionId 会话ID
     * @return 是否成功
     */
    Boolean deleteEvents(String sessionId);

    /**
     * 设置会话事件过期时间
     *
     * @param sessionId 会话ID
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功
     */
    Boolean setExpire(String sessionId, long expireSeconds);

    /**
     * 检查会话事件是否存在
     *
     * @param sessionId 会话ID
     * @return 是否存在
     */
    Boolean exists(String sessionId);

    /**
     * 获取会话事件的剩余过期时间
     *
     * @param sessionId 会话ID
     * @return 剩余时间（秒），-1表示永不过期，-2表示不存在
     */
    Long getExpire(String sessionId);

    /**
     * 清理过期的会话事件
     *
     * @return 清理的数量
     */
    Long cleanExpiredEvents();
}
