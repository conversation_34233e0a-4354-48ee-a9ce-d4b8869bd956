package com.drex.core.service.chain.sign;

import lombok.SneakyThrows;
import org.web3j.protocol.Web3j;

import java.math.BigInteger;

public interface ISigner {

    /**
     * Sign message.
     *
     * @param message the message
     * @return signature
     */
    String signPrefixedMessage(byte[] message);

    /**
     * Sign message need to hash false.
     * @param message the message
     * @return signature
     */
    String signMessage(byte[] message);


    /**
     * 发送交易
     *
     * @param chainId  链ID
     * @param web3j    web3j
     * @param to       目标地址
     * @param calldata 调用数据
     * @param value    转账金额
     * @return 交易哈希
     */
    @SneakyThrows
    String sendTransaction(Integer chainId, Web3j web3j, String to, String calldata, BigInteger value);
}
