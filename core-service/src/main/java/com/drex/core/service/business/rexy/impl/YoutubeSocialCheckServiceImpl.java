package com.drex.core.service.business.rexy.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.request.SocialConstant;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.CoreProperties;
import com.drex.core.service.business.rexy.SocialCheckService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Slf4j
@Service("youtubeSocialCheckService")
public class YoutubeSocialCheckServiceImpl implements SocialCheckService {

    @Resource
    private CoreProperties coreProperties;
    @Resource
    private MaizeRecordBuilder maizeRecordBuilder;

    @Override
    public boolean check(GenerateMaizeRequest request) {
        //是否为指定的Youtube视频
        if(Arrays.stream(coreProperties.getYoutubeId().split(",")).noneMatch(id -> id.equals(request.getSocialEventBody().getSocialContentId()))){
            return Boolean.FALSE;
        }
        //该视频是否已经产生过金蛋
        MaizeRecord maizeRecords = maizeRecordBuilder.findMaizeRecords(request.getCustomerId(), request.getSocialEvent().name(), request.getSocialEventBody().getSocialContentId());
        if(maizeRecords != null){
            return Boolean.FALSE;
        }
        if(!request.getSocialEvent().equals(SocialConstant.EventEnum.watch)){
            return Boolean.FALSE;
        }
        Long videoDuration = request.getSocialEventBody().getVideoDuration();
        Long watchedDuration = request.getSocialEventBody().getWatchedDuration();
        if (videoDuration == null || watchedDuration == null) {
            return Boolean.FALSE;
        }
        if(watchedDuration.doubleValue() / videoDuration.doubleValue() < coreProperties.getYoutubeDuration()){
            return Boolean.FALSE;
        }

        log.info("{}::{}::{}", RexyConstant.STATISTICS_ORIGINAL_DATA, RexyConstant.RiskMonitorType.YOUTUBE_WATCH, JSON.toJSONString(request));
        return Boolean.TRUE;
    }

    @Override
    public String getSocialType() {
        return SocialConstant.PlatformEnum.YouTube.name();
    }
}
