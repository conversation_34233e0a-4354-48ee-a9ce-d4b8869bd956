package com.drex.core.service.business.youtube.impl;

import com.drex.core.api.request.VideoSessionInitRequest;
import com.drex.core.dal.tablestore.builder.IpReputationBuilder;
import com.drex.core.dal.tablestore.builder.UserFingerprintBuilder;
import com.drex.core.dal.tablestore.model.IpReputation;
import com.drex.core.dal.tablestore.model.UserFingerprint;
import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
import com.drex.core.model.youtube.FraudIndicatorThresholds;
import com.drex.core.service.business.youtube.EnvironmentAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.drex.core.model.youtube.FraudIndicatorThresholds.FINGERPRINT_MAX_ALLOWED;
import static com.drex.core.model.youtube.FraudIndicatorThresholds.FINGERPRINT_THRESHOLD;

/**
 * 环境分析服务实现
 * 基于提供的欺诈指标阈值设计实现
 */
@Slf4j
@Service
public class EnvironmentAnalysisServiceImpl implements EnvironmentAnalysisService {

    @Autowired
    private UserFingerprintBuilder userFingerprintBuilder;

    @Autowired
    private IpReputationBuilder ipReputationBuilder;

    // 注意：欺诈指标阈值常量已移至FraudIndicatorThresholds类中统一管理

    @Override
    public FingerprintAnalysisResult analyzeBrowserFingerprint(VideoSessionInitRequest.BrowserFingerprint fingerprint, 
                                                              String customerId) {
        try {
            if (fingerprint == null || fingerprint.getFingerprintHash() == null) {
                return new FingerprintAnalysisResult(true, false, 0, 0.0);
            }

            String fingerprintHash = fingerprint.getFingerprintHash();
            
            // 查找现有指纹
            UserFingerprint existingFingerprint = userFingerprintBuilder.getByFingerprintHash(fingerprintHash);
            boolean isNewFingerprint = existingFingerprint == null;
            
            int associatedUserCount = 0;
            boolean isSuspicious = false;
            List<String> riskFactors = new ArrayList<>();

            if (!isNewFingerprint) {
                // 计算关联的用户数量
                if (existingFingerprint.getAssociatedUserIds() != null) {
                    String[] userIds = existingFingerprint.getAssociatedUserIds().split(",");
                    associatedUserCount = userIds.length;
                    
                    // 检查是否超过阈值
                    if (associatedUserCount > FINGERPRINT_THRESHOLD) {
                        isSuspicious = true;
                        riskFactors.add("MULTIPLE_USERS_SAME_FINGERPRINT");
                    }
                }
                
                // 检查是否已标记为可疑
                if (Boolean.TRUE.equals(existingFingerprint.getIsSuspicious())) {
                    isSuspicious = true;
                    riskFactors.add("PREVIOUSLY_MARKED_SUSPICIOUS");
                }
            }

            // 分析指纹特征
            analyzeFingerprintFeatures(fingerprint, riskFactors);

            double fingerprintRiskScore = calculateFingerprintRiskScore(associatedUserCount, riskFactors.size());

            FingerprintAnalysisResult result = new FingerprintAnalysisResult(
                    isNewFingerprint, isSuspicious, associatedUserCount, fingerprintRiskScore);
            result.setRiskFactors(riskFactors);
            result.setExistingFingerprint(existingFingerprint);

            Map<String, Object> analysisDetails = new HashMap<>();
            analysisDetails.put("fingerprintHash", fingerprintHash);
            analysisDetails.put("riskFactorCount", riskFactors.size());
            analysisDetails.put("analysisTimestamp", System.currentTimeMillis());
            result.setAnalysisDetails(analysisDetails);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze browser fingerprint", e);
            return new FingerprintAnalysisResult(true, true, 0, 1.0);
        }
    }

    @Override
    public IpReputationAnalysisResult analyzeIpReputation(String ipAddress) {
        try {
            if (ipAddress == null || ipAddress.trim().isEmpty()) {
                return new IpReputationAnalysisResult(false, 100, "UNKNOWN", 0.0);
            }

            // 查找IP信誉信息
            IpReputation ipReputation = ipReputationBuilder.getByIpAddress(ipAddress);
            
            boolean isMalicious = false;
            boolean isProxy = false;
            boolean isVpn = false;
            boolean isTorExitNode = false;
            boolean isDataCenter = false;
            int reputationScore = 100; // 默认高信誉
            String countryCode = "UNKNOWN";
            List<String> riskIndicators = new ArrayList<>();

            if (ipReputation != null) {
                isProxy = Boolean.TRUE.equals(ipReputation.getIsProxy());
                isVpn = Boolean.TRUE.equals(ipReputation.getIsVpn());
                isTorExitNode = Boolean.TRUE.equals(ipReputation.getIsTorExitNode());
                isDataCenter = Boolean.TRUE.equals(ipReputation.getIsDataCenter());
                reputationScore = ipReputation.getReputationScore() != null ? 
                        ipReputation.getReputationScore() : 100;
                countryCode = ipReputation.getCountryCode() != null ? 
                        ipReputation.getCountryCode() : "UNKNOWN";

                // 检测恶意IP
                if (isProxy) {
                    isMalicious = true;
                    riskIndicators.add("PROXY_IP");
                }
                if (isVpn) {
                    isMalicious = true;
                    riskIndicators.add("VPN_IP");
                }
                if (isTorExitNode) {
                    isMalicious = true;
                    riskIndicators.add("TOR_EXIT_NODE");
                }
                if (isDataCenter) {
                    isMalicious = true;
                    riskIndicators.add("DATA_CENTER_IP");
                }
                if (reputationScore < 50) {
                    isMalicious = true;
                    riskIndicators.add("LOW_REPUTATION_SCORE");
                }
            } else {
                // 新IP，需要进行信誉检查
                log.info("New IP detected, reputation check needed: {}", ipAddress);
            }

            double ipRiskScore = calculateIpRiskScore(isMalicious, reputationScore, riskIndicators.size());

            IpReputationAnalysisResult result = new IpReputationAnalysisResult(
                    isMalicious, reputationScore, countryCode, ipRiskScore);
            result.setProxy(isProxy);
            result.setVpn(isVpn);
            result.setTorExitNode(isTorExitNode);
            result.setDataCenter(isDataCenter);
            result.setIpReputation(ipReputation);
            result.setRiskIndicators(riskIndicators);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze IP reputation for: {}", ipAddress, e);
            return new IpReputationAnalysisResult(true, 0, "UNKNOWN", 1.0);
        }
    }

    @Override
    public DeviceAnalysisResult analyzeDeviceInfo(VideoSessionInitRequest.DeviceInfo deviceInfo, String userAgent) {
        try {
            if (deviceInfo == null) {
                return new DeviceAnalysisResult(true, 1.0, "UNKNOWN", "UNKNOWN", "UNKNOWN");
            }

            String deviceType = deviceInfo.getDeviceType() != null ? deviceInfo.getDeviceType() : "UNKNOWN";
            String operatingSystem = deviceInfo.getOperatingSystem() != null ? deviceInfo.getOperatingSystem() : "UNKNOWN";
            String browserName = deviceInfo.getBrowserName() != null ? deviceInfo.getBrowserName() : "UNKNOWN";

            List<String> suspiciousFeatures = new ArrayList<>();
            boolean isSuspiciousDevice = false;
            boolean isVirtualMachine = false;
            boolean isHeadlessBrowser = false;

            // 分析用户代理
            if (userAgent != null) {
                UserAgentAnalysisResult userAgentResult = analyzeSuspiciousUserAgent(userAgent);
                if (userAgentResult.isSuspicious()) {
                    isSuspiciousDevice = true;
                    suspiciousFeatures.addAll(userAgentResult.getSuspiciousPatterns());
                }
                isHeadlessBrowser = userAgentResult.isHeadless();
            }

            // 检测虚拟机特征
            if (detectVirtualMachineFeatures(deviceInfo, userAgent)) {
                isVirtualMachine = true;
                isSuspiciousDevice = true;
                suspiciousFeatures.add("VIRTUAL_MACHINE_DETECTED");
            }

            // 检测设备一致性
            if (!isDeviceInfoConsistent(deviceInfo, userAgent)) {
                isSuspiciousDevice = true;
                suspiciousFeatures.add("INCONSISTENT_DEVICE_INFO");
            }

            double deviceRiskScore = calculateDeviceRiskScore(isSuspiciousDevice, isVirtualMachine, 
                    isHeadlessBrowser, suspiciousFeatures.size());

            DeviceAnalysisResult result = new DeviceAnalysisResult(
                    isSuspiciousDevice, deviceRiskScore, deviceType, operatingSystem, browserName);
            result.setVirtualMachine(isVirtualMachine);
            result.setHeadlessBrowser(isHeadlessBrowser);
            result.setSuspiciousFeatures(suspiciousFeatures);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze device info", e);
            return new DeviceAnalysisResult(true, 1.0, "UNKNOWN", "UNKNOWN", "UNKNOWN");
        }
    }

    @Override
    public EnvironmentConsistencyResult analyzeEnvironmentConsistency(String customerId, 
                                                                     String currentFingerprint, 
                                                                     String currentIp) {
        try {
            List<String> inconsistencyReasons = new ArrayList<>();
            int environmentChangeCount = 0;

            // 获取用户历史指纹
            List<UserFingerprint> userFingerprints = userFingerprintBuilder.getByCustomerId(customerId);
            
            // 检查指纹一致性
            boolean fingerprintConsistent = checkFingerprintConsistency(userFingerprints, 
                    currentFingerprint, inconsistencyReasons);

            // 检查IP变化频率
            boolean ipConsistent = checkIpConsistency(customerId, currentIp, inconsistencyReasons);

            // 计算环境变化次数
            environmentChangeCount = userFingerprints.size();

            boolean isConsistent = inconsistencyReasons.isEmpty();
            double consistencyScore = calculateConsistencyScore(inconsistencyReasons.size(), environmentChangeCount);

            EnvironmentConsistencyResult result = new EnvironmentConsistencyResult(isConsistent, consistencyScore);
            result.setInconsistencyReasons(inconsistencyReasons);
            result.setEnvironmentChangeCount(environmentChangeCount);

            Map<String, Object> historicalData = new HashMap<>();
            historicalData.put("fingerprintCount", userFingerprints.size());
            historicalData.put("lastFingerprint", userFingerprints.isEmpty() ? null : 
                    userFingerprints.get(0).getFingerprintHash());
            result.setHistoricalData(historicalData);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze environment consistency for user: {}", customerId, e);
            return new EnvironmentConsistencyResult(false, 0.0);
        }
    }

    @Override
    public GeoLocationAnalysisResult analyzeGeoLocation(String ipAddress, Map<String, Object> geoLocation) {
        try {
            IpReputationAnalysisResult ipResult = analyzeIpReputation(ipAddress);
            
            String countryCode = ipResult.getCountryCode();
            String city = "UNKNOWN";
            boolean isAnomalousLocation = false;
            boolean isVpnLocation = ipResult.isVpn();
            double distanceFromPreviousLocation = 0.0;
            List<String> locationRiskFactors = new ArrayList<>();

            // 从客户端地理位置获取信息
            if (geoLocation != null) {
                Object cityObj = geoLocation.get("city");
                if (cityObj != null) {
                    city = cityObj.toString();
                }
            }

            // 检测地理位置异常
            if (isVpnLocation) {
                isAnomalousLocation = true;
                locationRiskFactors.add("VPN_LOCATION");
            }

            if (ipResult.isMalicious()) {
                isAnomalousLocation = true;
                locationRiskFactors.add("MALICIOUS_IP_LOCATION");
            }

            double locationRiskScore = calculateLocationRiskScore(isAnomalousLocation, 
                    isVpnLocation, locationRiskFactors.size());

            GeoLocationAnalysisResult result = new GeoLocationAnalysisResult(
                    isAnomalousLocation, countryCode, city, locationRiskScore);
            result.setVpnLocation(isVpnLocation);
            result.setDistanceFromPreviousLocation(distanceFromPreviousLocation);
            result.setLocationRiskFactors(locationRiskFactors);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze geo location for IP: {}", ipAddress, e);
            return new GeoLocationAnalysisResult(true, "UNKNOWN", "UNKNOWN", 1.0);
        }
    }

    @Override
    public UserAgentAnalysisResult analyzeSuspiciousUserAgent(String userAgent) {
        try {
            if (userAgent == null || userAgent.trim().isEmpty()) {
                return new UserAgentAnalysisResult(true, 1.0, "UNKNOWN", "UNKNOWN");
            }

            List<String> suspiciousPatterns = new ArrayList<>();
            boolean isSuspicious = false;
            boolean isBot = false;
            boolean isHeadless = false;

            // 检测机器人特征
            if (detectBotPatterns(userAgent)) {
                isBot = true;
                isSuspicious = true;
                suspiciousPatterns.add("BOT_PATTERN");
            }

            // 检测无头浏览器
            if (detectHeadlessPatterns(userAgent)) {
                isHeadless = true;
                isSuspicious = true;
                suspiciousPatterns.add("HEADLESS_BROWSER");
            }

            // 检测异常用户代理
            if (detectAnomalousUserAgent(userAgent)) {
                isSuspicious = true;
                suspiciousPatterns.add("ANOMALOUS_USER_AGENT");
            }

            // 提取浏览器信息
            String browserName = extractBrowserName(userAgent);
            String browserVersion = extractBrowserVersion(userAgent);

            double userAgentRiskScore = calculateUserAgentRiskScore(isSuspicious, isBot, 
                    isHeadless, suspiciousPatterns.size());

            UserAgentAnalysisResult result = new UserAgentAnalysisResult(
                    isSuspicious, userAgentRiskScore, browserName, browserVersion);
            result.setBot(isBot);
            result.setHeadless(isHeadless);
            result.setSuspiciousPatterns(suspiciousPatterns);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze user agent: {}", userAgent, e);
            return new UserAgentAnalysisResult(true, 1.0, "UNKNOWN", "UNKNOWN");
        }
    }

    @Override
    public FingerprintUpdateResult updateUserFingerprint(String customerId, 
                                                        VideoSessionInitRequest.BrowserFingerprint fingerprint, 
                                                        String ipAddress) {
        try {
            if (fingerprint == null || fingerprint.getFingerprintHash() == null) {
                return new FingerprintUpdateResult(false, false, null);
            }

            String fingerprintHash = fingerprint.getFingerprintHash();
            UserFingerprint existingFingerprint = userFingerprintBuilder.getByFingerprintHash(fingerprintHash);
            
            boolean isNewFingerprint = existingFingerprint == null;
            String fingerprintId;

            if (isNewFingerprint) {
                // 创建新指纹记录
                UserFingerprint newFingerprint = new UserFingerprint();
                newFingerprint.setCustomerId(customerId);
                newFingerprint.setFingerprintHash(fingerprintHash);
                newFingerprint.setAttributes(serializeFingerprintAttributes(fingerprint));
                newFingerprint.setIpAddress(ipAddress);
                newFingerprint.setAssociatedUserIds(customerId);
                newFingerprint.setIsSuspicious(false);

                boolean success = userFingerprintBuilder.insert(newFingerprint);
                fingerprintId = success ? newFingerprint.getId() : null;
                
                return new FingerprintUpdateResult(success, true, fingerprintId);
            } else {
                // 更新现有指纹记录
                updateAssociatedUsers(existingFingerprint, customerId);
                existingFingerprint.setIpAddress(ipAddress);
                
                boolean success = userFingerprintBuilder.update(existingFingerprint);
                fingerprintId = existingFingerprint.getId();
                
                return new FingerprintUpdateResult(success, false, fingerprintId);
            }

        } catch (Exception e) {
            log.error("Failed to update user fingerprint for user: {}", customerId, e);
            FingerprintUpdateResult result = new FingerprintUpdateResult(false, false, null);
            result.setErrorMessage("Failed to update fingerprint: " + e.getMessage());
            return result;
        }
    }

    @Override
    public IpReputationUpdateResult updateIpReputation(String ipAddress, Map<String, Object> reputationData) {
        try {
            IpReputation existingReputation = ipReputationBuilder.getByIpAddress(ipAddress);
            boolean isNewIp = existingReputation == null;

            if (isNewIp) {
                // 创建新IP信誉记录
                IpReputation newReputation = new IpReputation();
                newReputation.setIpAddress(ipAddress);
                populateIpReputationFromData(newReputation, reputationData);
                
                boolean success = ipReputationBuilder.insert(newReputation);
                return new IpReputationUpdateResult(success, true);
            } else {
                // 更新现有IP信誉记录
                populateIpReputationFromData(existingReputation, reputationData);
                
                boolean success = ipReputationBuilder.update(existingReputation);
                return new IpReputationUpdateResult(success, false);
            }

        } catch (Exception e) {
            log.error("Failed to update IP reputation for: {}", ipAddress, e);
            IpReputationUpdateResult result = new IpReputationUpdateResult(false, false);
            result.setErrorMessage("Failed to update IP reputation: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Double> calculateEnvironmentFraudIndicators(String customerId, 
                                                                  VideoSessionInitRequest.BrowserFingerprint fingerprint, 
                                                                  String ipAddress, 
                                                                  VideoSessionInitRequest.DeviceInfo deviceInfo) {
        Map<String, Double> indicators = new HashMap<>();

        try {
            // 2.5 环境不一致 (ENVIRONMENT_INCONSISTENCY)
            EnvironmentConsistencyResult consistencyResult = analyzeEnvironmentConsistency(
                    customerId, fingerprint != null ? fingerprint.getFingerprintHash() : null, ipAddress);
            double environmentInconsistencyScore = 1.0 - consistencyResult.getConsistencyScore();
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.ENVIRONMENT_INCONSISTENCY.getCode(), 
                    environmentInconsistencyScore);

            // 2.10 指纹重复 (FINGERPRINT_DUPLICATION)
            FingerprintAnalysisResult fingerprintResult = analyzeBrowserFingerprint(fingerprint, customerId);
            double fingerprintDuplicationScore = calculateFingerprintDuplicationScore(
                    fingerprintResult.getAssociatedUserCount());
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.FINGERPRINT_DUPLICATION.getCode(), 
                    fingerprintDuplicationScore);

            // 2.11 恶意IP (MALICIOUS_IP)
            IpReputationAnalysisResult ipResult = analyzeIpReputation(ipAddress);
            double maliciousIpScore = ipResult.isMalicious() ? 1.0 : 0.0;
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.MALICIOUS_IP.getCode(), 
                    maliciousIpScore);

            // 可疑用户代理
            UserAgentAnalysisResult userAgentResult = analyzeSuspiciousUserAgent(
                    deviceInfo != null ? deviceInfo.getBrowserName() : null);
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.SUSPICIOUS_USER_AGENT.getCode(), 
                    userAgentResult.getUserAgentRiskScore());

        } catch (Exception e) {
            log.error("Failed to calculate environment fraud indicators", e);
            // 返回高风险指标
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.MALICIOUS_IP.getCode(), 1.0);
        }

        return indicators;
    }

    /**
     * 计算指纹重复分数
     * 使用FraudIndicatorThresholds中的计算公式
     */
    private double calculateFingerprintDuplicationScore(int associatedAccountCount) {
        return FraudIndicatorThresholds.calculateFingerprintDuplicationScore(associatedAccountCount);
    }

    /**
     * 分析指纹特征
     */
    private void analyzeFingerprintFeatures(VideoSessionInitRequest.BrowserFingerprint fingerprint, 
                                          List<String> riskFactors) {
        // 检测常见的机器人指纹特征
        if (fingerprint.getScreenResolution() != null && 
            "1920x1080".equals(fingerprint.getScreenResolution())) {
            // 常见的自动化工具分辨率，但不一定是风险
        }

        if (fingerprint.getPlugins() == null || fingerprint.getPlugins().isEmpty()) {
            riskFactors.add("NO_PLUGINS");
        }

        if (fingerprint.getCanvasFingerprint() == null || fingerprint.getCanvasFingerprint().isEmpty()) {
            riskFactors.add("NO_CANVAS_FINGERPRINT");
        }
    }

    /**
     * 计算指纹风险分数
     */
    private double calculateFingerprintRiskScore(int associatedUserCount, int riskFactorCount) {
        double userCountScore = associatedUserCount > FINGERPRINT_THRESHOLD ? 
                Math.min((double) (associatedUserCount - FINGERPRINT_THRESHOLD) / 
                        (FINGERPRINT_MAX_ALLOWED - FINGERPRINT_THRESHOLD), 1.0) : 0.0;
        
        double riskFactorScore = Math.min(riskFactorCount / 5.0, 1.0);
        
        return Math.max(userCountScore, riskFactorScore);
    }

    /**
     * 计算IP风险分数
     */
    private double calculateIpRiskScore(boolean isMalicious, int reputationScore, int riskIndicatorCount) {
        if (isMalicious) return 1.0;
        
        double reputationRisk = reputationScore < 50 ? (50.0 - reputationScore) / 50.0 : 0.0;
        double indicatorRisk = Math.min(riskIndicatorCount / 3.0, 1.0);
        
        return Math.max(reputationRisk, indicatorRisk);
    }

    /**
     * 计算设备风险分数
     */
    private double calculateDeviceRiskScore(boolean isSuspicious, boolean isVirtualMachine, 
                                          boolean isHeadless, int suspiciousFeatureCount) {
        if (isVirtualMachine || isHeadless) return 1.0;
        if (isSuspicious) return 0.8;
        
        return Math.min(suspiciousFeatureCount / 3.0, 1.0);
    }

    /**
     * 计算一致性分数
     */
    private double calculateConsistencyScore(int inconsistencyCount, int environmentChangeCount) {
        if (environmentChangeCount == 0) return 1.0;
        
        double inconsistencyRatio = (double) inconsistencyCount / environmentChangeCount;
        return Math.max(0.0, 1.0 - inconsistencyRatio);
    }

    /**
     * 计算位置风险分数
     */
    private double calculateLocationRiskScore(boolean isAnomalous, boolean isVpn, int riskFactorCount) {
        if (isVpn) return 1.0;
        if (isAnomalous) return 0.8;
        
        return Math.min(riskFactorCount / 3.0, 1.0);
    }

    /**
     * 计算用户代理风险分数
     */
    private double calculateUserAgentRiskScore(boolean isSuspicious, boolean isBot, 
                                             boolean isHeadless, int suspiciousPatternCount) {
        if (isBot || isHeadless) return 1.0;
        if (isSuspicious) return 0.8;
        
        return Math.min(suspiciousPatternCount / 3.0, 1.0);
    }

    // 其他辅助方法的简化实现
    private boolean detectVirtualMachineFeatures(VideoSessionInitRequest.DeviceInfo deviceInfo, String userAgent) {
        return false; // 简化实现
    }

    private boolean isDeviceInfoConsistent(VideoSessionInitRequest.DeviceInfo deviceInfo, String userAgent) {
        return true; // 简化实现
    }

    private boolean checkFingerprintConsistency(List<UserFingerprint> userFingerprints, 
                                               String currentFingerprint, List<String> inconsistencyReasons) {
        return true; // 简化实现
    }

    private boolean checkIpConsistency(String customerId, String currentIp, List<String> inconsistencyReasons) {
        return true; // 简化实现
    }

    private boolean detectBotPatterns(String userAgent) {
        return userAgent.toLowerCase().contains("bot") || userAgent.toLowerCase().contains("crawler");
    }

    private boolean detectHeadlessPatterns(String userAgent) {
        return userAgent.toLowerCase().contains("headless") || userAgent.toLowerCase().contains("phantom");
    }

    private boolean detectAnomalousUserAgent(String userAgent) {
        return userAgent.length() < 10 || userAgent.length() > 500;
    }

    private String extractBrowserName(String userAgent) {
        if (userAgent.contains("Chrome")) return "Chrome";
        if (userAgent.contains("Firefox")) return "Firefox";
        if (userAgent.contains("Safari")) return "Safari";
        return "UNKNOWN";
    }

    private String extractBrowserVersion(String userAgent) {
        return "UNKNOWN"; // 简化实现
    }

    private String serializeFingerprintAttributes(VideoSessionInitRequest.BrowserFingerprint fingerprint) {
        return "{}"; // 简化实现，应该序列化为JSON
    }

    private void updateAssociatedUsers(UserFingerprint fingerprint, String customerId) {
        String currentUsers = fingerprint.getAssociatedUserIds();
        if (currentUsers == null || !currentUsers.contains(customerId)) {
            String newUsers = currentUsers == null ? customerId : currentUsers + "," + customerId;
            fingerprint.setAssociatedUserIds(newUsers);
        }
    }

    private void populateIpReputationFromData(IpReputation reputation, Map<String, Object> data) {
        // 简化实现，应该从外部数据源填充IP信誉信息
        reputation.setReputationScore(100);
        reputation.setIsProxy(false);
        reputation.setIsVpn(false);
        reputation.setIsTorExitNode(false);
        reputation.setIsDataCenter(false);
        reputation.setCountryCode("UNKNOWN");
        reputation.setSource("INTERNAL");
    }
}
