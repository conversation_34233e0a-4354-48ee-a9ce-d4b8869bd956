package com.drex.core.service.cache.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 会话事件模型
 * 对应设计方案中的sessionEvents缓存结构
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionEvent implements Serializable {

    /**
     * 事件唯一标识符
     */
    private String id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 客户端时间戳
     */
    private Long clientTimestamp;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 事件类型
     * 如：PLAY、PAUSE、BLUR、FOCUS、END、SEEKING、TIMEUPDATE等
     */
    private String eventType;

    /**
     * 事件数据
     * 结构随eventType变化，存储为JSON字符串
     * 
     * 示例：
     * PLAYBACK_STATE_CHANGE: {"newState": "PLAYING", "currentTime": 120.5, "playbackRate": 1.0}
     * FOCUS_CHANGE: {"tabActive": true, "windowFocused": true}
     * TIMEUPDATE: {"currentTime": 120.5, "duration": 300.0}
     * SEEKING: {"from": 100.0, "to": 150.0}
     */
    private Map<String, Object> eventData;

    /**
     * 创建时间
     */
    private Long created;

    /**
     * 事件序号（在会话中的顺序）
     */
    private Integer sequence;

    /**
     * 事件来源IP
     */
    private String sourceIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 浏览器指纹哈希
     */
    private String fingerprintHash;
}
