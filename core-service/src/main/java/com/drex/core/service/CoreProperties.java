package com.drex.core.service;

import lombok.Builder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "core")
public class CoreProperties {

    private String xId;
    private String xHandleName;
    private Integer xTextLength;
    private String youtubeId;
    private Double youtubeDuration;
    private String contractAddress;

    //事务确认消息topic
    private String txsConfirmTopic = "T_DREX_CORE_TRANSACTION";

    private Integer chainId;
    private String rpcUrl;

    // YouTube防刷系统配置
    private String youtubeAntiCheatSecretKey = "default_secret_key";
    private Integer sessionTimeoutMinutes = 60;
    private Integer maxEventsPerSession = 10000;
    private Double minWatchPercentage = 0.7;
    private Integer maxPauseCount = 10;
    private Integer maxSeekCount = 5;
    private Double minFocusPercentage = 0.7;
    private Integer maxIdleSeconds = 300;
    private Integer fingerprintThreshold = 3;
    private Integer ipReputationThreshold = 50;
    private Long ipReputationCacheHours = 24L;

    // 欺诈检测权重配置
    private Double dataValidationWeight = 0.1;
    private Double playbackAnalysisWeight = 0.3;
    private Double focusActivityWeight = 0.2;
    private Double environmentAnalysisWeight = 0.4;

    // 信任分数阈值
    private Double trustScoreThreshold = 0.6;
    private Double highRiskThreshold = 0.8;

}
