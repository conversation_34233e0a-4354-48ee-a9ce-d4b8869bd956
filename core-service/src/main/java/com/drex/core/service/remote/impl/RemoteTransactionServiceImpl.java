package com.drex.core.service.remote.impl;

import com.drex.core.api.RemoteTransactionService;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.response.WalletOperationDTO;
import com.drex.core.service.CoreProperties;
import com.drex.core.service.chain.sign.TransferSigner;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;

import java.math.BigInteger;
import java.time.Duration;

@Slf4j
@DubboService
public class RemoteTransactionServiceImpl implements RemoteTransactionService {

    private final TransferSigner transferSigner;
    private final Web3j web3j;
    private final CoreProperties coreProperties;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private static final String ADDRESS_KEY_PREFIX = "faucet:address:";
    private static final String IP_KEY_PREFIX = "faucet:ip:";
    private static final String LOCK_KEY_PREFIX = "faucet:lock:";
    private static final long LOCK_EXPIRE_TIME = 30; // 锁的过期时间，单位秒
    private static final long DRIP_EXPIRE_TIME = 24 * 60 * 60; // 领取间隔 24 小时，单位秒

    public RemoteTransactionServiceImpl(TransferSigner transferSigner, CoreProperties coreProperties) {
        this.transferSigner = transferSigner;
        this.coreProperties = coreProperties;
        this.web3j = Web3j.build(new HttpService(coreProperties.getRpcUrl(), false));
    }

    @Override
    public Response<WalletOperationDTO> faucetDrip(String toAddress, String dripIP) {
        log.info("faucetDrip request: toAddress={}, dripIP={}", toAddress, dripIP);
        String lockKey = LOCK_KEY_PREFIX + toAddress + ":" + dripIP;
        String lockValue = java.util.UUID.randomUUID().toString();

        // 尝试获取分布式锁
        Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, Duration.ofSeconds(LOCK_EXPIRE_TIME));
        if (locked == null || !locked) {
            log.warn("Failed to acquire lock for toAddress: {}, dripIP: {}", toAddress, dripIP);
            return Response.error(CoreResponseCode.REQUESTS_FREQUENT.getCode(), "Failed to acquire lock");
        }

        try {
            String addressKey = ADDRESS_KEY_PREFIX + toAddress;
            String ipKey = IP_KEY_PREFIX + dripIP;

            // 检查地址是否在 24 小时内领过水
            if (redisTemplate.hasKey(addressKey)) {
                log.warn("Address {} has received drip within 24 hours", toAddress);
                return Response.error(CoreResponseCode.ADDRESS_LIMITED.getCode(), "Address has received drip within 24 hours");
            }

            // 检查 IP 是否在 24 小时内领过水
            if (redisTemplate.hasKey(ipKey)) {
                log.warn("IP {} has received drip within 24 hours", dripIP);
                return Response.error(CoreResponseCode.IP_LIMITED.getCode(), "IP has received drip within 24 hours");
            }

            BigInteger points = new BigInteger("1000000000000000");
            WalletOperationDTO walletOperationDTO = performTransaction(toAddress, points);

            // 将地址和 IP 记录到缓存中，并设置 24 小时过期时间
            redisTemplate.opsForValue().set(addressKey, "1", Duration.ofSeconds(DRIP_EXPIRE_TIME));
            redisTemplate.opsForValue().set(ipKey, "1", Duration.ofSeconds(DRIP_EXPIRE_TIME));

            return Response.success(walletOperationDTO);
        } catch (Exception e) {
            log.error("Failed to send faucet drip transaction", e);
            return Response.error(CoreResponseCode.SYSTEM_ERROR.getCode(), "Drip Failed");
        } finally {
            if (lockValue.equals(redisTemplate.opsForValue().get(lockKey))) {
                redisTemplate.delete(lockKey);
            }
        }
    }

    private WalletOperationDTO performTransaction(String toAddress, BigInteger points) throws Exception {
        Integer chainId = coreProperties.getChainId();
        String transactionHash = transferSigner.sendTransaction(chainId, web3j, toAddress, "0x", points);
        log.info("Faucet drip transaction hash: {}", transactionHash);
        return WalletOperationDTO.builder()
                .targetAddress(toAddress)
                .callData("0x")
                .value(points)
                .txHash(transactionHash)
                .build();
    }
}