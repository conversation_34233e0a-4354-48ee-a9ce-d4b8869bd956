package com.drex.core.service.business.youtube;

import java.util.List;
import java.util.Map;

/**
 * 风控分数计算服务接口
 * 负责根据各种欺诈指标和权重计算最终的风控分数
 * 
 * 注意：风控分数越高表示风险越高（与原信任分相反）
 * 分数范围：0.0（无风险）到 1.0（极高风险）
 */
public interface RiskScoreCalculationService {

    /**
     * 计算综合风控分数
     * 
     * 基础公式框架：RiskScore = Σ(IndicatorValue × Weight)
     * 风控分数越高表示风险越高
     *
     * @param fraudIndicators 所有欺诈指标 (指标类型 -> 指标值)
     * @param weights 权重配置 (指标类型 -> 权重)
     * @return 风控分数计算结果
     */
    RiskScoreResult calculateRiskScore(Map<String, Double> fraudIndicators, 
                                      Map<String, Double> weights);

    /**
     * 计算分类风控分数
     * 按照不同的分析服务分类计算子分数
     *
     * @param dataValidationIndicators 数据验证指标
     * @param playbackAnalysisIndicators 播放分析指标
     * @param focusActivityIndicators 焦点活动指标
     * @param environmentAnalysisIndicators 环境分析指标
     * @return 分类风控分数结果
     */
    CategorizedRiskScoreResult calculateCategorizedRiskScore(
            Map<String, Double> dataValidationIndicators,
            Map<String, Double> playbackAnalysisIndicators,
            Map<String, Double> focusActivityIndicators,
            Map<String, Double> environmentAnalysisIndicators);

    /**
     * 获取默认权重配置
     *
     * @return 默认权重映射
     */
    Map<String, Double> getDefaultWeights();

    /**
     * 获取分类权重配置
     *
     * @return 分类权重映射
     */
    Map<String, Double> getCategoryWeights();

    /**
     * 更新权重配置
     *
     * @param weights 新的权重配置
     * @return 是否更新成功
     */
    boolean updateWeights(Map<String, Double> weights);

    /**
     * 验证权重配置的有效性
     *
     * @param weights 权重配置
     * @return 验证结果
     */
    WeightValidationResult validateWeights(Map<String, Double> weights);

    /**
     * 计算风险等级
     *
     * @param riskScore 风控分数
     * @return 风险等级
     */
    RiskLevel calculateRiskLevel(double riskScore);

    /**
     * 获取风控分数阈值配置
     *
     * @return 阈值配置
     */
    RiskScoreThresholds getThresholds();

    /**
     * 分析风控分数趋势
     *
     * @param customerId 用户ID
     * @param recentScores 最近的风控分数列表
     * @return 趋势分析结果
     */
    RiskScoreTrendAnalysis analyzeRiskScoreTrend(String customerId, List<Double> recentScores);

    /**
     * 计算风控分数置信度
     *
     * @param fraudIndicators 欺诈指标
     * @param dataQuality 数据质量评分
     * @return 置信度分数 (0-1)
     */
    double calculateConfidenceScore(Map<String, Double> fraudIndicators, double dataQuality);

    /**
     * 风控分数计算结果
     */
    class RiskScoreResult {
        private double finalRiskScore;
        private double rawScore;
        private double normalizedScore;
        private RiskLevel riskLevel;
        private double confidenceScore;
        private Map<String, Double> contributionByIndicator;
        private Map<String, Object> calculationDetails;
        private long calculationTimestamp;

        public RiskScoreResult(double finalRiskScore, double rawScore, RiskLevel riskLevel) {
            this.finalRiskScore = finalRiskScore;
            this.rawScore = rawScore;
            this.riskLevel = riskLevel;
            this.calculationTimestamp = System.currentTimeMillis();
        }

        // Getters and setters
        public double getFinalRiskScore() { return finalRiskScore; }
        public double getRawScore() { return rawScore; }
        public double getNormalizedScore() { return normalizedScore; }
        public void setNormalizedScore(double normalizedScore) { this.normalizedScore = normalizedScore; }
        public RiskLevel getRiskLevel() { return riskLevel; }
        public double getConfidenceScore() { return confidenceScore; }
        public void setConfidenceScore(double confidenceScore) { this.confidenceScore = confidenceScore; }
        public Map<String, Double> getContributionByIndicator() { return contributionByIndicator; }
        public void setContributionByIndicator(Map<String, Double> contributionByIndicator) { 
            this.contributionByIndicator = contributionByIndicator; 
        }
        public Map<String, Object> getCalculationDetails() { return calculationDetails; }
        public void setCalculationDetails(Map<String, Object> calculationDetails) { 
            this.calculationDetails = calculationDetails; 
        }
        public long getCalculationTimestamp() { return calculationTimestamp; }
    }

    /**
     * 分类风控分数结果
     */
    class CategorizedRiskScoreResult {
        private double overallRiskScore;
        private double dataValidationScore;
        private double playbackAnalysisScore;
        private double focusActivityScore;
        private double environmentAnalysisScore;
        private Map<String, Double> categoryWeights;
        private RiskLevel overallRiskLevel;

        public CategorizedRiskScoreResult(double overallRiskScore, double dataValidationScore,
                                         double playbackAnalysisScore, double focusActivityScore,
                                         double environmentAnalysisScore) {
            this.overallRiskScore = overallRiskScore;
            this.dataValidationScore = dataValidationScore;
            this.playbackAnalysisScore = playbackAnalysisScore;
            this.focusActivityScore = focusActivityScore;
            this.environmentAnalysisScore = environmentAnalysisScore;
        }

        // Getters and setters
        public double getOverallRiskScore() { return overallRiskScore; }
        public double getDataValidationScore() { return dataValidationScore; }
        public double getPlaybackAnalysisScore() { return playbackAnalysisScore; }
        public double getFocusActivityScore() { return focusActivityScore; }
        public double getEnvironmentAnalysisScore() { return environmentAnalysisScore; }
        public Map<String, Double> getCategoryWeights() { return categoryWeights; }
        public void setCategoryWeights(Map<String, Double> categoryWeights) { 
            this.categoryWeights = categoryWeights; 
        }
        public RiskLevel getOverallRiskLevel() { return overallRiskLevel; }
        public void setOverallRiskLevel(RiskLevel overallRiskLevel) { this.overallRiskLevel = overallRiskLevel; }
    }

    /**
     * 权重验证结果
     */
    class WeightValidationResult {
        private boolean isValid;
        private List<String> validationErrors;
        private double totalWeight;
        private Map<String, String> recommendations;

        public WeightValidationResult(boolean isValid, double totalWeight) {
            this.isValid = isValid;
            this.totalWeight = totalWeight;
        }

        // Getters and setters
        public boolean isValid() { return isValid; }
        public List<String> getValidationErrors() { return validationErrors; }
        public void setValidationErrors(List<String> validationErrors) { 
            this.validationErrors = validationErrors; 
        }
        public double getTotalWeight() { return totalWeight; }
        public Map<String, String> getRecommendations() { return recommendations; }
        public void setRecommendations(Map<String, String> recommendations) { 
            this.recommendations = recommendations; 
        }
    }

    /**
     * 风控分数趋势分析
     */
    class RiskScoreTrendAnalysis {
        private String trendDirection; // INCREASING, DECREASING, STABLE
        private double trendSlope;
        private double averageScore;
        private double scoreVariance;
        private boolean isVolatile;
        private List<String> trendInsights;

        public RiskScoreTrendAnalysis(String trendDirection, double trendSlope, double averageScore) {
            this.trendDirection = trendDirection;
            this.trendSlope = trendSlope;
            this.averageScore = averageScore;
        }

        // Getters and setters
        public String getTrendDirection() { return trendDirection; }
        public double getTrendSlope() { return trendSlope; }
        public double getAverageScore() { return averageScore; }
        public double getScoreVariance() { return scoreVariance; }
        public void setScoreVariance(double scoreVariance) { this.scoreVariance = scoreVariance; }
        public boolean isVolatile() { return isVolatile; }
        public void setVolatile(boolean volatile_) { isVolatile = volatile_; }
        public List<String> getTrendInsights() { return trendInsights; }
        public void setTrendInsights(List<String> trendInsights) { this.trendInsights = trendInsights; }
    }

    /**
     * 风险等级枚举
     * 注意：风控分数越高，风险等级越高
     */
    enum RiskLevel {
        LOW("LOW", "低风险", 0.0, 0.3),
        MEDIUM("MEDIUM", "中等风险", 0.3, 0.6),
        HIGH("HIGH", "高风险", 0.6, 0.8),
        CRITICAL("CRITICAL", "极高风险", 0.8, 1.0);

        private final String code;
        private final String description;
        private final double minThreshold;
        private final double maxThreshold;

        RiskLevel(String code, String description, double minThreshold, double maxThreshold) {
            this.code = code;
            this.description = description;
            this.minThreshold = minThreshold;
            this.maxThreshold = maxThreshold;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
        public double getMinThreshold() { return minThreshold; }
        public double getMaxThreshold() { return maxThreshold; }

        public static RiskLevel fromScore(double score) {
            for (RiskLevel level : values()) {
                if (score >= level.minThreshold && score < level.maxThreshold) {
                    return level;
                }
            }
            return CRITICAL; // 默认为最高风险
        }
    }

    /**
     * 风控分数阈值配置
     */
    class RiskScoreThresholds {
        private double lowRiskThreshold;
        private double mediumRiskThreshold;
        private double highRiskThreshold;
        private double rewardEligibilityThreshold; // 奖励资格阈值（低于此分数才能获得奖励）
        private double autoRejectThreshold; // 自动拒绝阈值（高于此分数自动拒绝）

        public RiskScoreThresholds(double lowRiskThreshold, double mediumRiskThreshold, 
                                  double highRiskThreshold, double rewardEligibilityThreshold, 
                                  double autoRejectThreshold) {
            this.lowRiskThreshold = lowRiskThreshold;
            this.mediumRiskThreshold = mediumRiskThreshold;
            this.highRiskThreshold = highRiskThreshold;
            this.rewardEligibilityThreshold = rewardEligibilityThreshold;
            this.autoRejectThreshold = autoRejectThreshold;
        }

        // Getters
        public double getLowRiskThreshold() { return lowRiskThreshold; }
        public double getMediumRiskThreshold() { return mediumRiskThreshold; }
        public double getHighRiskThreshold() { return highRiskThreshold; }
        public double getRewardEligibilityThreshold() { return rewardEligibilityThreshold; }
        public double getAutoRejectThreshold() { return autoRejectThreshold; }
    }
}
