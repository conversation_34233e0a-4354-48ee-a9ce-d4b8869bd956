package com.drex.core.service.mapperstruct;

import com.drex.core.api.request.NoticeDTO;
import com.drex.core.dal.tablestore.model.Notice;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NoticeMapperStruct {

    /**
     * Notice 转 NoticeDTO
     *
     * @param notice Notice实体
     * @return NoticeDTO
     */
    NoticeDTO toNoticeDTO(Notice notice);

    /**
     * NoticeDTO 转 Notice
     *
     * @param noticeDTO NoticeDTO
     * @return Notice实体
     */
    @Mapping(target = "created", ignore = true)
    @Mapping(target = "modified", ignore = true)
    Notice toNotice(NoticeDTO noticeDTO);

    /**
     * Notice列表 转 NoticeDTO列表
     *
     * @param noticeList Notice实体列表
     * @return NoticeDTO列表
     */
    List<NoticeDTO> toNoticeDTOList(List<Notice> noticeList);
}
