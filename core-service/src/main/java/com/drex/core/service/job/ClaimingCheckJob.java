package com.drex.core.service.job;

import cn.hutool.core.collection.CollectionUtil;
import com.drex.core.api.common.BusinessMonitorConstant;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.response.MaizeKernelDTO;
import com.drex.core.dal.tablestore.builder.CustomerRexyBasketsBuilder;
import com.drex.core.dal.tablestore.model.RexyClaimRecord;
import com.drex.core.service.business.rexy.RexyBasketService;
import com.drex.core.service.business.rexy.RexyClaimRecordService;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class ClaimingCheckJob implements SimpleJob {

    @Resource
    @Lazy
    private RexyClaimRecordService rexyClaimRecordService;

    @Resource
    @Lazy
    private RexyBasketService rexyBasketService;

    @Resource
    @Lazy
    private KiKiMonitor kikiMonitor;

    @Override
    public void execute(ShardingContext shardingContext) {
        List<RexyClaimRecord> claimRecords = rexyClaimRecordService.getByStatus(RexyConstant.CommonStatus.TRYING.name());
        if(CollectionUtil.isNotEmpty(claimRecords)){
            kikiMonitor.increment(BusinessMonitorConstant.CLAIM_MAIZE_JOB, new String[]{"status",RexyConstant.CommonStatus.TRYING.name()}, claimRecords.size());
            claimRecords.forEach(record -> {
                log.info("claimMaizeKernelJob request: {}", record);
                Response<MaizeKernelDTO> maizeKernelDTOResponse = rexyBasketService.claimMaizeKernel(record);
                log.info("claimMaizeKernelJob result: {}", maizeKernelDTOResponse);
            });
        }
    }
}
