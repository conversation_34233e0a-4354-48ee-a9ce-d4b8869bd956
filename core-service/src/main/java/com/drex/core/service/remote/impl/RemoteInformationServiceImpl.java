package com.drex.core.service.remote.impl;

import com.drex.core.api.RemoteInformationService;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.service.business.rexy.InformationService;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

@Slf4j
@DubboService
public class RemoteInformationServiceImpl implements RemoteInformationService {

    @Resource
    private InformationService informationService;

    @Override
    public Response<InformationDTO> getById(String id) {
        log.info("getById id: {}", id);
        try {
            InformationDTO informationDTO = informationService.getById(id);
            return Response.success(informationDTO);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<Boolean> saveInformation(InformationDTO informationDTO) {
        log.info("saveInformation informationDTO: {}", informationDTO);
        try {
            Boolean result = informationService.saveInformation(informationDTO);
            return Response.success(result);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateInformation(InformationDTO informationDTO) {
        log.info("updateInformation informationDTO: {}", informationDTO);
        try {
            Boolean result = informationService.updateInformation(informationDTO);
            return Response.success(result);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<Boolean> deleteInformation(String id) {
        log.info("deleteInformation id: {}", id);
        try {
            Boolean result = informationService.deleteInformation(id);
            return Response.success(result);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<List<InformationDTO>> getByType(String type) {
        log.info("getByType type: {}", type);
        try {
            List<InformationDTO> informationDTOList = informationService.getByType(type);
            return Response.success(informationDTOList);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<List<InformationDTO>> getByRecommend(Boolean isRecommend, String position) {
        log.info("getByRecommend isRecommend: {}", isRecommend);
        try {
            List<InformationDTO> informationDTOList = informationService.getByRecommend(isRecommend, position);
            return Response.success(informationDTOList);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<List<InformationDTO>> getByTypeAndRecommend(String type, Boolean isRecommend) {
        log.info("getByTypeAndRecommend type: {}, isRecommend: {}", type, isRecommend);
        try {
            List<InformationDTO> informationDTOList = informationService.getByTypeAndRecommend(type, isRecommend);
            return Response.success(informationDTOList);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }

    @Override
    public Response<List<InformationDTO>> listAll(Integer offset, Integer limit, String position) {
        try {
            List<InformationDTO> informationDTOList = informationService.listAll(offset, limit, position);
            return Response.success(informationDTOList);
        } catch (CoreException e) {
            return Response.error(e.getCode().getCode(), e.getMessage());
        }
    }
}
