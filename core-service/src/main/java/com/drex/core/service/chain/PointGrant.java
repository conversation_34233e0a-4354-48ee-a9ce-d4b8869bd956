package com.drex.core.service.chain;

import com.drex.core.api.response.WalletOperationDTO;
import com.drex.core.model.BasketPoint;
import com.drex.core.service.CoreProperties;
import com.drex.core.service.chain.sign.ISigner;
import com.drex.core.service.chain.sign.KmsSigner;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Bytes31;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Hash;
import org.web3j.utils.Numeric;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.drex.core.model.BasketPoint.FUNC_CLAIMPOINTS;

@Slf4j
@Service
public class PointGrant {

    private final KmsSigner kmsSigner;

    @Resource
    private CoreProperties coreProperties;


    public PointGrant(KmsSigner kmsSigner) {
        this.kmsSigner = kmsSigner;
    }

    public WalletOperationDTO buildClaimOperation(String address, String businessId, BigInteger points) {
        String param = FunctionEncoder.encodeConstructorPacked(
                List.of(
                        new Address(address),
                        new Uint256(Numeric.toBigInt(businessId)),
                        new Uint256(points)
                )
        );
        String sign = kmsSigner.signPrefixedMessage(Hash.sha3(Numeric.hexStringToByteArray(param)));
        String calldata = FunctionEncoder.encode(new Function(
                FUNC_CLAIMPOINTS,
                Arrays.asList(new Uint256(Numeric.toBigInt(businessId)),
                        new Uint256(points),
                        new DynamicBytes(Numeric.hexStringToByteArray(sign))),
                Collections.emptyList()));

        return WalletOperationDTO.builder().targetAddress(coreProperties.getContractAddress()).callData(calldata).value(points).build();
    }

    public BasketPoint.ClaimPointsModel decodeClaimOperation(String calldata) {
        calldata = "0x" + calldata.substring(10);
        List<Type> typeList =
                FunctionReturnDecoder.decode(calldata, List.of((org.web3j.abi.TypeReference) new org.web3j.abi.TypeReference<Uint256>() {
                }, (org.web3j.abi.TypeReference) new org.web3j.abi.TypeReference<Uint256>() {
                }, (org.web3j.abi.TypeReference) new org.web3j.abi.TypeReference<DynamicBytes>() {
                }));
        // 进行解码
        if (typeList.size() >= 3) {
            BigInteger decodedBusinessId = ((Uint256) typeList.get(0)).getValue();
            BigInteger decodedPoints = ((Uint256) typeList.get(1)).getValue();
            byte[] decodedSign = ((DynamicBytes) typeList.get(2)).getValue();
            log.info("Decoded BusinessId: {}, decodedPoints: {}, decodedSign: {}", decodedBusinessId, decodedPoints, decodedSign);
            return BasketPoint.ClaimPointsModel.builder().businessId(Numeric.toHexStringNoPrefix(decodedBusinessId)).points(decodedPoints).build();
        }
        return null;
    }

    public BasketPoint.ClaimPointsModel decodeClaimOperationNoPrefix(String calldata) {
        List<Type> typeList =
                FunctionReturnDecoder.decode(calldata, List.of((org.web3j.abi.TypeReference) new org.web3j.abi.TypeReference<Uint256>() {
                }, (org.web3j.abi.TypeReference) new org.web3j.abi.TypeReference<Uint256>() {
                }));
        // 进行解码
        if (typeList.size() >= 2) {
            BigInteger decodedBusinessId = ((Uint256) typeList.get(0)).getValue();
            BigInteger decodedPoints = ((Uint256) typeList.get(1)).getValue();
            log.info("Decoded BusinessId: {}, decodedPoints: {}", decodedBusinessId, decodedPoints);
            return BasketPoint.ClaimPointsModel.builder().businessId(Numeric.toHexStringNoPrefix(decodedBusinessId)).points(decodedPoints).build();
        }
        return null;
    }
}
