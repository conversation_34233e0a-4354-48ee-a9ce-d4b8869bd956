package com.drex.core.service.business.youtube.util;

import com.drex.core.api.request.RexyReportRequest;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 事件数据处理工具类
 * 负责处理6种不同类型的视频播放事件
 */
@Slf4j
@Component
public class EventDataProcessor {

    /**
     * 处理PLAYING状态事件
     * 包含newState、currentTime、playbackRate字段
     */
    public Map<String, Object> processPlayingEvent(RexyReportRequest.PlayingEventData playingData) {
        Map<String, Object> processedData = new HashMap<>();
        
        if (playingData != null) {
            processedData.put("newState", playingData.getNewState());
            processedData.put("currentTime", playingData.getCurrentTime());
            processedData.put("playbackRate", playingData.getPlaybackRate());
            
            // 添加处理逻辑标记
            processedData.put("eventProcessed", true);
            processedData.put("eventCategory", "PLAYBACK_STATE");
            
            log.debug("Processed PLAYING event: state={}, time={}, rate={}", 
                    playingData.getNewState(), playingData.getCurrentTime(), playingData.getPlaybackRate());
        }
        
        return processedData;
    }

    /**
     * 处理PAUSED状态事件
     * 包含newState、currentTime字段
     */
    public Map<String, Object> processPausedEvent(RexyReportRequest.PausedEventData pausedData) {
        Map<String, Object> processedData = new HashMap<>();
        
        if (pausedData != null) {
            processedData.put("newState", pausedData.getNewState());
            processedData.put("currentTime", pausedData.getCurrentTime());
            
            // 添加处理逻辑标记
            processedData.put("eventProcessed", true);
            processedData.put("eventCategory", "PLAYBACK_STATE");
            
            log.debug("Processed PAUSED event: state={}, time={}", 
                    pausedData.getNewState(), pausedData.getCurrentTime());
        }
        
        return processedData;
    }

    /**
     * 处理SEEK跳转事件
     * 包含currentTime、previousTime字段
     */
    public Map<String, Object> processSeekEvent(RexyReportRequest.SeekEventData seekData) {
        Map<String, Object> processedData = new HashMap<>();
        
        if (seekData != null) {
            processedData.put("currentTime", seekData.getCurrentTime());
            processedData.put("previousTime", seekData.getPreviousTime());
            
            // 计算跳转距离
            double seekDistance = Math.abs(seekData.getCurrentTime() - seekData.getPreviousTime());
            processedData.put("seekDistance", seekDistance);
            
            // 判断跳转方向
            String seekDirection = seekData.getCurrentTime() > seekData.getPreviousTime() ? "FORWARD" : "BACKWARD";
            processedData.put("seekDirection", seekDirection);
            
            // 添加处理逻辑标记
            processedData.put("eventProcessed", true);
            processedData.put("eventCategory", "SEEK_BEHAVIOR");
            
            log.debug("Processed SEEK event: from={} to={}, distance={}, direction={}", 
                    seekData.getPreviousTime(), seekData.getCurrentTime(), seekDistance, seekDirection);
        }
        
        return processedData;
    }

    /**
     * 处理FOCUS_LOST失焦事件
     * 包含tabActive、windowFocused字段
     */
    public Map<String, Object> processFocusLostEvent(RexyReportRequest.FocusLostEventData focusLostData) {
        Map<String, Object> processedData = new HashMap<>();
        
        if (focusLostData != null) {
            processedData.put("tabActive", focusLostData.getTabActive());
            processedData.put("windowFocused", focusLostData.getWindowFocused());
            
            // 计算焦点丢失级别
            String focusLossLevel = calculateFocusLossLevel(focusLostData.getTabActive(), focusLostData.getWindowFocused());
            processedData.put("focusLossLevel", focusLossLevel);
            
            // 添加处理逻辑标记
            processedData.put("eventProcessed", true);
            processedData.put("eventCategory", "FOCUS_CHANGE");
            
            log.debug("Processed FOCUS_LOST event: tabActive={}, windowFocused={}, level={}", 
                    focusLostData.getTabActive(), focusLostData.getWindowFocused(), focusLossLevel);
        }
        
        return processedData;
    }

    /**
     * 处理FOCUS_GAINED聚焦事件
     * 包含tabActive、windowFocused字段
     */
    public Map<String, Object> processFocusGainedEvent(RexyReportRequest.FocusGainedEventData focusGainedData) {
        Map<String, Object> processedData = new HashMap<>();
        
        if (focusGainedData != null) {
            processedData.put("tabActive", focusGainedData.getTabActive());
            processedData.put("windowFocused", focusGainedData.getWindowFocused());
            
            // 计算焦点恢复级别
            String focusGainLevel = calculateFocusGainLevel(focusGainedData.getTabActive(), focusGainedData.getWindowFocused());
            processedData.put("focusGainLevel", focusGainLevel);
            
            // 添加处理逻辑标记
            processedData.put("eventProcessed", true);
            processedData.put("eventCategory", "FOCUS_CHANGE");
            
            log.debug("Processed FOCUS_GAINED event: tabActive={}, windowFocused={}, level={}", 
                    focusGainedData.getTabActive(), focusGainedData.getWindowFocused(), focusGainLevel);
        }
        
        return processedData;
    }

    /**
     * 处理USER_STATE用户状态事件
     * 包含state字段（ACTIVE/IDLE/LOCKED）
     */
    public Map<String, Object> processUserStateEvent(RexyReportRequest.UserStateEventData userStateData) {
        Map<String, Object> processedData = new HashMap<>();
        
        if (userStateData != null) {
            String state = userStateData.getState();
            processedData.put("state", state);
            processedData.put("userActivityState", state);
            
            // 标准化状态值
            String normalizedState = normalizeUserState(state);
            processedData.put("normalizedState", normalizedState);
            
            // 计算状态权重（用于后续分析）
            double stateWeight = calculateStateWeight(normalizedState);
            processedData.put("stateWeight", stateWeight);
            
            // 添加处理逻辑标记
            processedData.put("eventProcessed", true);
            processedData.put("eventCategory", "USER_ACTIVITY");
            
            log.debug("Processed USER_STATE event: state={}, normalized={}, weight={}", 
                    state, normalizedState, stateWeight);
        }
        
        return processedData;
    }

    /**
     * 批量处理事件数据
     */
    public List<SessionEvent> processEventsBatch(List<RexyReportRequest.EventData> eventDataList) {
        return eventDataList.stream()
                .map(this::processEventData)
                .collect(Collectors.toList());
    }

    /**
     * 处理单个事件数据
     */
    public SessionEvent processEventData(RexyReportRequest.EventData eventData) {
        SessionEvent sessionEvent = SessionEvent.builder()
                .id(eventData.getEventId())
                .eventType(eventData.getEventType())
                .clientTimestamp(eventData.getTimestamp())
                .sequence(eventData.getSequence())
                .serverTimestamp(System.currentTimeMillis())
                .created(System.currentTimeMillis())
                .build();

        Map<String, Object> processedEventData = new HashMap<>();
        
        // 根据事件类型处理详细数据
        if (eventData.getDetails() != null) {
            RexyReportRequest.EventDetails details = eventData.getDetails();
            
            switch (eventData.getEventType()) {
                case "PLAYING":
                    processedEventData.putAll(processPlayingEvent(details.getPlayingData()));
                    break;
                case "PAUSED":
                    processedEventData.putAll(processPausedEvent(details.getPausedData()));
                    break;
                case "SEEK":
                    processedEventData.putAll(processSeekEvent(details.getSeekData()));
                    break;
                case "FOCUS_LOST":
                    processedEventData.putAll(processFocusLostEvent(details.getFocusLostData()));
                    break;
                case "FOCUS_GAINED":
                    processedEventData.putAll(processFocusGainedEvent(details.getFocusGainedData()));
                    break;
                case "USER_STATE":
                    processedEventData.putAll(processUserStateEvent(details.getUserStateData()));
                    break;
                default:
                    log.debug("Event type {} does not require special processing", eventData.getEventType());
                    break;
            }
        }
        
        sessionEvent.setEventData(processedEventData);
        return sessionEvent;
    }

    /**
     * 计算焦点丢失级别
     */
    private String calculateFocusLossLevel(Boolean tabActive, Boolean windowFocused) {
        if (Boolean.FALSE.equals(tabActive) && Boolean.FALSE.equals(windowFocused)) {
            return "COMPLETE_LOSS";
        } else if (Boolean.FALSE.equals(tabActive)) {
            return "TAB_INACTIVE";
        } else if (Boolean.FALSE.equals(windowFocused)) {
            return "WINDOW_UNFOCUSED";
        } else {
            return "MINIMAL_LOSS";
        }
    }

    /**
     * 计算焦点恢复级别
     */
    private String calculateFocusGainLevel(Boolean tabActive, Boolean windowFocused) {
        if (Boolean.TRUE.equals(tabActive) && Boolean.TRUE.equals(windowFocused)) {
            return "COMPLETE_GAIN";
        } else if (Boolean.TRUE.equals(tabActive)) {
            return "TAB_ACTIVE";
        } else if (Boolean.TRUE.equals(windowFocused)) {
            return "WINDOW_FOCUSED";
        } else {
            return "PARTIAL_GAIN";
        }
    }

    /**
     * 标准化用户状态
     */
    private String normalizeUserState(String state) {
        if (state == null) {
            return "UNKNOWN";
        }
        
        String upperState = state.toUpperCase();
        switch (upperState) {
            case "ACTIVE":
            case "ENGAGED":
            case "INTERACTING":
                return "ACTIVE";
            case "IDLE":
            case "INACTIVE":
            case "AWAY":
                return "IDLE";
            case "LOCKED":
            case "SCREEN_LOCKED":
            case "SUSPENDED":
                return "LOCKED";
            default:
                return "UNKNOWN";
        }
    }

    /**
     * 计算状态权重
     */
    private double calculateStateWeight(String normalizedState) {
        switch (normalizedState) {
            case "ACTIVE":
                return 1.0;
            case "IDLE":
                return 0.3;
            case "LOCKED":
                return 0.0;
            default:
                return 0.5;
        }
    }
}
