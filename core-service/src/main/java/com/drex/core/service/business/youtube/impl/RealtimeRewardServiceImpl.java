package com.drex.core.service.business.youtube.impl;

import com.drex.core.api.rules.youtube.YouTubeRewardRules;
import com.drex.core.api.websocket.YouTubeWebSocketMessage;
import com.drex.core.dal.tablestore.builder.InformationBuilder;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.business.rexy.Base62Encoding;
import com.drex.core.service.business.youtube.*;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.config.YouTubeRewardProperties;
import com.drex.core.service.util.async.executor.Async;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 实时奖励服务实现
 */
@Slf4j
@Service
public class RealtimeRewardServiceImpl implements RealtimeRewardService {

    @Autowired
    private PlaybackAnalysisService playbackAnalysisService;

    @Autowired
    private TrustScoreCalculationService trustScoreCalculationService;

    @Autowired
    private InformationBuilder informationBuilder;

    @Autowired
    private MaizeRecordBuilder maizeRecordBuilder;

    @Autowired
    private Base62Encoding base62Encoding;

    @Autowired
    private AsyncTool asyncTool;

    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;

    private final Random random = new Random();

    @Override
    public RealtimeProcessingResult processRealtimeEvents(String sessionId, String customerId, String videoId,
                                                         List<SessionEvent> events, int videoDurationSeconds) {
        try {
            log.debug("Processing realtime events for session: {}, events: {}", sessionId, events.size());

            // 使用新的AsyncTool编排异步处理流程
            try {
                // 创建事件处理任务
                AsyncTool.AsyncTaskWrapper<EventProcessingResult> eventTask = asyncTool.createTask(
                        "event-processing-" + sessionId,
                        () -> {
                            int effectiveWatchSeconds = playbackAnalysisService.calculateEffectiveWatchTime(events);
                            Integer currentProgress = calculateProgress(effectiveWatchSeconds, videoDurationSeconds);
                            return new EventProcessingResult(effectiveWatchSeconds, currentProgress);
                        },
                        AsyncTool.TaskType.EVENT_PROCESSING
                ).timeout(3000); // 3秒超时

                // 创建欺诈检测任务
                AsyncTool.AsyncTaskWrapper<Double> fraudTask = asyncTool.createTask(
                        "fraud-detection-" + sessionId,
                        () -> calculateTrustScore(events, videoDurationSeconds),
                        AsyncTool.TaskType.FRAUD_DETECTION
                ).timeout(2000); // 2秒超时

                //使用asyncTool来编排
                // 并行执行事件处理和欺诈检测
                CompletableFuture<EventProcessingResult> eventFuture = eventTask.execute();
                CompletableFuture<Double> fraudFuture = fraudTask.execute();

                // 等待两个任务完成
                EventProcessingResult eventResult = eventFuture.get(5, TimeUnit.SECONDS);
                Double trustScore = fraudFuture.get(5, TimeUnit.SECONDS);

                if (trustScore == null) {
                    trustScore = 0.5; // 默认信任分数
                }

                // 构建进度信息
                YouTubeWebSocketMessage.ProgressInfo progressInfo = buildProgressInfo(
                        eventResult.getCurrentProgress(), eventResult.getEffectiveWatchSeconds(),
                        videoDurationSeconds, trustScore);

                RealtimeProcessingResult result = new RealtimeProcessingResult(true, "Processing completed");
                result.setProgressInfo(progressInfo);
                result.setTrustScore(trustScore);

                // 检查是否可以获得奖励
                if (eventResult.getCurrentProgress() != null &&
                    canReceiveReward(customerId, sessionId, videoId, eventResult.getCurrentProgress())) {

                    // 创建奖励计算任务
                    AsyncTool.AsyncTaskWrapper<RewardGenerationResult> rewardTask = asyncTool.createTask(
                            "reward-calculation-" + sessionId,
                            () -> generateReward(customerId, sessionId, videoId,
                                    eventResult.getCurrentProgress(), trustScore),
                            AsyncTool.TaskType.REWARD_CALCULATION
                    ).timeout(2000); // 2秒超时

                    RewardGenerationResult rewardResult = rewardTask.execute().get(3, TimeUnit.SECONDS);

                    if (rewardResult != null && rewardResult.isSuccess()) {
                        YouTubeWebSocketMessage.RewardInfo rewardInfo = YouTubeWebSocketMessage.RewardInfo.builder()
                                .rewardCode(rewardResult.getRewardCode())
                                .rewardScore(rewardResult.getRewardScore())
                                .rewardLevel(rewardResult.getRewardLevel())
                                .progress(eventResult.getCurrentProgress())
                                .expirationTime(rewardResult.getExpirationTime())
                                .description("恭喜获得进度" + eventResult.getCurrentProgress() + "奖励！")
                                .build();

                        result.setRewardInfo(rewardInfo);
                        result.setHasNewReward(true);
                        log.info("Generated reward for session: {}, progress: {}, code: {}",
                                sessionId, eventResult.getCurrentProgress(), rewardResult.getRewardCode());
                    }
                }

                return result;

            } catch (TimeoutException e) {
                log.error("Async processing timeout for session: {}", sessionId, e);
                return new RealtimeProcessingResult(false, "Processing timeout");
            } catch (ExecutionException e) {
                log.error("Async processing execution failed for session: {}", sessionId, e);
                return new RealtimeProcessingResult(false, "Processing execution failed");
            } catch (InterruptedException e) {
                log.error("Async processing interrupted for session: {}", sessionId, e);
                Thread.currentThread().interrupt();
                return new RealtimeProcessingResult(false, "Processing interrupted");
            }

        } catch (Exception e) {
            log.error("Failed to process realtime events for session: {}", sessionId, e);
            return new RealtimeProcessingResult(false, "Processing failed: " + e.getMessage());
        }
    }

    /**
     * 事件处理结果
     */
    private static class EventProcessingResult {
        private final int effectiveWatchSeconds;
        private final Integer currentProgress;

        public EventProcessingResult(int effectiveWatchSeconds, Integer currentProgress) {
            this.effectiveWatchSeconds = effectiveWatchSeconds;
            this.currentProgress = currentProgress;
        }

        public int getEffectiveWatchSeconds() { return effectiveWatchSeconds; }
        public Integer getCurrentProgress() { return currentProgress; }
    }

    @Override
    public Integer calculateProgress(int effectiveWatchSeconds, int videoDurationSeconds) {
        if (videoDurationSeconds <= 0) {
            return null;
        }

        double watchPercentage = (double) effectiveWatchSeconds / videoDurationSeconds;
        
        // 根据有效播放时长占视频总时长的百分比计算progress进度
        if (watchPercentage >= 0.2 && watchPercentage <= 0.4) {
            return 1;
        } else if (watchPercentage >= 0.5 && watchPercentage <= 0.7) {
            return 2;
        } else if (watchPercentage >= 0.8 && watchPercentage <= 1.2) {
            return 3;
        }
        
        return null;
    }

    @Override
    public boolean canReceiveReward(String customerId, String sessionId, String videoId, Integer progress) {
        try {
            // 使用组合键查询maizeRecord表检查是否已发放奖励
            // customerId + socialEvent + socialPlatform + socialContentId + sessionId + progress
            MaizeRecord existingRecord = maizeRecordBuilder.findMaizeRecordByCompositeKey(
                    customerId, "watch", "youtube", videoId, sessionId, progress);
            
            return existingRecord == null;
            
        } catch (Exception e) {
            log.error("Failed to check reward eligibility for session: {}, progress: {}", sessionId, progress, e);
            return false;
        }
    }

    @Override
    public RewardGenerationResult generateReward(String customerId, String sessionId, String videoId, 
                                               Integer progress, double trustScore) {
        try {
            // 1. 获取奖励规则配置
            YouTubeRewardRules rewardRules = getYouTubeRewardRules(videoId);
            if (rewardRules == null) {
                return new RewardGenerationResult(false, "Reward rules not found");
            }

            // 2. 获取进度配置
            YouTubeRewardRules.ProgressRewardConfig progressConfig = rewardRules.getProgressRewardConfig(progress);
            if (progressConfig == null) {
                return new RewardGenerationResult(false, "Progress config not found for progress: " + progress);
            }

            // 3. 检查信任分数要求
            if (trustScore < progressConfig.getMinTrustScore()) {
                return new RewardGenerationResult(false, "Trust score too low: " + trustScore);
            }

            // 4. 生成奖励代码
            String rewardCode = generateRewardCode();
            
            // 5. 计算奖励积分
            Long rewardScore = calculateRewardScore(progressConfig, trustScore);
            
            // 6. 保存奖励记录
            MaizeRecord maizeRecord = new MaizeRecord();
            maizeRecord.setCustomerId(customerId);
            maizeRecord.setSocialEvent("watch");
            maizeRecord.setSocialPlatform("youtube");
            maizeRecord.setSocialContentId(videoId);
            maizeRecord.setMaizeCode(rewardCode);
            maizeRecord.setMaizeLevel(progressConfig.getRewardScoreRange().getRewardLevel());
            maizeRecord.setMaizeScore(rewardScore);
            maizeRecord.setCreateTime(System.currentTimeMillis());
            
            // 扩展字段存储sessionId和progress
            maizeRecord.setId(sessionId + "_" + progress); // 使用复合ID确保唯一性
            
            boolean saved = maizeRecordBuilder.save(maizeRecord);
            if (!saved) {
                return new RewardGenerationResult(false, "Failed to save reward record");
            }

            // 7. 构建返回结果
            RewardGenerationResult result = new RewardGenerationResult(true, "Reward generated successfully");
            result.setRewardCode(rewardCode);
            result.setRewardScore(rewardScore);
            result.setRewardLevel(progressConfig.getRewardScoreRange().getRewardLevel());
            result.setExpirationTime(System.currentTimeMillis() + 
                    progressConfig.getRewardCodeExpirationMinutes() * 60 * 1000L);

            return result;

        } catch (Exception e) {
            log.error("Failed to generate reward for session: {}, progress: {}", sessionId, progress, e);
            return new RewardGenerationResult(false, "Failed to generate reward: " + e.getMessage());
        }
    }

    @Override
    public YouTubeRewardRules getYouTubeRewardRules(String videoId) {
        try {
            log.debug("Getting YouTube reward rules from application.properties for video: {}", videoId);

            // 从配置文件读取奖励规则
            return createYouTubeRewardRulesFromProperties();

        } catch (Exception e) {
            log.error("Failed to get YouTube reward rules from properties for video: {}", videoId, e);
            return createDefaultYouTubeRewardRules();
        }
    }

    /**
     * 从配置属性创建YouTube奖励规则
     */
    private YouTubeRewardRules createYouTubeRewardRulesFromProperties() {
        List<YouTubeRewardRules.ProgressRewardConfig> progressRewards = List.of(
                createProgressRewardConfigFromProperties(1, youTubeRewardProperties.getProgress1()),
                createProgressRewardConfigFromProperties(2, youTubeRewardProperties.getProgress2()),
                createProgressRewardConfigFromProperties(3, youTubeRewardProperties.getProgress3())
        );

        YouTubeRewardRules.GlobalConfig globalConfig = YouTubeRewardRules.GlobalConfig.builder()
                .minSessionDurationSeconds(youTubeRewardProperties.getGlobal().getMinSessionDurationSeconds())
                .maxSessionDurationSeconds(youTubeRewardProperties.getGlobal().getMaxSessionDurationSeconds())
                .fraudDetectionThreshold(youTubeRewardProperties.getGlobal().getFraudDetectionThreshold())
                .enableRealtimeReward(youTubeRewardProperties.getGlobal().getEnableRealtimeReward())
                .heartbeatIntervalSeconds(youTubeRewardProperties.getGlobal().getHeartbeatIntervalSeconds())
                .eventReportIntervalSeconds(youTubeRewardProperties.getGlobal().getEventReportIntervalSeconds())
                .maxConcurrentSessions(youTubeRewardProperties.getGlobal().getMaxConcurrentSessions())
                .build();

        return YouTubeRewardRules.builder()
                .type("YouTube")
                .progressRewards(progressRewards)
                .globalConfig(globalConfig)
                .build();
    }

    /**
     * 从配置属性创建进度奖励配置
     */
    private YouTubeRewardRules.ProgressRewardConfig createProgressRewardConfigFromProperties(
            Integer progress, YouTubeRewardProperties.ProgressConfig config) {

        YouTubeRewardRules.RewardScoreRange scoreRange = YouTubeRewardRules.RewardScoreRange.builder()
                .minScore(config.getMinScore())
                .maxScore(config.getMaxScore())
                .baseScore(config.getBaseScore())
                .rewardLevel(config.getRewardLevel())
                .build();

        return YouTubeRewardRules.ProgressRewardConfig.builder()
                .progress(progress)
                .progressName(getProgressName(progress))
                .minWatchPercentage(config.getMinWatchPercentage())
                .maxWatchPercentage(config.getMaxWatchPercentage())
                .rewardScoreRange(scoreRange)
                .minTrustScore(config.getMinTrustScore())
                .maxDailyRewards(config.getMaxDailyRewards())
                .rewardCodeExpirationMinutes(config.getExpirationMinutes())
                .enabled(config.getEnabled())
                .build();
    }

    /**
     * 获取进度名称
     */
    private String getProgressName(Integer progress) {
        switch (progress) {
            case 1:
                return "初级观看";
            case 2:
                return "中级观看";
            case 3:
                return "高级观看";
            default:
                return "未知进度";
        }
    }

    /**
     * 计算信任分数
     */
    private double calculateTrustScore(List<SessionEvent> events, int videoDurationSeconds) {
        try {
            return trustScoreCalculationService.calculateTrustScore(events, videoDurationSeconds);
        } catch (Exception e) {
            log.error("Failed to calculate trust score", e);
            return 0.5; // 默认中等信任分数
        }
    }

    /**
     * 构建进度信息
     */
    private YouTubeWebSocketMessage.ProgressInfo buildProgressInfo(Integer progress, int effectiveWatchSeconds, 
                                                                  int videoDurationSeconds, double trustScore) {
        double watchPercentage = videoDurationSeconds > 0 ? 
                (double) effectiveWatchSeconds / videoDurationSeconds : 0.0;
        
        boolean rewardEligible = progress != null && trustScore >= 0.3;
        
        String nextRewardRequirement = getNextRewardRequirement(progress, watchPercentage);

        return YouTubeWebSocketMessage.ProgressInfo.builder()
                .progress(progress)
                .effectiveWatchSeconds(effectiveWatchSeconds)
                .videoDurationSeconds(videoDurationSeconds)
                .watchPercentage(watchPercentage)
                .trustScore(trustScore)
                .rewardEligible(rewardEligible)
                .nextRewardRequirement(nextRewardRequirement)
                .build();
    }

    /**
     * 获取下一个奖励要求描述
     */
    private String getNextRewardRequirement(Integer currentProgress, double watchPercentage) {
        if (currentProgress == null) {
            if (watchPercentage < 0.2) {
                return "继续观看至20%可获得进度1奖励";
            }
        } else if (currentProgress == 1) {
            return "继续观看至50%可获得进度2奖励";
        } else if (currentProgress == 2) {
            return "继续观看至80%可获得进度3奖励";
        } else if (currentProgress == 3) {
            return "已完成所有奖励阶段";
        }
        return "继续观看以获得奖励";
    }

    /**
     * 生成奖励代码
     */
    private String generateRewardCode() {
        return base62Encoding.encode(System.currentTimeMillis()) + 
               base62Encoding.encode(random.nextLong() & Long.MAX_VALUE);
    }

    /**
     * 计算奖励积分
     */
    private Long calculateRewardScore(YouTubeRewardRules.ProgressRewardConfig progressConfig, double trustScore) {
        YouTubeRewardRules.RewardScoreRange scoreRange = progressConfig.getRewardScoreRange();
        
        // 基于信任分数调整奖励积分
        double multiplier = Math.min(1.5, Math.max(0.5, trustScore * 1.5));
        long adjustedScore = Math.round(scoreRange.getBaseScore() * multiplier);
        
        // 确保在范围内
        return Math.max(scoreRange.getMinScore(), 
                Math.min(scoreRange.getMaxScore(), adjustedScore));
    }

    /**
     * 创建默认YouTube奖励规则
     */
    private YouTubeRewardRules createDefaultYouTubeRewardRules() {
        List<YouTubeRewardRules.ProgressRewardConfig> progressRewards = List.of(
                YouTubeRewardRules.ProgressRewardConfig.builder()
                        .progress(1)
                        .progressName("初级观看")
                        .minWatchPercentage(0.2)
                        .maxWatchPercentage(0.4)
                        .rewardScoreRange(YouTubeRewardRules.RewardScoreRange.builder()
                                .minScore(50L)
                                .maxScore(100L)
                                .baseScore(75L)
                                .rewardLevel("BRONZE")
                                .build())
                        .minTrustScore(0.3)
                        .maxDailyRewards(10)
                        .rewardCodeExpirationMinutes(30)
                        .enabled(true)
                        .build(),
                YouTubeRewardRules.ProgressRewardConfig.builder()
                        .progress(2)
                        .progressName("中级观看")
                        .minWatchPercentage(0.5)
                        .maxWatchPercentage(0.7)
                        .rewardScoreRange(YouTubeRewardRules.RewardScoreRange.builder()
                                .minScore(100L)
                                .maxScore(200L)
                                .baseScore(150L)
                                .rewardLevel("SILVER")
                                .build())
                        .minTrustScore(0.5)
                        .maxDailyRewards(8)
                        .rewardCodeExpirationMinutes(30)
                        .enabled(true)
                        .build(),
                YouTubeRewardRules.ProgressRewardConfig.builder()
                        .progress(3)
                        .progressName("高级观看")
                        .minWatchPercentage(0.8)
                        .maxWatchPercentage(1.2)
                        .rewardScoreRange(YouTubeRewardRules.RewardScoreRange.builder()
                                .minScore(200L)
                                .maxScore(400L)
                                .baseScore(300L)
                                .rewardLevel("GOLD")
                                .build())
                        .minTrustScore(0.7)
                        .maxDailyRewards(5)
                        .rewardCodeExpirationMinutes(30)
                        .enabled(true)
                        .build()
        );

        return YouTubeRewardRules.builder()
                .progressRewards(progressRewards)
                .build();
    }
}
