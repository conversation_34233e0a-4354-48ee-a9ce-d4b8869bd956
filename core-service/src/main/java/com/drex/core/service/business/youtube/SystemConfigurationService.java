package com.drex.core.service.business.youtube;

import java.util.List;
import java.util.Map;

/**
 * 系统配置服务接口
 * 负责管理YouTube防刷系统的各种配置参数
 */
public interface SystemConfigurationService {

    /**
     * 获取欺诈检测权重配置
     *
     * @return 权重配置映射
     */
    Map<String, Double> getFraudDetectionWeights();

    /**
     * 更新欺诈检测权重配置
     *
     * @param weights 新的权重配置
     * @return 是否更新成功
     */
    boolean updateFraudDetectionWeights(Map<String, Double> weights);

    /**
     * 获取信任分数阈值配置
     *
     * @return 阈值配置
     */
    TrustScoreThresholds getTrustScoreThresholds();

    /**
     * 更新信任分数阈值配置
     *
     * @param thresholds 新的阈值配置
     * @return 是否更新成功
     */
    boolean updateTrustScoreThresholds(TrustScoreThresholds thresholds);

    /**
     * 获取奖励配置
     *
     * @return 奖励配置列表
     */
    List<RewardStageConfiguration> getRewardConfigurations();

    /**
     * 更新奖励配置
     *
     * @param configurations 新的奖励配置
     * @return 是否更新成功
     */
    boolean updateRewardConfigurations(List<RewardStageConfiguration> configurations);

    /**
     * 获取系统限制配置
     *
     * @return 系统限制配置
     */
    SystemLimitsConfiguration getSystemLimits();

    /**
     * 更新系统限制配置
     *
     * @param limits 新的限制配置
     * @return 是否更新成功
     */
    boolean updateSystemLimits(SystemLimitsConfiguration limits);

    /**
     * 获取黑名单配置
     *
     * @return 黑名单配置
     */
    BlacklistConfiguration getBlacklistConfiguration();

    /**
     * 更新黑名单配置
     *
     * @param configuration 新的黑名单配置
     * @return 是否更新成功
     */
    boolean updateBlacklistConfiguration(BlacklistConfiguration configuration);

    /**
     * 获取加密配置
     *
     * @return 加密配置
     */
    EncryptionConfiguration getEncryptionConfiguration();

    /**
     * 更新加密配置
     *
     * @param configuration 新的加密配置
     * @return 是否更新成功
     */
    boolean updateEncryptionConfiguration(EncryptionConfiguration configuration);

    /**
     * 获取监控配置
     *
     * @return 监控配置
     */
    MonitoringConfiguration getMonitoringConfiguration();

    /**
     * 更新监控配置
     *
     * @param configuration 新的监控配置
     * @return 是否更新成功
     */
    boolean updateMonitoringConfiguration(MonitoringConfiguration configuration);

    /**
     * 获取特定配置项
     *
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 更新特定配置项
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否更新成功
     */
    boolean updateConfigValue(String configKey, String configValue);

    /**
     * 获取所有配置
     *
     * @return 所有配置映射
     */
    Map<String, Object> getAllConfigurations();

    /**
     * 重置配置为默认值
     *
     * @param configKeys 要重置的配置键列表，为空则重置所有
     * @return 是否重置成功
     */
    boolean resetToDefaults(List<String> configKeys);

    /**
     * 验证配置的有效性
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 验证结果
     */
    ConfigValidationResult validateConfiguration(String configKey, String configValue);

    /**
     * 信任分数阈值配置
     */
    class TrustScoreThresholds {
        private double rewardEligibilityThreshold;
        private double lowRiskThreshold;
        private double mediumRiskThreshold;
        private double highRiskThreshold;
        private double autoRejectThreshold;

        public TrustScoreThresholds(double rewardEligibilityThreshold, double lowRiskThreshold,
                                  double mediumRiskThreshold, double highRiskThreshold,
                                  double autoRejectThreshold) {
            this.rewardEligibilityThreshold = rewardEligibilityThreshold;
            this.lowRiskThreshold = lowRiskThreshold;
            this.mediumRiskThreshold = mediumRiskThreshold;
            this.highRiskThreshold = highRiskThreshold;
            this.autoRejectThreshold = autoRejectThreshold;
        }

        // Getters and setters
        public double getRewardEligibilityThreshold() { return rewardEligibilityThreshold; }
        public void setRewardEligibilityThreshold(double rewardEligibilityThreshold) { 
            this.rewardEligibilityThreshold = rewardEligibilityThreshold; 
        }
        public double getLowRiskThreshold() { return lowRiskThreshold; }
        public void setLowRiskThreshold(double lowRiskThreshold) { this.lowRiskThreshold = lowRiskThreshold; }
        public double getMediumRiskThreshold() { return mediumRiskThreshold; }
        public void setMediumRiskThreshold(double mediumRiskThreshold) { 
            this.mediumRiskThreshold = mediumRiskThreshold; 
        }
        public double getHighRiskThreshold() { return highRiskThreshold; }
        public void setHighRiskThreshold(double highRiskThreshold) { this.highRiskThreshold = highRiskThreshold; }
        public double getAutoRejectThreshold() { return autoRejectThreshold; }
        public void setAutoRejectThreshold(double autoRejectThreshold) { 
            this.autoRejectThreshold = autoRejectThreshold; 
        }
    }

    /**
     * 奖励阶段配置
     */
    class RewardStageConfiguration {
        private String stageName;
        private String stageCode;
        private double requiredWatchPercentage;
        private String baseRewardAmount;
        private double minTrustScore;
        private int maxDailyRewards;
        private long rewardCodeExpirationMinutes;

        public RewardStageConfiguration(String stageName, String stageCode, double requiredWatchPercentage,
                                      String baseRewardAmount, double minTrustScore) {
            this.stageName = stageName;
            this.stageCode = stageCode;
            this.requiredWatchPercentage = requiredWatchPercentage;
            this.baseRewardAmount = baseRewardAmount;
            this.minTrustScore = minTrustScore;
        }

        // Getters and setters
        public String getStageName() { return stageName; }
        public String getStageCode() { return stageCode; }
        public double getRequiredWatchPercentage() { return requiredWatchPercentage; }
        public void setRequiredWatchPercentage(double requiredWatchPercentage) { 
            this.requiredWatchPercentage = requiredWatchPercentage; 
        }
        public String getBaseRewardAmount() { return baseRewardAmount; }
        public void setBaseRewardAmount(String baseRewardAmount) { this.baseRewardAmount = baseRewardAmount; }
        public double getMinTrustScore() { return minTrustScore; }
        public void setMinTrustScore(double minTrustScore) { this.minTrustScore = minTrustScore; }
        public int getMaxDailyRewards() { return maxDailyRewards; }
        public void setMaxDailyRewards(int maxDailyRewards) { this.maxDailyRewards = maxDailyRewards; }
        public long getRewardCodeExpirationMinutes() { return rewardCodeExpirationMinutes; }
        public void setRewardCodeExpirationMinutes(long rewardCodeExpirationMinutes) { 
            this.rewardCodeExpirationMinutes = rewardCodeExpirationMinutes; 
        }
    }

    /**
     * 系统限制配置
     */
    class SystemLimitsConfiguration {
        private int maxSessionsPerUser;
        private int maxEventsPerSession;
        private int maxSessionDurationHours;
        private int maxReportsPerMinute;
        private int maxConcurrentSessions;
        private long sessionTimeoutMinutes;

        public SystemLimitsConfiguration(int maxSessionsPerUser, int maxEventsPerSession,
                                       int maxSessionDurationHours, int maxReportsPerMinute) {
            this.maxSessionsPerUser = maxSessionsPerUser;
            this.maxEventsPerSession = maxEventsPerSession;
            this.maxSessionDurationHours = maxSessionDurationHours;
            this.maxReportsPerMinute = maxReportsPerMinute;
        }

        // Getters and setters
        public int getMaxSessionsPerUser() { return maxSessionsPerUser; }
        public void setMaxSessionsPerUser(int maxSessionsPerUser) { this.maxSessionsPerUser = maxSessionsPerUser; }
        public int getMaxEventsPerSession() { return maxEventsPerSession; }
        public void setMaxEventsPerSession(int maxEventsPerSession) { 
            this.maxEventsPerSession = maxEventsPerSession; 
        }
        public int getMaxSessionDurationHours() { return maxSessionDurationHours; }
        public void setMaxSessionDurationHours(int maxSessionDurationHours) { 
            this.maxSessionDurationHours = maxSessionDurationHours; 
        }
        public int getMaxReportsPerMinute() { return maxReportsPerMinute; }
        public void setMaxReportsPerMinute(int maxReportsPerMinute) { 
            this.maxReportsPerMinute = maxReportsPerMinute; 
        }
        public int getMaxConcurrentSessions() { return maxConcurrentSessions; }
        public void setMaxConcurrentSessions(int maxConcurrentSessions) { 
            this.maxConcurrentSessions = maxConcurrentSessions; 
        }
        public long getSessionTimeoutMinutes() { return sessionTimeoutMinutes; }
        public void setSessionTimeoutMinutes(long sessionTimeoutMinutes) { 
            this.sessionTimeoutMinutes = sessionTimeoutMinutes; 
        }
    }

    /**
     * 黑名单配置
     */
    class BlacklistConfiguration {
        private boolean enableUserBlacklist;
        private boolean enableIpBlacklist;
        private long blacklistCacheExpirationHours;
        private int autoBlacklistThreshold;
        private boolean enableAutoBlacklist;

        public BlacklistConfiguration(boolean enableUserBlacklist, boolean enableIpBlacklist,
                                    long blacklistCacheExpirationHours) {
            this.enableUserBlacklist = enableUserBlacklist;
            this.enableIpBlacklist = enableIpBlacklist;
            this.blacklistCacheExpirationHours = blacklistCacheExpirationHours;
        }

        // Getters and setters
        public boolean isEnableUserBlacklist() { return enableUserBlacklist; }
        public void setEnableUserBlacklist(boolean enableUserBlacklist) { 
            this.enableUserBlacklist = enableUserBlacklist; 
        }
        public boolean isEnableIpBlacklist() { return enableIpBlacklist; }
        public void setEnableIpBlacklist(boolean enableIpBlacklist) { this.enableIpBlacklist = enableIpBlacklist; }
        public long getBlacklistCacheExpirationHours() { return blacklistCacheExpirationHours; }
        public void setBlacklistCacheExpirationHours(long blacklistCacheExpirationHours) { 
            this.blacklistCacheExpirationHours = blacklistCacheExpirationHours; 
        }
        public int getAutoBlacklistThreshold() { return autoBlacklistThreshold; }
        public void setAutoBlacklistThreshold(int autoBlacklistThreshold) { 
            this.autoBlacklistThreshold = autoBlacklistThreshold; 
        }
        public boolean isEnableAutoBlacklist() { return enableAutoBlacklist; }
        public void setEnableAutoBlacklist(boolean enableAutoBlacklist) { 
            this.enableAutoBlacklist = enableAutoBlacklist; 
        }
    }

    /**
     * 加密配置
     */
    class EncryptionConfiguration {
        private boolean enableEncryption;
        private String encryptionAlgorithm;
        private int keyLength;
        private boolean enableSignatureVerification;
        private String signatureAlgorithm;

        public EncryptionConfiguration(boolean enableEncryption, String encryptionAlgorithm,
                                     int keyLength, boolean enableSignatureVerification) {
            this.enableEncryption = enableEncryption;
            this.encryptionAlgorithm = encryptionAlgorithm;
            this.keyLength = keyLength;
            this.enableSignatureVerification = enableSignatureVerification;
        }

        // Getters and setters
        public boolean isEnableEncryption() { return enableEncryption; }
        public void setEnableEncryption(boolean enableEncryption) { this.enableEncryption = enableEncryption; }
        public String getEncryptionAlgorithm() { return encryptionAlgorithm; }
        public void setEncryptionAlgorithm(String encryptionAlgorithm) { 
            this.encryptionAlgorithm = encryptionAlgorithm; 
        }
        public int getKeyLength() { return keyLength; }
        public void setKeyLength(int keyLength) { this.keyLength = keyLength; }
        public boolean isEnableSignatureVerification() { return enableSignatureVerification; }
        public void setEnableSignatureVerification(boolean enableSignatureVerification) { 
            this.enableSignatureVerification = enableSignatureVerification; 
        }
        public String getSignatureAlgorithm() { return signatureAlgorithm; }
        public void setSignatureAlgorithm(String signatureAlgorithm) { 
            this.signatureAlgorithm = signatureAlgorithm; 
        }
    }

    /**
     * 监控配置
     */
    class MonitoringConfiguration {
        private boolean enableRealTimeMonitoring;
        private int alertThreshold;
        private long statisticsRetentionDays;
        private boolean enablePerformanceMetrics;
        private int metricsCollectionIntervalSeconds;

        public MonitoringConfiguration(boolean enableRealTimeMonitoring, int alertThreshold,
                                     long statisticsRetentionDays) {
            this.enableRealTimeMonitoring = enableRealTimeMonitoring;
            this.alertThreshold = alertThreshold;
            this.statisticsRetentionDays = statisticsRetentionDays;
        }

        // Getters and setters
        public boolean isEnableRealTimeMonitoring() { return enableRealTimeMonitoring; }
        public void setEnableRealTimeMonitoring(boolean enableRealTimeMonitoring) { 
            this.enableRealTimeMonitoring = enableRealTimeMonitoring; 
        }
        public int getAlertThreshold() { return alertThreshold; }
        public void setAlertThreshold(int alertThreshold) { this.alertThreshold = alertThreshold; }
        public long getStatisticsRetentionDays() { return statisticsRetentionDays; }
        public void setStatisticsRetentionDays(long statisticsRetentionDays) { 
            this.statisticsRetentionDays = statisticsRetentionDays; 
        }
        public boolean isEnablePerformanceMetrics() { return enablePerformanceMetrics; }
        public void setEnablePerformanceMetrics(boolean enablePerformanceMetrics) { 
            this.enablePerformanceMetrics = enablePerformanceMetrics; 
        }
        public int getMetricsCollectionIntervalSeconds() { return metricsCollectionIntervalSeconds; }
        public void setMetricsCollectionIntervalSeconds(int metricsCollectionIntervalSeconds) { 
            this.metricsCollectionIntervalSeconds = metricsCollectionIntervalSeconds; 
        }
    }

    /**
     * 配置验证结果
     */
    class ConfigValidationResult {
        private boolean valid;
        private String errorMessage;
        private List<String> warnings;
        private Object suggestedValue;

        public ConfigValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        // Getters and setters
        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings; }
        public Object getSuggestedValue() { return suggestedValue; }
        public void setSuggestedValue(Object suggestedValue) { this.suggestedValue = suggestedValue; }
    }
}
