package com.drex.core.service.business.rexy.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.*;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.model.RexyBusinessCode;
import com.drex.core.service.business.rexy.Base62Encoding;
import com.drex.core.service.business.rexy.MaizeService;
import com.drex.core.service.business.rexy.RexyBasketService;
import com.drex.core.service.mapperstruct.MaizeMapperStruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class MaizeServiceImpl implements MaizeService {

    @Resource
    private SocialCheckManager socialCheckManager;
    @Resource
    private MaizeRecordBuilder maizeRecordBuilder;
    @Resource
    private MaizeMapperStruct maizeMapperStruct;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private IdUtils idUtils;
    @Resource
    private RexyBasketService rexyBasketService;

    private static final String COLLECT_MAIZE = "collect_maize";

    @Override
    public MaizeDTO generateMaize(GenerateMaizeRequest request) {
        boolean check = socialCheckManager.check(request);
        if(!check){
            return null;
        }
        String code = generateCode(request.getSocialPlatform());
        MaizeDTO maizeDTO = MaizeDTO.builder()
                .socialPlatform(request.getSocialPlatform())
                .socialEvent(request.getSocialEvent())
                .code(code)
                .level(SocialConstant.MaizeLevelEnum.GOLD.name())
                .score(SocialConstant.MaizeLevelEnum.GOLD.getScore())
                .build();
        MaizeRecord maizeRecord = maizeMapperStruct.toMaizeRecord(maizeDTO, request);
        maizeRecordBuilder.save(maizeRecord);
        redisTemplate.opsForHash().put("maize:" + request.getCustomerId(), request.getSocialPlatform().name() + ":" + request.getSocialEvent().name(), JSON.toJSONString(maizeDTO));
        return maizeDTO;
    }

    @Override
    public List<MaizeDTO> lastMaize(LastMaizeRequest request) {
        List<MaizeDTO> maizeDTOS = new ArrayList<>();
        SocialConstant.EventEnum[] values = SocialConstant.EventEnum.values();
        for(SocialConstant.EventEnum eventEnum : values){
            Object object = redisTemplate.opsForHash().get("maize:" + request.getCustomerId(), request.getSocialPlatform().name() + ":" + eventEnum.name());
            if(object != null){
                MaizeDTO maizeDTO = JSON.parseObject(object.toString(), MaizeDTO.class);
                maizeDTOS.add(maizeDTO);
            }
        }
        return maizeDTOS;
    }

    @Override
    public List<MaizeDTO> collectMaize(CollectMaizeRequest request) throws CoreException {
        String decode = Base62Encoding.decode(request.getMaizeCode());
        SocialConstant.PlatformEnum platformEnum = SocialConstant.PlatformEnum.X;
        if (decode.charAt(1) == '2') {
            platformEnum = SocialConstant.PlatformEnum.YouTube;
        }
        SocialConstant.EventEnum[] values = SocialConstant.EventEnum.values();
        List<MaizeDTO> list = new ArrayList<>();
        for(SocialConstant.EventEnum eventEnum : values){
            String hashKey = platformEnum + ":" + eventEnum.name();
            Object object = redisTemplate.opsForHash().get("maize:" + request.getCustomerId(), hashKey);
            if(object != null){
                MaizeDTO maizeDTO = JSON.parseObject(object.toString(), MaizeDTO.class);
                if(maizeDTO.getCode().equals(request.getMaizeCode())){
                    //玉米转玉米粒
                    OperateMaizeKernelRequest build = OperateMaizeKernelRequest.builder()
                            .customerId(request.getCustomerId())
                            .amount(BigDecimal.valueOf(maizeDTO.getScore()))
                            .basketType(RexyConstant.RexyBasketsTypeEnum.normal)
                            .operateType(RexyConstant.OperateTypeEnum.collect)
                            .businessCode(RexyBusinessCode.COLLECT_MAIZE.getCode())
                            .businessId(decode)
                            .build();
                    rexyBasketService.collectMaizeKernel(build);
                    redisTemplate.opsForHash().delete("maize:" + request.getCustomerId(), hashKey);
                    list.add(maizeDTO);
                }
            }
        }
        return list;
    }

    private String generateCode(SocialConstant.PlatformEnum socialPlatformEnum) {
        String id = idUtils.nextId();
        if(SocialConstant.PlatformEnum.X == socialPlatformEnum){
            id = "21"+id;
        }else if(SocialConstant.PlatformEnum.YouTube == socialPlatformEnum){
            id = "22"+id;
        }
        String code = Base62Encoding.encode(id);
        log.info("generateCode id:{},{}", id, code);
        return code;
    }
}
