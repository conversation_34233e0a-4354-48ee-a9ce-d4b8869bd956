package com.drex.core.service.cache.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.service.cache.SessionEventCacheService;
import com.drex.core.service.cache.model.SessionEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 会话事件缓存服务实现
 */
@Slf4j
@Service
public class SessionEventCacheServiceImpl implements SessionEventCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private static final String SESSION_EVENT_KEY_PREFIX = "session_events:";
    private static final String SESSION_EVENT_PATTERN = SESSION_EVENT_KEY_PREFIX + "*";
    private static final long DEFAULT_EXPIRE_SECONDS = 24 * 60 * 60; // 24小时

    /**
     * 生成Redis key
     */
    private String getSessionEventKey(String sessionId) {
        return SESSION_EVENT_KEY_PREFIX + sessionId;
    }

    @Override
    public Boolean addEvent(String sessionId, SessionEvent event) {
        try {
            String key = getSessionEventKey(sessionId);
            if (event.getServerTimestamp() == null) {
                event.setServerTimestamp(System.currentTimeMillis());
            }
            if (event.getCreated() == null) {
                event.setCreated(System.currentTimeMillis());
            }
            
            // 使用List结构存储事件，按时间顺序
            String eventJson = JSON.toJSONString(event);
            redisTemplate.opsForList().rightPush(key, eventJson);
            
            // 设置过期时间
            redisTemplate.expire(key, DEFAULT_EXPIRE_SECONDS, TimeUnit.SECONDS);
            
            return true;
        } catch (Exception e) {
            log.error("Failed to add event to session {}: {}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean addEvents(String sessionId, List<SessionEvent> events) {
        try {
            String key = getSessionEventKey(sessionId);
            long currentTime = System.currentTimeMillis();
            
            List<String> eventJsonList = events.stream()
                    .peek(event -> {
                        if (event.getServerTimestamp() == null) {
                            event.setServerTimestamp(currentTime);
                        }
                        if (event.getCreated() == null) {
                            event.setCreated(currentTime);
                        }
                    })
                    .map(JSON::toJSONString)
                    .collect(Collectors.toList());
            
            redisTemplate.opsForList().rightPushAll(key, eventJsonList.toArray());
            
            // 设置过期时间
            redisTemplate.expire(key, DEFAULT_EXPIRE_SECONDS, TimeUnit.SECONDS);
            
            return true;
        } catch (Exception e) {
            log.error("Failed to add events to session {}: {}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<SessionEvent> getEvents(String sessionId) {
        try {
            String key = getSessionEventKey(sessionId);
            List<Object> eventJsonList = redisTemplate.opsForList().range(key, 0, -1);
            
            if (eventJsonList == null || eventJsonList.isEmpty()) {
                return new ArrayList<>();
            }
            
            return eventJsonList.stream()
                    .map(obj -> JSON.parseObject(obj.toString(), SessionEvent.class))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get events for session {}: {}", sessionId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SessionEvent> getEventsByType(String sessionId, String eventType) {
        try {
            List<SessionEvent> allEvents = getEvents(sessionId);
            return allEvents.stream()
                    .filter(event -> eventType.equals(event.getEventType()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get events by type {} for session {}: {}", eventType, sessionId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Long getEventCount(String sessionId) {
        try {
            String key = getSessionEventKey(sessionId);
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            log.error("Failed to get event count for session {}: {}", sessionId, e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Boolean deleteEvents(String sessionId) {
        try {
            String key = getSessionEventKey(sessionId);
            return redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("Failed to delete events for session {}: {}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean setExpire(String sessionId, long expireSeconds) {
        try {
            String key = getSessionEventKey(sessionId);
            return redisTemplate.expire(key, expireSeconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Failed to set expire for session {}: {}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean exists(String sessionId) {
        try {
            String key = getSessionEventKey(sessionId);
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("Failed to check existence for session {}: {}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Long getExpire(String sessionId) {
        try {
            String key = getSessionEventKey(sessionId);
            return redisTemplate.getExpire(key, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Failed to get expire for session {}: {}", sessionId, e.getMessage(), e);
            return -2L;
        }
    }

    @Override
    public Long cleanExpiredEvents() {
        try {
            Set<String> keys = redisTemplate.keys(SESSION_EVENT_PATTERN);
            if (keys == null || keys.isEmpty()) {
                return 0L;
            }
            
            long cleanedCount = 0;
            for (String key : keys) {
                Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
                if (expire != null && expire <= 0) {
                    if (Boolean.TRUE.equals(redisTemplate.delete(key))) {
                        cleanedCount++;
                    }
                }
            }
            
            log.info("Cleaned {} expired session event keys", cleanedCount);
            return cleanedCount;
        } catch (Exception e) {
            log.error("Failed to clean expired events: {}", e.getMessage(), e);
            return 0L;
        }
    }
}
