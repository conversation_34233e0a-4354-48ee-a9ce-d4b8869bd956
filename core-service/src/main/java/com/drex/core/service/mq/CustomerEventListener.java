package com.drex.core.service.mq;

import com.alibaba.fastjson2.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.drex.activity.task.api.RemoteTaskService;
import com.drex.activity.task.model.response.TaskConfigResponse;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.OperateMaizeKernelRequest;
import com.drex.core.model.RexyBusinessCode;
import com.drex.core.service.business.rexy.RexyBasketService;
import com.drex.customer.api.request.CustomerReferralDTO;
import com.drex.customer.api.response.CustomerDTO;
import com.drex.core.service.business.rexy.CustomerRexyService;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.framework.ons.OnsProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class CustomerEventListener implements OnsMessageListener {

    @Resource
    private OnsProperties onsProperties;
    @Resource
    private CustomerRexyService customerRexyService;
    @Resource
    private RexyBasketService rexyBasketService;
    @DubboReference
    private RemoteTaskService remoteTaskService;

    private static final String TOPIC = "T_DREX_CUSTOMER_REGISTER";

    @Override
    public String topic() {
        return TOPIC + onsProperties.getEnv();
    }

    @Override
    public Action doConsume(Message message, ConsumeContext context) {
        log.info("RexyInitListener: {},{},{}", message.getMsgID(), message.getTopic(), new String(message.getBody()));
        Map<String, String> map = JSON.parseObject(message.getBody(), HashMap.class);
        String event = map.get("event");
        switch (event){
            case "register":
                CustomerDTO customerDTO = JSON.parseObject(map.get("data"), CustomerDTO.class);
                if(customerDTO == null){
                    log.error("RexyInitListener: customerDTO is null,{}", message);
                    return Action.CommitMessage;
                }
                customerRexyService.initCustomerRexy(customerDTO);
                return Action.CommitMessage;
            case "bindInviteCode":
                CustomerReferralDTO customerReferralDTO = JSON.parseObject(map.get("data"), CustomerReferralDTO.class);
                Response<TaskConfigResponse> inviteRegister = remoteTaskService.getTaskByCode("trex", "invite_register", null);
                if(!inviteRegister.isSuccess()){
                    return Action.CommitMessage;
                }
                OperateMaizeKernelRequest request = OperateMaizeKernelRequest.builder()
                        .customerId(customerReferralDTO.getReferrerId())
                        .businessCode(RexyBusinessCode.INVITE_REGISTER.getCode())
                        .businessId(customerReferralDTO.getCustomerId())
                        .operateType(RexyConstant.OperateTypeEnum.collect)
                        .basketType(RexyConstant.RexyBasketsTypeEnum.invite)
                        .amount(inviteRegister.getData().getShowReward())
                        .build();
                try {
                    log.info("RexyInitListener: collectMaizeKernel,{}", JSON.toJSONString(request));
                    rexyBasketService.collectMaizeKernel(request);
                } catch (CoreException e) {
                    log.error("RexyInitListener: collectMaizeKernel error,{}", message);
                    return Action.ReconsumeLater;
                }
                return Action.CommitMessage;
        }
        return Action.CommitMessage;
    }
}
