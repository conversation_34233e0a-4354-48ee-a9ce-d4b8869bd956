package com.drex.core.service.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 异步处理工具类
 * 用于编排异步线程处理YouTube防刷系统的各种任务
 */
@Slf4j
@Component
public class AsyncTool {

    // 事件处理线程池
    private final ExecutorService eventProcessingExecutor;
    
    // 奖励计算线程池
    private final ExecutorService rewardCalculationExecutor;
    
    // 欺诈检测线程池
    private final ExecutorService fraudDetectionExecutor;

    public AsyncTool() {
        // 事件处理线程池：IO密集型任务
        this.eventProcessingExecutor = new ThreadPoolExecutor(
                4, 16, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(r, "event-processing-" + System.currentTimeMillis()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 奖励计算线程池：CPU密集型任务
        this.rewardCalculationExecutor = new ThreadPoolExecutor(
                2, 8, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(500),
                r -> new Thread(r, "reward-calculation-" + System.currentTimeMillis()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 欺诈检测线程池：CPU密集型任务
        this.fraudDetectionExecutor = new ThreadPoolExecutor(
                2, 6, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(300),
                r -> new Thread(r, "fraud-detection-" + System.currentTimeMillis()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 异步执行事件处理任务
     */
    public <T> CompletableFuture<T> executeEventProcessing(Supplier<T> task) {
        return CompletableFuture.supplyAsync(task, eventProcessingExecutor)
                .exceptionally(throwable -> {
                    log.error("Event processing task failed", throwable);
                    return null;
                });
    }

    /**
     * 异步执行奖励计算任务
     */
    public <T> CompletableFuture<T> executeRewardCalculation(Supplier<T> task) {
        return CompletableFuture.supplyAsync(task, rewardCalculationExecutor)
                .exceptionally(throwable -> {
                    log.error("Reward calculation task failed", throwable);
                    return null;
                });
    }

    /**
     * 异步执行欺诈检测任务
     */
    public <T> CompletableFuture<T> executeFraudDetection(Supplier<T> task) {
        return CompletableFuture.supplyAsync(task, fraudDetectionExecutor)
                .exceptionally(throwable -> {
                    log.error("Fraud detection task failed", throwable);
                    return null;
                });
    }

    /**
     * 并行执行多个任务并等待所有完成
     */
    public <T> CompletableFuture<Void> executeAllTasks(CompletableFuture<T>... futures) {
        return CompletableFuture.allOf(futures);
    }

    /**
     * 并行执行多个任务并返回第一个完成的结果
     */
    public <T> CompletableFuture<Object> executeAnyTask(CompletableFuture<T>... futures) {
        return CompletableFuture.anyOf(futures);
    }

    /**
     * 带超时的异步执行
     */
    public <T> CompletableFuture<T> executeWithTimeout(Supplier<T> task, long timeout, TimeUnit unit) {
        CompletableFuture<T> future = CompletableFuture.supplyAsync(task, eventProcessingExecutor);
        
        return future.completeOnTimeout(null, timeout, unit)
                .exceptionally(throwable -> {
                    log.error("Task execution failed or timed out", throwable);
                    return null;
                });
    }

    /**
     * 编排复杂的异步任务流
     * 用于YouTube防刷系统的完整处理流程
     */
    public static class TaskOrchestrator {
        private final AsyncTool asyncTool;

        public TaskOrchestrator(AsyncTool asyncTool) {
            this.asyncTool = asyncTool;
        }

        /**
         * 编排YouTube事件处理的完整流程
         */
        public <T> CompletableFuture<YouTubeProcessingResult> orchestrateYouTubeProcessing(
                Supplier<T> eventProcessingTask,
                Supplier<Double> fraudDetectionTask,
                Supplier<Object> rewardCalculationTask) {

            // 1. 并行执行事件处理和欺诈检测
            CompletableFuture<T> eventFuture = asyncTool.executeEventProcessing(eventProcessingTask);
            CompletableFuture<Double> fraudFuture = asyncTool.executeFraudDetection(fraudDetectionTask);

            // 2. 等待前两个任务完成后执行奖励计算
            return eventFuture.thenCombine(fraudFuture, (eventResult, fraudScore) -> {
                log.debug("Event processing and fraud detection completed, fraud score: {}", fraudScore);
                
                // 如果欺诈分数过高，跳过奖励计算
                if (fraudScore != null && fraudScore > 0.7) {
                    log.warn("High fraud score detected: {}, skipping reward calculation", fraudScore);
                    return new YouTubeProcessingResult(eventResult, fraudScore, null, false);
                }
                
                // 执行奖励计算
                try {
                    Object rewardResult = rewardCalculationTask.get();
                    return new YouTubeProcessingResult(eventResult, fraudScore, rewardResult, true);
                } catch (Exception e) {
                    log.error("Reward calculation failed", e);
                    return new YouTubeProcessingResult(eventResult, fraudScore, null, false);
                }
            }).exceptionally(throwable -> {
                log.error("YouTube processing orchestration failed", throwable);
                return new YouTubeProcessingResult(null, null, null, false);
            });
        }

        /**
         * 批量处理多个会话的事件
         */
        public CompletableFuture<Void> processBatchSessions(java.util.List<Supplier<Void>> sessionTasks) {
            CompletableFuture<Void>[] futures = sessionTasks.stream()
                    .map(task -> asyncTool.executeEventProcessing(task))
                    .toArray(CompletableFuture[]::new);
            
            return CompletableFuture.allOf(futures);
        }
    }

    /**
     * YouTube处理结果
     */
    public static class YouTubeProcessingResult {
        private final Object eventResult;
        private final Double fraudScore;
        private final Object rewardResult;
        private final boolean success;

        public YouTubeProcessingResult(Object eventResult, Double fraudScore, Object rewardResult, boolean success) {
            this.eventResult = eventResult;
            this.fraudScore = fraudScore;
            this.rewardResult = rewardResult;
            this.success = success;
        }

        public Object getEventResult() { return eventResult; }
        public Double getFraudScore() { return fraudScore; }
        public Object getRewardResult() { return rewardResult; }
        public boolean isSuccess() { return success; }
    }

    /**
     * 创建任务编排器
     */
    public TaskOrchestrator createOrchestrator() {
        return new TaskOrchestrator(this);
    }

    /**
     * 关闭线程池
     */
    public void shutdown() {
        log.info("Shutting down AsyncTool thread pools");
        
        shutdownExecutor(eventProcessingExecutor, "EventProcessing");
        shutdownExecutor(rewardCalculationExecutor, "RewardCalculation");
        shutdownExecutor(fraudDetectionExecutor, "FraudDetection");
    }

    private void shutdownExecutor(ExecutorService executor, String name) {
        try {
            executor.shutdown();
            if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("{} executor did not terminate gracefully, forcing shutdown", name);
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            log.error("Interrupted while shutting down {} executor", name, e);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 获取线程池状态信息
     */
    public String getThreadPoolStatus() {
        StringBuilder status = new StringBuilder();
        status.append("AsyncTool Thread Pool Status:\n");
        
        appendExecutorStatus(status, "EventProcessing", eventProcessingExecutor);
        appendExecutorStatus(status, "RewardCalculation", rewardCalculationExecutor);
        appendExecutorStatus(status, "FraudDetection", fraudDetectionExecutor);
        
        return status.toString();
    }

    private void appendExecutorStatus(StringBuilder status, String name, ExecutorService executor) {
        if (executor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
            status.append(String.format("%s: Active=%d, Pool=%d, Queue=%d, Completed=%d\n",
                    name, tpe.getActiveCount(), tpe.getPoolSize(), 
                    tpe.getQueue().size(), tpe.getCompletedTaskCount()));
        }
    }
}
