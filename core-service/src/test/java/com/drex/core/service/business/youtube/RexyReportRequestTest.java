package com.drex.core.service.business.youtube;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.request.RexyReportRequest;
import com.drex.core.service.business.youtube.impl.DataValidationServiceImpl;
import com.drex.core.service.business.youtube.util.EventDataProcessor;
import com.drex.core.service.cache.model.SessionEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RexyReportRequest新事件类型测试
 */
@ExtendWith(MockitoExtension.class)
public class RexyReportRequestTest {

    @InjectMocks
    private DataValidationServiceImpl dataValidationService;

    @Mock
    private EventDataProcessor eventDataProcessor;

    private RexyReportRequest testRequest;

    @BeforeEach
    void setUp() {
        testRequest = createTestRexyReportRequest();
    }

    @Test
    void testRexyReportRequestStructure() {
        // 测试基本字段
        assertNotNull(testRequest.getSessionId());
        assertNotNull(testRequest.getCustomerId());
        assertNotNull(testRequest.getDeviceFinger());
        assertNotNull(testRequest.getClientTimestamp());
        assertNotNull(testRequest.getEvents());
        assertEquals(6, testRequest.getEvents().size());
    }

    @Test
    void testPlayingEventData() {
        RexyReportRequest.EventData playingEvent = testRequest.getEvents().get(0);
        assertEquals("PLAYING", playingEvent.getEventType());
        assertNotNull(playingEvent.getDetails());
        assertNotNull(playingEvent.getDetails().getPlayingData());
        
        RexyReportRequest.PlayingEventData playingData = playingEvent.getDetails().getPlayingData();
        assertEquals("PLAYING", playingData.getNewState());
        assertEquals(120.5, playingData.getCurrentTime());
        assertEquals(1.0, playingData.getPlaybackRate());
    }

    @Test
    void testPausedEventData() {
        RexyReportRequest.EventData pausedEvent = testRequest.getEvents().get(1);
        assertEquals("PAUSED", pausedEvent.getEventType());
        assertNotNull(pausedEvent.getDetails());
        assertNotNull(pausedEvent.getDetails().getPausedData());
        
        RexyReportRequest.PausedEventData pausedData = pausedEvent.getDetails().getPausedData();
        assertEquals("PAUSED", pausedData.getNewState());
        assertEquals(125.0, pausedData.getCurrentTime());
    }

    @Test
    void testSeekEventData() {
        RexyReportRequest.EventData seekEvent = testRequest.getEvents().get(2);
        assertEquals("SEEK", seekEvent.getEventType());
        assertNotNull(seekEvent.getDetails());
        assertNotNull(seekEvent.getDetails().getSeekData());
        
        RexyReportRequest.SeekEventData seekData = seekEvent.getDetails().getSeekData();
        assertEquals(150.0, seekData.getCurrentTime());
        assertEquals(125.0, seekData.getPreviousTime());
    }

    @Test
    void testFocusLostEventData() {
        RexyReportRequest.EventData focusLostEvent = testRequest.getEvents().get(3);
        assertEquals("FOCUS_LOST", focusLostEvent.getEventType());
        assertNotNull(focusLostEvent.getDetails());
        assertNotNull(focusLostEvent.getDetails().getFocusLostData());
        
        RexyReportRequest.FocusLostEventData focusLostData = focusLostEvent.getDetails().getFocusLostData();
        assertEquals(false, focusLostData.getTabActive());
        assertEquals(true, focusLostData.getWindowFocused());
    }

    @Test
    void testFocusGainedEventData() {
        RexyReportRequest.EventData focusGainedEvent = testRequest.getEvents().get(4);
        assertEquals("FOCUS_GAINED", focusGainedEvent.getEventType());
        assertNotNull(focusGainedEvent.getDetails());
        assertNotNull(focusGainedEvent.getDetails().getFocusGainedData());
        
        RexyReportRequest.FocusGainedEventData focusGainedData = focusGainedEvent.getDetails().getFocusGainedData();
        assertEquals(true, focusGainedData.getTabActive());
        assertEquals(true, focusGainedData.getWindowFocused());
    }

    @Test
    void testUserStateEventData() {
        RexyReportRequest.EventData userStateEvent = testRequest.getEvents().get(5);
        assertEquals("USER_STATE", userStateEvent.getEventType());
        assertNotNull(userStateEvent.getDetails());
        assertNotNull(userStateEvent.getDetails().getUserStateData());
        
        RexyReportRequest.UserStateEventData userStateData = userStateEvent.getDetails().getUserStateData();
        assertEquals("ACTIVE", userStateData.getState());
    }

    @Test
    void testEventDataValidation() {
        DataValidationService.ValidationResult result = dataValidationService.validateReportRequest(testRequest);
        // 注意：由于我们没有设置encryptedData和signature，验证会失败
        // 这里主要测试事件结构的验证逻辑
        assertNotNull(result);
    }

    @Test
    void testJsonSerialization() {
        // 测试JSON序列化和反序列化
        String json = JSON.toJSONString(testRequest);
        assertNotNull(json);
        assertTrue(json.contains("PLAYING"));
        assertTrue(json.contains("PAUSED"));
        assertTrue(json.contains("SEEK"));
        assertTrue(json.contains("FOCUS_LOST"));
        assertTrue(json.contains("FOCUS_GAINED"));
        assertTrue(json.contains("USER_STATE"));
        
        // 反序列化测试
        RexyReportRequest deserializedRequest = JSON.parseObject(json, RexyReportRequest.class);
        assertNotNull(deserializedRequest);
        assertEquals(testRequest.getSessionId(), deserializedRequest.getSessionId());
        assertEquals(testRequest.getEvents().size(), deserializedRequest.getEvents().size());
    }

    /**
     * 创建测试用的RexyReportRequest
     */
    private RexyReportRequest createTestRexyReportRequest() {
        // 创建6种不同类型的事件
        List<RexyReportRequest.EventData> events = Arrays.asList(
                createPlayingEvent(),
                createPausedEvent(),
                createSeekEvent(),
                createFocusLostEvent(),
                createFocusGainedEvent(),
                createUserStateEvent()
        );

        return RexyReportRequest.builder()
                .sessionId("test-session-123")
                .customerId("test-customer-456")
                .deviceFinger("test-device-fingerprint-789")
                .clientTimestamp(System.currentTimeMillis())
                .encryptedData("encrypted-test-data")
                .signature("test-signature")
                .events(events)
                .build();
    }

    private RexyReportRequest.EventData createPlayingEvent() {
        RexyReportRequest.PlayingEventData playingData = RexyReportRequest.PlayingEventData.builder()
                .newState("PLAYING")
                .currentTime(120.5)
                .playbackRate(1.0)
                .build();

        RexyReportRequest.EventDetails details = RexyReportRequest.EventDetails.builder()
                .playingData(playingData)
                .build();

        return RexyReportRequest.EventData.builder()
                .eventId("event-1")
                .eventType("PLAYING")
                .timestamp(System.currentTimeMillis())
                .sequence(1)
                .details(details)
                .build();
    }

    private RexyReportRequest.EventData createPausedEvent() {
        RexyReportRequest.PausedEventData pausedData = RexyReportRequest.PausedEventData.builder()
                .newState("PAUSED")
                .currentTime(125.0)
                .build();

        RexyReportRequest.EventDetails details = RexyReportRequest.EventDetails.builder()
                .pausedData(pausedData)
                .build();

        return RexyReportRequest.EventData.builder()
                .eventId("event-2")
                .eventType("PAUSED")
                .timestamp(System.currentTimeMillis())
                .sequence(2)
                .details(details)
                .build();
    }

    private RexyReportRequest.EventData createSeekEvent() {
        RexyReportRequest.SeekEventData seekData = RexyReportRequest.SeekEventData.builder()
                .currentTime(150.0)
                .previousTime(125.0)
                .build();

        RexyReportRequest.EventDetails details = RexyReportRequest.EventDetails.builder()
                .seekData(seekData)
                .build();

        return RexyReportRequest.EventData.builder()
                .eventId("event-3")
                .eventType("SEEK")
                .timestamp(System.currentTimeMillis())
                .sequence(3)
                .details(details)
                .build();
    }

    private RexyReportRequest.EventData createFocusLostEvent() {
        RexyReportRequest.FocusLostEventData focusLostData = RexyReportRequest.FocusLostEventData.builder()
                .tabActive(false)
                .windowFocused(true)
                .build();

        RexyReportRequest.EventDetails details = RexyReportRequest.EventDetails.builder()
                .focusLostData(focusLostData)
                .build();

        return RexyReportRequest.EventData.builder()
                .eventId("event-4")
                .eventType("FOCUS_LOST")
                .timestamp(System.currentTimeMillis())
                .sequence(4)
                .details(details)
                .build();
    }

    private RexyReportRequest.EventData createFocusGainedEvent() {
        RexyReportRequest.FocusGainedEventData focusGainedData = RexyReportRequest.FocusGainedEventData.builder()
                .tabActive(true)
                .windowFocused(true)
                .build();

        RexyReportRequest.EventDetails details = RexyReportRequest.EventDetails.builder()
                .focusGainedData(focusGainedData)
                .build();

        return RexyReportRequest.EventData.builder()
                .eventId("event-5")
                .eventType("FOCUS_GAINED")
                .timestamp(System.currentTimeMillis())
                .sequence(5)
                .details(details)
                .build();
    }

    private RexyReportRequest.EventData createUserStateEvent() {
        RexyReportRequest.UserStateEventData userStateData = RexyReportRequest.UserStateEventData.builder()
                .state("ACTIVE")
                .build();

        RexyReportRequest.EventDetails details = RexyReportRequest.EventDetails.builder()
                .userStateData(userStateData)
                .build();

        return RexyReportRequest.EventData.builder()
                .eventId("event-6")
                .eventType("USER_STATE")
                .timestamp(System.currentTimeMillis())
                .sequence(6)
                .details(details)
                .build();
    }
}
