package com.drex.core.service.business.youtube;

import com.drex.core.api.rules.youtube.YouTubeRewardRules;
import com.drex.core.dal.tablestore.builder.InformationBuilder;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.business.rexy.Base62Encoding;
import com.drex.core.service.business.youtube.impl.RealtimeRewardServiceImpl;
import com.drex.core.service.cache.model.SessionEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 实时奖励服务测试
 */
@ExtendWith(MockitoExtension.class)
public class RealtimeRewardServiceTest {

    @InjectMocks
    private RealtimeRewardServiceImpl realtimeRewardService;

    @Mock
    private PlaybackAnalysisService playbackAnalysisService;

    @Mock
    private TrustScoreCalculationService trustScoreCalculationService;

    @Mock
    private InformationBuilder informationBuilder;

    @Mock
    private MaizeRecordBuilder maizeRecordBuilder;

    @Mock
    private Base62Encoding base62Encoding;

    @Mock
    private AsyncTool asyncTool;

    private List<SessionEvent> testEvents;
    private String testSessionId = "test-session-123";
    private String testCustomerId = "test-customer-456";
    private String testVideoId = "test-video-789";

    @BeforeEach
    void setUp() {
        testEvents = createTestSessionEvents();
    }

    @Test
    void testCalculateProgress() {
        // 测试进度1：20%-40%
        Integer progress1 = realtimeRewardService.calculateProgress(60, 300); // 20%
        assertEquals(1, progress1);

        Integer progress1Max = realtimeRewardService.calculateProgress(120, 300); // 40%
        assertEquals(1, progress1Max);

        // 测试进度2：50%-70%
        Integer progress2 = realtimeRewardService.calculateProgress(150, 300); // 50%
        assertEquals(2, progress2);

        Integer progress2Max = realtimeRewardService.calculateProgress(210, 300); // 70%
        assertEquals(2, progress2Max);

        // 测试进度3：80%-120%
        Integer progress3 = realtimeRewardService.calculateProgress(240, 300); // 80%
        assertEquals(3, progress3);

        Integer progress3Max = realtimeRewardService.calculateProgress(360, 300); // 120%
        assertEquals(3, progress3Max);

        // 测试无效进度
        Integer progressInvalid = realtimeRewardService.calculateProgress(30, 300); // 10%
        assertNull(progressInvalid);
    }

    @Test
    void testCanReceiveReward() {
        // 模拟没有现有记录，可以获得奖励
        when(maizeRecordBuilder.findMaizeRecordByCompositeKey(
                testCustomerId, "watch", "youtube", testVideoId, testSessionId, 1))
                .thenReturn(null);

        boolean canReceive = realtimeRewardService.canReceiveReward(
                testCustomerId, testSessionId, testVideoId, 1);
        assertTrue(canReceive);

        // 模拟已有记录，不能重复获得奖励
        MaizeRecord existingRecord = new MaizeRecord();
        when(maizeRecordBuilder.findMaizeRecordByCompositeKey(
                testCustomerId, "watch", "youtube", testVideoId, testSessionId, 2))
                .thenReturn(existingRecord);

        boolean cannotReceive = realtimeRewardService.canReceiveReward(
                testCustomerId, testSessionId, testVideoId, 2);
        assertFalse(cannotReceive);
    }

    @Test
    void testGenerateReward() {
        // 准备测试数据
        YouTubeRewardRules rewardRules = createTestRewardRules();
        when(base62Encoding.encode(anyLong())).thenReturn("ABC123", "DEF456");
        when(maizeRecordBuilder.save(any(MaizeRecord.class))).thenReturn(true);

        // 执行测试
        RealtimeRewardService.RewardGenerationResult result = realtimeRewardService.generateReward(
                testCustomerId, testSessionId, testVideoId, 1, 0.8);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getRewardCode());
        assertNotNull(result.getRewardScore());
        assertEquals("BRONZE", result.getRewardLevel());
        assertTrue(result.getRewardScore() >= 50L && result.getRewardScore() <= 100L);

        // 验证保存操作被调用
        verify(maizeRecordBuilder).save(any(MaizeRecord.class));
    }

    @Test
    void testProcessRealtimeEventsSuccess() {
        // 准备模拟数据
        when(playbackAnalysisService.calculateEffectiveWatchTime(testEvents)).thenReturn(180);
        when(trustScoreCalculationService.calculateTrustScore(testEvents, 300)).thenReturn(0.8);
        when(maizeRecordBuilder.findMaizeRecordByCompositeKey(anyString(), anyString(), anyString(), 
                anyString(), anyString(), anyInt())).thenReturn(null);
        when(base62Encoding.encode(anyLong())).thenReturn("REWARD123");
        when(maizeRecordBuilder.save(any(MaizeRecord.class))).thenReturn(true);

        // 模拟AsyncTool
        AsyncTool.TaskOrchestrator mockOrchestrator = mock(AsyncTool.TaskOrchestrator.class);
        when(asyncTool.createOrchestrator()).thenReturn(mockOrchestrator);

        // 执行测试
        RealtimeRewardService.RealtimeProcessingResult result = realtimeRewardService.processRealtimeEvents(
                testSessionId, testCustomerId, testVideoId, testEvents, 300);

        // 验证结果
        assertNotNull(result);
        // 注意：由于使用了异步处理，这里的测试需要根据实际实现调整
    }

    @Test
    void testGetYouTubeRewardRules() {
        // 测试获取默认规则
        YouTubeRewardRules rules = realtimeRewardService.getYouTubeRewardRules(testVideoId);
        
        assertNotNull(rules);
        assertEquals("YouTube", rules.getType());
        assertNotNull(rules.getProgressRewards());
        assertEquals(3, rules.getProgressRewards().size());

        // 验证进度1配置
        YouTubeRewardRules.ProgressRewardConfig progress1Config = rules.getProgressRewardConfig(1);
        assertNotNull(progress1Config);
        assertEquals(1, progress1Config.getProgress());
        assertEquals(0.2, progress1Config.getMinWatchPercentage());
        assertEquals(0.4, progress1Config.getMaxWatchPercentage());
        assertEquals("BRONZE", progress1Config.getRewardScoreRange().getRewardLevel());
    }

    @Test
    void testRewardRulesCalculateProgress() {
        YouTubeRewardRules rules = createTestRewardRules();

        // 测试各个进度区间
        assertEquals(1, rules.calculateProgress(0.3)); // 30% -> 进度1
        assertEquals(2, rules.calculateProgress(0.6)); // 60% -> 进度2
        assertEquals(3, rules.calculateProgress(0.9)); // 90% -> 进度3
        assertNull(rules.calculateProgress(0.1)); // 10% -> 无进度
    }

    /**
     * 创建测试用的SessionEvent列表
     */
    private List<SessionEvent> createTestSessionEvents() {
        SessionEvent playEvent = SessionEvent.builder()
                .id("event-1")
                .eventType("PLAYING")
                .clientTimestamp(System.currentTimeMillis())
                .eventData(createEventData("PLAYING", 120.0, 1.0))
                .build();

        SessionEvent pauseEvent = SessionEvent.builder()
                .id("event-2")
                .eventType("PAUSED")
                .clientTimestamp(System.currentTimeMillis() + 1000)
                .eventData(createEventData("PAUSED", 180.0, null))
                .build();

        SessionEvent focusLostEvent = SessionEvent.builder()
                .id("event-3")
                .eventType("FOCUS_LOST")
                .clientTimestamp(System.currentTimeMillis() + 2000)
                .eventData(createFocusEventData(false, true))
                .build();

        return Arrays.asList(playEvent, pauseEvent, focusLostEvent);
    }

    /**
     * 创建事件数据
     */
    private Map<String, Object> createEventData(String state, Double currentTime, Double playbackRate) {
        Map<String, Object> data = new HashMap<>();
        data.put("newState", state);
        data.put("currentTime", currentTime);
        if (playbackRate != null) {
            data.put("playbackRate", playbackRate);
        }
        return data;
    }

    /**
     * 创建焦点事件数据
     */
    private Map<String, Object> createFocusEventData(Boolean tabActive, Boolean windowFocused) {
        Map<String, Object> data = new HashMap<>();
        data.put("tabActive", tabActive);
        data.put("windowFocused", windowFocused);
        return data;
    }

    /**
     * 创建测试用的YouTube奖励规则
     */
    private YouTubeRewardRules createTestRewardRules() {
        List<YouTubeRewardRules.ProgressRewardConfig> progressRewards = Arrays.asList(
                YouTubeRewardRules.ProgressRewardConfig.builder()
                        .progress(1)
                        .progressName("初级观看")
                        .minWatchPercentage(0.2)
                        .maxWatchPercentage(0.4)
                        .rewardScoreRange(YouTubeRewardRules.RewardScoreRange.builder()
                                .minScore(50L)
                                .maxScore(100L)
                                .baseScore(75L)
                                .rewardLevel("BRONZE")
                                .build())
                        .minTrustScore(0.3)
                        .enabled(true)
                        .build(),
                YouTubeRewardRules.ProgressRewardConfig.builder()
                        .progress(2)
                        .progressName("中级观看")
                        .minWatchPercentage(0.5)
                        .maxWatchPercentage(0.7)
                        .rewardScoreRange(YouTubeRewardRules.RewardScoreRange.builder()
                                .minScore(100L)
                                .maxScore(200L)
                                .baseScore(150L)
                                .rewardLevel("SILVER")
                                .build())
                        .minTrustScore(0.5)
                        .enabled(true)
                        .build(),
                YouTubeRewardRules.ProgressRewardConfig.builder()
                        .progress(3)
                        .progressName("高级观看")
                        .minWatchPercentage(0.8)
                        .maxWatchPercentage(1.2)
                        .rewardScoreRange(YouTubeRewardRules.RewardScoreRange.builder()
                                .minScore(200L)
                                .maxScore(400L)
                                .baseScore(300L)
                                .rewardLevel("GOLD")
                                .build())
                        .minTrustScore(0.7)
                        .enabled(true)
                        .build()
        );

        return YouTubeRewardRules.builder()
                .progressRewards(progressRewards)
                .build();
    }
}
